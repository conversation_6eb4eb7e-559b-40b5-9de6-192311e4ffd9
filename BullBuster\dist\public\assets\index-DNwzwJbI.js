var Mr=t=>{throw TypeError(t)};var Ks=(t,e,s)=>e.has(t)||Mr("Cannot "+s);var p=(t,e,s)=>(Ks(t,e,"read from private field"),s?s.call(t):e.get(t)),j=(t,e,s)=>e.has(t)?Mr("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),P=(t,e,s,n)=>(Ks(t,e,"write to private field"),n?n.call(t,s):e.set(t,s),s),D=(t,e,s)=>(Ks(t,e,"access private method"),s);var fs=(t,e,s,n)=>({set _(r){P(t,e,r,s)},get _(){return p(t,e,n)}});import{j as h,V as wo,R as To,A as Po,C as So,T as Co,D as Ao,P as sc,c as nc,a as jo,u as Ro,b as rc,d as ic,e as ht,f as oc,g as ac,h as lc,i as cc,k as uc,l as dc}from"./ui-7t35Orsm.js";import{a as hc,r as g,c as fc}from"./vendor-CX2mysxk.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))n(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function s(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(r){if(r.ep)return;r.ep=!0;const o=s(r);fetch(r.href,o)}})();var Eo,kr=hc;Eo=kr.createRoot,kr.hydrateRoot;function pc(t,e){if(t instanceof RegExp)return{keys:!1,pattern:t};var s,n,r,o,i=[],a="",l=t.split("/");for(l[0]||l.shift();r=l.shift();)s=r[0],s==="*"?(i.push(s),a+=r[1]==="?"?"(?:/(.*))?":"/(.*)"):s===":"?(n=r.indexOf("?",1),o=r.indexOf(".",1),i.push(r.substring(1,~n?n:~o?o:r.length)),a+=~n&&!~o?"(?:/([^/]+?))?":"/([^/]+?)",~o&&(a+=(~n?"?":"")+"\\"+r.substring(o))):a+="/"+r;return{keys:i,pattern:new RegExp("^"+a+(e?"(?=$|/)":"/?$"),"i")}}var Mo={exports:{}},ko={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dt=g;function mc(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var yc=typeof Object.is=="function"?Object.is:mc,gc=Dt.useState,vc=Dt.useEffect,xc=Dt.useLayoutEffect,bc=Dt.useDebugValue;function wc(t,e){var s=e(),n=gc({inst:{value:s,getSnapshot:e}}),r=n[0].inst,o=n[1];return xc(function(){r.value=s,r.getSnapshot=e,zs(r)&&o({inst:r})},[t,s,e]),vc(function(){return zs(r)&&o({inst:r}),t(function(){zs(r)&&o({inst:r})})},[t]),bc(s),s}function zs(t){var e=t.getSnapshot;t=t.value;try{var s=e();return!yc(t,s)}catch{return!0}}function Tc(t,e){return e()}var Pc=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Tc:wc;ko.useSyncExternalStore=Dt.useSyncExternalStore!==void 0?Dt.useSyncExternalStore:Pc;Mo.exports=ko;var Sc=Mo.exports;const Cc=fc.useInsertionEffect,Ac=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",jc=Ac?g.useLayoutEffect:g.useEffect,Rc=Cc||jc,No=t=>{const e=g.useRef([t,(...s)=>e[0](...s)]).current;return Rc(()=>{e[0]=t}),e[1]},Ec="popstate",zn="pushState",Hn="replaceState",Mc="hashchange",Nr=[Ec,zn,Hn,Mc],kc=t=>{for(const e of Nr)addEventListener(e,t);return()=>{for(const e of Nr)removeEventListener(e,t)}},Do=(t,e)=>Sc.useSyncExternalStore(kc,t,e),Nc=()=>location.search,Dc=({ssrSearch:t=""}={})=>Do(Nc,()=>t),Dr=()=>location.pathname,Vc=({ssrPath:t}={})=>Do(Dr,t?()=>t:Dr),Oc=(t,{replace:e=!1,state:s=null}={})=>history[e?Hn:zn](s,"",t),Lc=(t={})=>[Vc(t),Oc],Vr=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Vr]>"u"){for(const t of[zn,Hn]){const e=history[t];history[t]=function(){const s=e.apply(this,arguments),n=new Event(t);return n.arguments=arguments,dispatchEvent(n),s}}Object.defineProperty(window,Vr,{value:!0})}const Ic=(t,e)=>e.toLowerCase().indexOf(t.toLowerCase())?"~"+e:e.slice(t.length)||"/",Vo=(t="")=>t==="/"?"":t,Fc=(t,e)=>t[0]==="~"?t.slice(1):Vo(e)+t,Bc=(t="",e)=>Ic(Or(Vo(t)),Or(e)),Or=t=>{try{return decodeURI(t)}catch{return t}},Oo={hook:Lc,searchHook:Dc,parser:pc,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:t=>t},Lo=g.createContext(Oo),Ds=()=>g.useContext(Lo),Io={},Fo=g.createContext(Io),Uc=()=>g.useContext(Fo),Gn=t=>{const[e,s]=t.hook(t);return[Bc(t.base,e),No((n,r)=>s(Fc(n,t.base),r))]},Bo=(t,e,s,n)=>{const{pattern:r,keys:o}=e instanceof RegExp?{keys:!1,pattern:e}:t(e||"*",n),i=r.exec(s)||[],[a,...l]=i;return a!==void 0?[!0,(()=>{const c=o!==!1?Object.fromEntries(o.map((d,f)=>[d,l[f]])):i.groups;let u={...l};return c&&Object.assign(u,c),u})(),...n?[a]:[]]:[!1,null]},_c=({children:t,...e})=>{var u,d;const s=Ds(),n=e.hook?Oo:s;let r=n;const[o,i]=((u=e.ssrPath)==null?void 0:u.split("?"))??[];i&&(e.ssrSearch=i,e.ssrPath=o),e.hrefs=e.hrefs??((d=e.hook)==null?void 0:d.hrefs);let a=g.useRef({}),l=a.current,c=l;for(let f in n){const m=f==="base"?n[f]+(e[f]||""):e[f]||n[f];l===c&&m!==c[f]&&(a.current=c={...c}),c[f]=m,m!==n[f]&&(r=c)}return g.createElement(Lo.Provider,{value:r,children:t})},Lr=({children:t,component:e},s)=>e?g.createElement(e,{params:s}):typeof t=="function"?t(s):t,Kc=t=>{let e=g.useRef(Io),s=e.current;for(const n in t)t[n]!==s[n]&&(s=t);return Object.keys(t).length===0&&(s=t),e.current=s},Ir=({path:t,nest:e,match:s,...n})=>{const r=Ds(),[o]=Gn(r),[i,a,l]=s??Bo(r.parser,t,o,e),c=Kc({...Uc(),...a});if(!i)return null;const u=l?g.createElement(_c,{base:l},Lr(n,c)):Lr(n,c);return g.createElement(Fo.Provider,{value:c,children:u})};g.forwardRef((t,e)=>{const s=Ds(),[n,r]=Gn(s),{to:o="",href:i=o,onClick:a,asChild:l,children:c,className:u,replace:d,state:f,...m}=t,x=No(v=>{v.ctrlKey||v.metaKey||v.altKey||v.shiftKey||v.button!==0||(a==null||a(v),v.defaultPrevented||(v.preventDefault(),r(i,t)))}),y=s.hrefs(i[0]==="~"?i.slice(1):s.base+i,s);return l&&g.isValidElement(c)?g.cloneElement(c,{onClick:x,href:y}):g.createElement("a",{...m,onClick:x,href:y,className:u!=null&&u.call?u(n===i):u,children:c,ref:e})});const Uo=t=>Array.isArray(t)?t.flatMap(e=>Uo(e&&e.type===g.Fragment?e.props.children:e)):[t],zc=({children:t,location:e})=>{const s=Ds(),[n]=Gn(s);for(const r of Uo(t)){let o=0;if(g.isValidElement(r)&&(o=Bo(s.parser,r.props.path,e||n,r.props.nest))[0])return g.cloneElement(r,{match:o})}return null};var Ft=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},lt=typeof window>"u"||"Deno"in globalThis;function de(){}function Hc(t,e){return typeof t=="function"?t(e):t}function cn(t){return typeof t=="number"&&t>=0&&t!==1/0}function _o(t,e){return Math.max(t+(e||0)-Date.now(),0)}function vt(t,e){return typeof t=="function"?t(e):t}function pe(t,e){return typeof t=="function"?t(e):t}function Fr(t,e){const{type:s="all",exact:n,fetchStatus:r,predicate:o,queryKey:i,stale:a}=t;if(i){if(n){if(e.queryHash!==qn(i,e.options))return!1}else if(!Zt(e.queryKey,i))return!1}if(s!=="all"){const l=e.isActive();if(s==="active"&&!l||s==="inactive"&&l)return!1}return!(typeof a=="boolean"&&e.isStale()!==a||r&&r!==e.state.fetchStatus||o&&!o(e))}function Br(t,e){const{exact:s,status:n,predicate:r,mutationKey:o}=t;if(o){if(!e.options.mutationKey)return!1;if(s){if(ct(e.options.mutationKey)!==ct(o))return!1}else if(!Zt(e.options.mutationKey,o))return!1}return!(n&&e.state.status!==n||r&&!r(e))}function qn(t,e){return((e==null?void 0:e.queryKeyHashFn)||ct)(t)}function ct(t){return JSON.stringify(t,(e,s)=>un(s)?Object.keys(s).sort().reduce((n,r)=>(n[r]=s[r],n),{}):s)}function Zt(t,e){return t===e?!0:typeof t!=typeof e?!1:t&&e&&typeof t=="object"&&typeof e=="object"?!Object.keys(e).some(s=>!Zt(t[s],e[s])):!1}function Ko(t,e){if(t===e)return t;const s=Ur(t)&&Ur(e);if(s||un(t)&&un(e)){const n=s?t:Object.keys(t),r=n.length,o=s?e:Object.keys(e),i=o.length,a=s?[]:{};let l=0;for(let c=0;c<i;c++){const u=s?c:o[c];(!s&&n.includes(u)||s)&&t[u]===void 0&&e[u]===void 0?(a[u]=void 0,l++):(a[u]=Ko(t[u],e[u]),a[u]===t[u]&&t[u]!==void 0&&l++)}return r===i&&l===r?t:a}return e}function Ss(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function Ur(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function un(t){if(!_r(t))return!1;const e=t.constructor;if(e===void 0)return!0;const s=e.prototype;return!(!_r(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function _r(t){return Object.prototype.toString.call(t)==="[object Object]"}function Gc(t){return new Promise(e=>{setTimeout(e,t)})}function dn(t,e,s){return typeof s.structuralSharing=="function"?s.structuralSharing(t,e):s.structuralSharing!==!1?Ko(t,e):e}function qc(t,e,s=0){const n=[...t,e];return s&&n.length>s?n.slice(1):n}function $c(t,e,s=0){const n=[e,...t];return s&&n.length>s?n.slice(0,-1):n}var $n=Symbol();function zo(t,e){return!t.queryFn&&(e!=null&&e.initialPromise)?()=>e.initialPromise:!t.queryFn||t.queryFn===$n?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}var et,Oe,bt,uo,Wc=(uo=class extends Ft{constructor(){super();j(this,et);j(this,Oe);j(this,bt);P(this,bt,e=>{if(!lt&&window.addEventListener){const s=()=>e();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){p(this,Oe)||this.setEventListener(p(this,bt))}onUnsubscribe(){var e;this.hasListeners()||((e=p(this,Oe))==null||e.call(this),P(this,Oe,void 0))}setEventListener(e){var s;P(this,bt,e),(s=p(this,Oe))==null||s.call(this),P(this,Oe,e(n=>{typeof n=="boolean"?this.setFocused(n):this.onFocus()}))}setFocused(e){p(this,et)!==e&&(P(this,et,e),this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach(s=>{s(e)})}isFocused(){var e;return typeof p(this,et)=="boolean"?p(this,et):((e=globalThis.document)==null?void 0:e.visibilityState)!=="hidden"}},et=new WeakMap,Oe=new WeakMap,bt=new WeakMap,uo),Wn=new Wc,wt,Le,Tt,ho,Qc=(ho=class extends Ft{constructor(){super();j(this,wt,!0);j(this,Le);j(this,Tt);P(this,Tt,e=>{if(!lt&&window.addEventListener){const s=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",n)}}})}onSubscribe(){p(this,Le)||this.setEventListener(p(this,Tt))}onUnsubscribe(){var e;this.hasListeners()||((e=p(this,Le))==null||e.call(this),P(this,Le,void 0))}setEventListener(e){var s;P(this,Tt,e),(s=p(this,Le))==null||s.call(this),P(this,Le,e(this.setOnline.bind(this)))}setOnline(e){p(this,wt)!==e&&(P(this,wt,e),this.listeners.forEach(n=>{n(e)}))}isOnline(){return p(this,wt)}},wt=new WeakMap,Le=new WeakMap,Tt=new WeakMap,ho),Cs=new Qc;function hn(){let t,e;const s=new Promise((r,o)=>{t=r,e=o});s.status="pending",s.catch(()=>{});function n(r){Object.assign(s,r),delete s.resolve,delete s.reject}return s.resolve=r=>{n({status:"fulfilled",value:r}),t(r)},s.reject=r=>{n({status:"rejected",reason:r}),e(r)},s}function Yc(t){return Math.min(1e3*2**t,3e4)}function Ho(t){return(t??"online")==="online"?Cs.isOnline():!0}var Go=class extends Error{constructor(t){super("CancelledError"),this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent}};function Hs(t){return t instanceof Go}function qo(t){let e=!1,s=0,n=!1,r;const o=hn(),i=y=>{var v;n||(f(new Go(y)),(v=t.abort)==null||v.call(t))},a=()=>{e=!0},l=()=>{e=!1},c=()=>Wn.isFocused()&&(t.networkMode==="always"||Cs.isOnline())&&t.canRun(),u=()=>Ho(t.networkMode)&&t.canRun(),d=y=>{var v;n||(n=!0,(v=t.onSuccess)==null||v.call(t,y),r==null||r(),o.resolve(y))},f=y=>{var v;n||(n=!0,(v=t.onError)==null||v.call(t,y),r==null||r(),o.reject(y))},m=()=>new Promise(y=>{var v;r=b=>{(n||c())&&y(b)},(v=t.onPause)==null||v.call(t)}).then(()=>{var y;r=void 0,n||(y=t.onContinue)==null||y.call(t)}),x=()=>{if(n)return;let y;const v=s===0?t.initialPromise:void 0;try{y=v??t.fn()}catch(b){y=Promise.reject(b)}Promise.resolve(y).then(d).catch(b=>{var k;if(n)return;const w=t.retry??(lt?0:3),T=t.retryDelay??Yc,A=typeof T=="function"?T(s,b):T,S=w===!0||typeof w=="number"&&s<w||typeof w=="function"&&w(s,b);if(e||!S){f(b);return}s++,(k=t.onFail)==null||k.call(t,s,b),Gc(A).then(()=>c()?void 0:m()).then(()=>{e?f(b):x()})})};return{promise:o,cancel:i,continue:()=>(r==null||r(),o),cancelRetry:a,continueRetry:l,canStart:u,start:()=>(u()?x():m().then(x),o)}}function Xc(){let t=[],e=0,s=a=>{a()},n=a=>{a()},r=a=>setTimeout(a,0);const o=a=>{e?t.push(a):r(()=>{s(a)})},i=()=>{const a=t;t=[],a.length&&r(()=>{n(()=>{a.forEach(l=>{s(l)})})})};return{batch:a=>{let l;e++;try{l=a()}finally{e--,e||i()}return l},batchCalls:a=>(...l)=>{o(()=>{a(...l)})},schedule:o,setNotifyFunction:a=>{s=a},setBatchNotifyFunction:a=>{n=a},setScheduler:a=>{r=a}}}var q=Xc(),tt,fo,$o=(fo=class{constructor(){j(this,tt)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),cn(this.gcTime)&&P(this,tt,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(lt?1/0:5*60*1e3))}clearGcTimeout(){p(this,tt)&&(clearTimeout(p(this,tt)),P(this,tt,void 0))}},tt=new WeakMap,fo),Pt,St,ue,Y,ns,st,fe,Se,po,Zc=(po=class extends $o{constructor(e){super();j(this,fe);j(this,Pt);j(this,St);j(this,ue);j(this,Y);j(this,ns);j(this,st);P(this,st,!1),P(this,ns,e.defaultOptions),this.setOptions(e.options),this.observers=[],P(this,ue,e.cache),this.queryKey=e.queryKey,this.queryHash=e.queryHash,P(this,Pt,Jc(this.options)),this.state=e.state??p(this,Pt),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var e;return(e=p(this,Y))==null?void 0:e.promise}setOptions(e){this.options={...p(this,ns),...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&p(this,ue).remove(this)}setData(e,s){const n=dn(this.state.data,e,this.options);return D(this,fe,Se).call(this,{data:n,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),n}setState(e,s){D(this,fe,Se).call(this,{type:"setState",state:e,setStateOptions:s})}cancel(e){var n,r;const s=(n=p(this,Y))==null?void 0:n.promise;return(r=p(this,Y))==null||r.cancel(e),s?s.then(de).catch(de):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(p(this,Pt))}isActive(){return this.observers.some(e=>pe(e.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===$n||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(e=0){return this.state.isInvalidated||this.state.data===void 0||!_o(this.state.dataUpdatedAt,e)}onFocus(){var s;const e=this.observers.find(n=>n.shouldFetchOnWindowFocus());e==null||e.refetch({cancelRefetch:!1}),(s=p(this,Y))==null||s.continue()}onOnline(){var s;const e=this.observers.find(n=>n.shouldFetchOnReconnect());e==null||e.refetch({cancelRefetch:!1}),(s=p(this,Y))==null||s.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),p(this,ue).notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(s=>s!==e),this.observers.length||(p(this,Y)&&(p(this,st)?p(this,Y).cancel({revert:!0}):p(this,Y).cancelRetry()),this.scheduleGc()),p(this,ue).notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||D(this,fe,Se).call(this,{type:"invalidate"})}fetch(e,s){var l,c,u;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(p(this,Y))return p(this,Y).continueRetry(),p(this,Y).promise}if(e&&this.setOptions(e),!this.options.queryFn){const d=this.observers.find(f=>f.options.queryFn);d&&this.setOptions(d.options)}const n=new AbortController,r=d=>{Object.defineProperty(d,"signal",{enumerable:!0,get:()=>(P(this,st,!0),n.signal)})},o=()=>{const d=zo(this.options,s),f={queryKey:this.queryKey,meta:this.meta};return r(f),P(this,st,!1),this.options.persister?this.options.persister(d,f,this):d(f)},i={fetchOptions:s,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:o};r(i),(l=this.options.behavior)==null||l.onFetch(i,this),P(this,St,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=i.fetchOptions)==null?void 0:c.meta))&&D(this,fe,Se).call(this,{type:"fetch",meta:(u=i.fetchOptions)==null?void 0:u.meta});const a=d=>{var f,m,x,y;Hs(d)&&d.silent||D(this,fe,Se).call(this,{type:"error",error:d}),Hs(d)||((m=(f=p(this,ue).config).onError)==null||m.call(f,d,this),(y=(x=p(this,ue).config).onSettled)==null||y.call(x,this.state.data,d,this)),this.scheduleGc()};return P(this,Y,qo({initialPromise:s==null?void 0:s.initialPromise,fn:i.fetchFn,abort:n.abort.bind(n),onSuccess:d=>{var f,m,x,y;if(d===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(d)}catch(v){a(v);return}(m=(f=p(this,ue).config).onSuccess)==null||m.call(f,d,this),(y=(x=p(this,ue).config).onSettled)==null||y.call(x,d,this.state.error,this),this.scheduleGc()},onError:a,onFail:(d,f)=>{D(this,fe,Se).call(this,{type:"failed",failureCount:d,error:f})},onPause:()=>{D(this,fe,Se).call(this,{type:"pause"})},onContinue:()=>{D(this,fe,Se).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),p(this,Y).start()}},Pt=new WeakMap,St=new WeakMap,ue=new WeakMap,Y=new WeakMap,ns=new WeakMap,st=new WeakMap,fe=new WeakSet,Se=function(e){const s=n=>{switch(e.type){case"failed":return{...n,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...n,fetchStatus:"paused"};case"continue":return{...n,fetchStatus:"fetching"};case"fetch":return{...n,...Wo(n.data,this.options),fetchMeta:e.meta??null};case"success":return{...n,data:e.data,dataUpdateCount:n.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return Hs(r)&&r.revert&&p(this,St)?{...p(this,St),fetchStatus:"idle"}:{...n,error:r,errorUpdateCount:n.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:n.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...n,isInvalidated:!0};case"setState":return{...n,...e.state}}};this.state=s(this.state),q.batch(()=>{this.observers.forEach(n=>{n.onQueryUpdate()}),p(this,ue).notify({query:this,type:"updated",action:e})})},po);function Wo(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ho(e.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function Jc(t){const e=typeof t.initialData=="function"?t.initialData():t.initialData,s=e!==void 0,n=s?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var me,mo,eu=(mo=class extends Ft{constructor(e={}){super();j(this,me);this.config=e,P(this,me,new Map)}build(e,s,n){const r=s.queryKey,o=s.queryHash??qn(r,s);let i=this.get(o);return i||(i=new Zc({cache:this,queryKey:r,queryHash:o,options:e.defaultQueryOptions(s),state:n,defaultOptions:e.getQueryDefaults(r)}),this.add(i)),i}add(e){p(this,me).has(e.queryHash)||(p(this,me).set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const s=p(this,me).get(e.queryHash);s&&(e.destroy(),s===e&&p(this,me).delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){q.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return p(this,me).get(e)}getAll(){return[...p(this,me).values()]}find(e){const s={exact:!0,...e};return this.getAll().find(n=>Fr(s,n))}findAll(e={}){const s=this.getAll();return Object.keys(e).length>0?s.filter(n=>Fr(e,n)):s}notify(e){q.batch(()=>{this.listeners.forEach(s=>{s(e)})})}onFocus(){q.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){q.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},me=new WeakMap,mo),ye,ee,nt,ge,De,yo,tu=(yo=class extends $o{constructor(e){super();j(this,ge);j(this,ye);j(this,ee);j(this,nt);this.mutationId=e.mutationId,P(this,ee,e.mutationCache),P(this,ye,[]),this.state=e.state||Qo(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){p(this,ye).includes(e)||(p(this,ye).push(e),this.clearGcTimeout(),p(this,ee).notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){P(this,ye,p(this,ye).filter(s=>s!==e)),this.scheduleGc(),p(this,ee).notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){p(this,ye).length||(this.state.status==="pending"?this.scheduleGc():p(this,ee).remove(this))}continue(){var e;return((e=p(this,nt))==null?void 0:e.continue())??this.execute(this.state.variables)}async execute(e){var r,o,i,a,l,c,u,d,f,m,x,y,v,b,w,T,A,S,k,V;P(this,nt,qo({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(C,M)=>{D(this,ge,De).call(this,{type:"failed",failureCount:C,error:M})},onPause:()=>{D(this,ge,De).call(this,{type:"pause"})},onContinue:()=>{D(this,ge,De).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>p(this,ee).canRun(this)}));const s=this.state.status==="pending",n=!p(this,nt).canStart();try{if(!s){D(this,ge,De).call(this,{type:"pending",variables:e,isPaused:n}),await((o=(r=p(this,ee).config).onMutate)==null?void 0:o.call(r,e,this));const M=await((a=(i=this.options).onMutate)==null?void 0:a.call(i,e));M!==this.state.context&&D(this,ge,De).call(this,{type:"pending",context:M,variables:e,isPaused:n})}const C=await p(this,nt).start();return await((c=(l=p(this,ee).config).onSuccess)==null?void 0:c.call(l,C,e,this.state.context,this)),await((d=(u=this.options).onSuccess)==null?void 0:d.call(u,C,e,this.state.context)),await((m=(f=p(this,ee).config).onSettled)==null?void 0:m.call(f,C,null,this.state.variables,this.state.context,this)),await((y=(x=this.options).onSettled)==null?void 0:y.call(x,C,null,e,this.state.context)),D(this,ge,De).call(this,{type:"success",data:C}),C}catch(C){try{throw await((b=(v=p(this,ee).config).onError)==null?void 0:b.call(v,C,e,this.state.context,this)),await((T=(w=this.options).onError)==null?void 0:T.call(w,C,e,this.state.context)),await((S=(A=p(this,ee).config).onSettled)==null?void 0:S.call(A,void 0,C,this.state.variables,this.state.context,this)),await((V=(k=this.options).onSettled)==null?void 0:V.call(k,void 0,C,e,this.state.context)),C}finally{D(this,ge,De).call(this,{type:"error",error:C})}}finally{p(this,ee).runNext(this)}}},ye=new WeakMap,ee=new WeakMap,nt=new WeakMap,ge=new WeakSet,De=function(e){const s=n=>{switch(e.type){case"failed":return{...n,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...n,isPaused:!0};case"continue":return{...n,isPaused:!1};case"pending":return{...n,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...n,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...n,data:void 0,error:e.error,failureCount:n.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}};this.state=s(this.state),q.batch(()=>{p(this,ye).forEach(n=>{n.onMutationUpdate(e)}),p(this,ee).notify({mutation:this,type:"updated",action:e})})},yo);function Qo(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var ie,rs,go,su=(go=class extends Ft{constructor(e={}){super();j(this,ie);j(this,rs);this.config=e,P(this,ie,new Map),P(this,rs,Date.now())}build(e,s,n){const r=new tu({mutationCache:this,mutationId:++fs(this,rs)._,options:e.defaultMutationOptions(s),state:n});return this.add(r),r}add(e){const s=ps(e),n=p(this,ie).get(s)??[];n.push(e),p(this,ie).set(s,n),this.notify({type:"added",mutation:e})}remove(e){var n;const s=ps(e);if(p(this,ie).has(s)){const r=(n=p(this,ie).get(s))==null?void 0:n.filter(o=>o!==e);r&&(r.length===0?p(this,ie).delete(s):p(this,ie).set(s,r))}this.notify({type:"removed",mutation:e})}canRun(e){var n;const s=(n=p(this,ie).get(ps(e)))==null?void 0:n.find(r=>r.state.status==="pending");return!s||s===e}runNext(e){var n;const s=(n=p(this,ie).get(ps(e)))==null?void 0:n.find(r=>r!==e&&r.state.isPaused);return(s==null?void 0:s.continue())??Promise.resolve()}clear(){q.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}getAll(){return[...p(this,ie).values()].flat()}find(e){const s={exact:!0,...e};return this.getAll().find(n=>Br(s,n))}findAll(e={}){return this.getAll().filter(s=>Br(e,s))}notify(e){q.batch(()=>{this.listeners.forEach(s=>{s(e)})})}resumePausedMutations(){const e=this.getAll().filter(s=>s.state.isPaused);return q.batch(()=>Promise.all(e.map(s=>s.continue().catch(de))))}},ie=new WeakMap,rs=new WeakMap,go);function ps(t){var e;return((e=t.options.scope)==null?void 0:e.id)??String(t.mutationId)}function Kr(t){return{onFetch:(e,s)=>{var u,d,f,m,x;const n=e.options,r=(f=(d=(u=e.fetchOptions)==null?void 0:u.meta)==null?void 0:d.fetchMore)==null?void 0:f.direction,o=((m=e.state.data)==null?void 0:m.pages)||[],i=((x=e.state.data)==null?void 0:x.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const c=async()=>{let y=!1;const v=T=>{Object.defineProperty(T,"signal",{enumerable:!0,get:()=>(e.signal.aborted?y=!0:e.signal.addEventListener("abort",()=>{y=!0}),e.signal)})},b=zo(e.options,e.fetchOptions),w=async(T,A,S)=>{if(y)return Promise.reject();if(A==null&&T.pages.length)return Promise.resolve(T);const k={queryKey:e.queryKey,pageParam:A,direction:S?"backward":"forward",meta:e.options.meta};v(k);const V=await b(k),{maxPages:C}=e.options,M=S?$c:qc;return{pages:M(T.pages,V,C),pageParams:M(T.pageParams,A,C)}};if(r&&o.length){const T=r==="backward",A=T?nu:zr,S={pages:o,pageParams:i},k=A(n,S);a=await w(S,k,T)}else{const T=t??o.length;do{const A=l===0?i[0]??n.initialPageParam:zr(n,a);if(l>0&&A==null)break;a=await w(a,A),l++}while(l<T)}return a};e.options.persister?e.fetchFn=()=>{var y,v;return(v=(y=e.options).persister)==null?void 0:v.call(y,c,{queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s)}:e.fetchFn=c}}}function zr(t,{pages:e,pageParams:s}){const n=e.length-1;return e.length>0?t.getNextPageParam(e[n],e,s[n],s):void 0}function nu(t,{pages:e,pageParams:s}){var n;return e.length>0?(n=t.getPreviousPageParam)==null?void 0:n.call(t,e[0],e,s[0],s):void 0}var K,Ie,Fe,Ct,At,Be,jt,Rt,vo,ru=(vo=class{constructor(t={}){j(this,K);j(this,Ie);j(this,Fe);j(this,Ct);j(this,At);j(this,Be);j(this,jt);j(this,Rt);P(this,K,t.queryCache||new eu),P(this,Ie,t.mutationCache||new su),P(this,Fe,t.defaultOptions||{}),P(this,Ct,new Map),P(this,At,new Map),P(this,Be,0)}mount(){fs(this,Be)._++,p(this,Be)===1&&(P(this,jt,Wn.subscribe(async t=>{t&&(await this.resumePausedMutations(),p(this,K).onFocus())})),P(this,Rt,Cs.subscribe(async t=>{t&&(await this.resumePausedMutations(),p(this,K).onOnline())})))}unmount(){var t,e;fs(this,Be)._--,p(this,Be)===0&&((t=p(this,jt))==null||t.call(this),P(this,jt,void 0),(e=p(this,Rt))==null||e.call(this),P(this,Rt,void 0))}isFetching(t){return p(this,K).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return p(this,Ie).findAll({...t,status:"pending"}).length}getQueryData(t){var s;const e=this.defaultQueryOptions({queryKey:t});return(s=p(this,K).get(e.queryHash))==null?void 0:s.state.data}ensureQueryData(t){const e=this.getQueryData(t.queryKey);if(e===void 0)return this.fetchQuery(t);{const s=this.defaultQueryOptions(t),n=p(this,K).build(this,s);return t.revalidateIfStale&&n.isStaleByTime(vt(s.staleTime,n))&&this.prefetchQuery(s),Promise.resolve(e)}}getQueriesData(t){return p(this,K).findAll(t).map(({queryKey:e,state:s})=>{const n=s.data;return[e,n]})}setQueryData(t,e,s){const n=this.defaultQueryOptions({queryKey:t}),r=p(this,K).get(n.queryHash),o=r==null?void 0:r.state.data,i=Hc(e,o);if(i!==void 0)return p(this,K).build(this,n).setData(i,{...s,manual:!0})}setQueriesData(t,e,s){return q.batch(()=>p(this,K).findAll(t).map(({queryKey:n})=>[n,this.setQueryData(n,e,s)]))}getQueryState(t){var s;const e=this.defaultQueryOptions({queryKey:t});return(s=p(this,K).get(e.queryHash))==null?void 0:s.state}removeQueries(t){const e=p(this,K);q.batch(()=>{e.findAll(t).forEach(s=>{e.remove(s)})})}resetQueries(t,e){const s=p(this,K),n={type:"active",...t};return q.batch(()=>(s.findAll(t).forEach(r=>{r.reset()}),this.refetchQueries(n,e)))}cancelQueries(t={},e={}){const s={revert:!0,...e},n=q.batch(()=>p(this,K).findAll(t).map(r=>r.cancel(s)));return Promise.all(n).then(de).catch(de)}invalidateQueries(t={},e={}){return q.batch(()=>{if(p(this,K).findAll(t).forEach(n=>{n.invalidate()}),t.refetchType==="none")return Promise.resolve();const s={...t,type:t.refetchType??t.type??"active"};return this.refetchQueries(s,e)})}refetchQueries(t={},e){const s={...e,cancelRefetch:(e==null?void 0:e.cancelRefetch)??!0},n=q.batch(()=>p(this,K).findAll(t).filter(r=>!r.isDisabled()).map(r=>{let o=r.fetch(void 0,s);return s.throwOnError||(o=o.catch(de)),r.state.fetchStatus==="paused"?Promise.resolve():o}));return Promise.all(n).then(de)}fetchQuery(t){const e=this.defaultQueryOptions(t);e.retry===void 0&&(e.retry=!1);const s=p(this,K).build(this,e);return s.isStaleByTime(vt(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(de).catch(de)}fetchInfiniteQuery(t){return t.behavior=Kr(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(de).catch(de)}ensureInfiniteQueryData(t){return t.behavior=Kr(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return Cs.isOnline()?p(this,Ie).resumePausedMutations():Promise.resolve()}getQueryCache(){return p(this,K)}getMutationCache(){return p(this,Ie)}getDefaultOptions(){return p(this,Fe)}setDefaultOptions(t){P(this,Fe,t)}setQueryDefaults(t,e){p(this,Ct).set(ct(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...p(this,Ct).values()];let s={};return e.forEach(n=>{Zt(t,n.queryKey)&&(s={...s,...n.defaultOptions})}),s}setMutationDefaults(t,e){p(this,At).set(ct(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...p(this,At).values()];let s={};return e.forEach(n=>{Zt(t,n.mutationKey)&&(s={...s,...n.defaultOptions})}),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...p(this,Fe).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=qn(e.queryKey,e)),e.refetchOnReconnect===void 0&&(e.refetchOnReconnect=e.networkMode!=="always"),e.throwOnError===void 0&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.enabled!==!0&&e.queryFn===$n&&(e.enabled=!1),e}defaultMutationOptions(t){return t!=null&&t._defaulted?t:{...p(this,Fe).mutations,...(t==null?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){p(this,K).clear(),p(this,Ie).clear()}},K=new WeakMap,Ie=new WeakMap,Fe=new WeakMap,Ct=new WeakMap,At=new WeakMap,Be=new WeakMap,jt=new WeakMap,Rt=new WeakMap,vo),se,O,is,te,rt,Et,Ue,ve,os,Mt,kt,it,ot,_e,Nt,I,Gt,fn,pn,mn,yn,gn,vn,xn,Yo,xo,iu=(xo=class extends Ft{constructor(e,s){super();j(this,I);j(this,se);j(this,O);j(this,is);j(this,te);j(this,rt);j(this,Et);j(this,Ue);j(this,ve);j(this,os);j(this,Mt);j(this,kt);j(this,it);j(this,ot);j(this,_e);j(this,Nt,new Set);this.options=s,P(this,se,e),P(this,ve,null),P(this,Ue,hn()),this.options.experimental_prefetchInRender||p(this,Ue).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(p(this,O).addObserver(this),Hr(p(this,O),this.options)?D(this,I,Gt).call(this):this.updateResult(),D(this,I,yn).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return bn(p(this,O),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return bn(p(this,O),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,D(this,I,gn).call(this),D(this,I,vn).call(this),p(this,O).removeObserver(this)}setOptions(e,s){const n=this.options,r=p(this,O);if(this.options=p(this,se).defaultQueryOptions(e),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof pe(this.options.enabled,p(this,O))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");D(this,I,xn).call(this),p(this,O).setOptions(this.options),n._defaulted&&!Ss(this.options,n)&&p(this,se).getQueryCache().notify({type:"observerOptionsUpdated",query:p(this,O),observer:this});const o=this.hasListeners();o&&Gr(p(this,O),r,this.options,n)&&D(this,I,Gt).call(this),this.updateResult(s),o&&(p(this,O)!==r||pe(this.options.enabled,p(this,O))!==pe(n.enabled,p(this,O))||vt(this.options.staleTime,p(this,O))!==vt(n.staleTime,p(this,O)))&&D(this,I,fn).call(this);const i=D(this,I,pn).call(this);o&&(p(this,O)!==r||pe(this.options.enabled,p(this,O))!==pe(n.enabled,p(this,O))||i!==p(this,_e))&&D(this,I,mn).call(this,i)}getOptimisticResult(e){const s=p(this,se).getQueryCache().build(p(this,se),e),n=this.createResult(s,e);return au(this,n)&&(P(this,te,n),P(this,Et,this.options),P(this,rt,p(this,O).state)),n}getCurrentResult(){return p(this,te)}trackResult(e,s){const n={};return Object.keys(e).forEach(r=>{Object.defineProperty(n,r,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(r),s==null||s(r),e[r])})}),n}trackProp(e){p(this,Nt).add(e)}getCurrentQuery(){return p(this,O)}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const s=p(this,se).defaultQueryOptions(e),n=p(this,se).getQueryCache().build(p(this,se),s);return n.fetch().then(()=>this.createResult(n,s))}fetch(e){return D(this,I,Gt).call(this,{...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),p(this,te)))}createResult(e,s){var C;const n=p(this,O),r=this.options,o=p(this,te),i=p(this,rt),a=p(this,Et),c=e!==n?e.state:p(this,is),{state:u}=e;let d={...u},f=!1,m;if(s._optimisticResults){const M=this.hasListeners(),U=!M&&Hr(e,s),W=M&&Gr(e,n,s,r);(U||W)&&(d={...d,...Wo(u.data,e.options)}),s._optimisticResults==="isRestoring"&&(d.fetchStatus="idle")}let{error:x,errorUpdatedAt:y,status:v}=d;if(s.select&&d.data!==void 0)if(o&&d.data===(i==null?void 0:i.data)&&s.select===p(this,os))m=p(this,Mt);else try{P(this,os,s.select),m=s.select(d.data),m=dn(o==null?void 0:o.data,m,s),P(this,Mt,m),P(this,ve,null)}catch(M){P(this,ve,M)}else m=d.data;if(s.placeholderData!==void 0&&m===void 0&&v==="pending"){let M;if(o!=null&&o.isPlaceholderData&&s.placeholderData===(a==null?void 0:a.placeholderData))M=o.data;else if(M=typeof s.placeholderData=="function"?s.placeholderData((C=p(this,kt))==null?void 0:C.state.data,p(this,kt)):s.placeholderData,s.select&&M!==void 0)try{M=s.select(M),P(this,ve,null)}catch(U){P(this,ve,U)}M!==void 0&&(v="success",m=dn(o==null?void 0:o.data,M,s),f=!0)}p(this,ve)&&(x=p(this,ve),m=p(this,Mt),y=Date.now(),v="error");const b=d.fetchStatus==="fetching",w=v==="pending",T=v==="error",A=w&&b,S=m!==void 0,V={status:v,fetchStatus:d.fetchStatus,isPending:w,isSuccess:v==="success",isError:T,isInitialLoading:A,isLoading:A,data:m,dataUpdatedAt:d.dataUpdatedAt,error:x,errorUpdatedAt:y,failureCount:d.fetchFailureCount,failureReason:d.fetchFailureReason,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>c.dataUpdateCount||d.errorUpdateCount>c.errorUpdateCount,isFetching:b,isRefetching:b&&!w,isLoadingError:T&&!S,isPaused:d.fetchStatus==="paused",isPlaceholderData:f,isRefetchError:T&&S,isStale:Qn(e,s),refetch:this.refetch,promise:p(this,Ue)};if(this.options.experimental_prefetchInRender){const M=L=>{V.status==="error"?L.reject(V.error):V.data!==void 0&&L.resolve(V.data)},U=()=>{const L=P(this,Ue,V.promise=hn());M(L)},W=p(this,Ue);switch(W.status){case"pending":e.queryHash===n.queryHash&&M(W);break;case"fulfilled":(V.status==="error"||V.data!==W.value)&&U();break;case"rejected":(V.status!=="error"||V.error!==W.reason)&&U();break}}return V}updateResult(e){const s=p(this,te),n=this.createResult(p(this,O),this.options);if(P(this,rt,p(this,O).state),P(this,Et,this.options),p(this,rt).data!==void 0&&P(this,kt,p(this,O)),Ss(n,s))return;P(this,te,n);const r={},o=()=>{if(!s)return!0;const{notifyOnChangeProps:i}=this.options,a=typeof i=="function"?i():i;if(a==="all"||!a&&!p(this,Nt).size)return!0;const l=new Set(a??p(this,Nt));return this.options.throwOnError&&l.add("error"),Object.keys(p(this,te)).some(c=>{const u=c;return p(this,te)[u]!==s[u]&&l.has(u)})};(e==null?void 0:e.listeners)!==!1&&o()&&(r.listeners=!0),D(this,I,Yo).call(this,{...r,...e})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&D(this,I,yn).call(this)}},se=new WeakMap,O=new WeakMap,is=new WeakMap,te=new WeakMap,rt=new WeakMap,Et=new WeakMap,Ue=new WeakMap,ve=new WeakMap,os=new WeakMap,Mt=new WeakMap,kt=new WeakMap,it=new WeakMap,ot=new WeakMap,_e=new WeakMap,Nt=new WeakMap,I=new WeakSet,Gt=function(e){D(this,I,xn).call(this);let s=p(this,O).fetch(this.options,e);return e!=null&&e.throwOnError||(s=s.catch(de)),s},fn=function(){D(this,I,gn).call(this);const e=vt(this.options.staleTime,p(this,O));if(lt||p(this,te).isStale||!cn(e))return;const n=_o(p(this,te).dataUpdatedAt,e)+1;P(this,it,setTimeout(()=>{p(this,te).isStale||this.updateResult()},n))},pn=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(p(this,O)):this.options.refetchInterval)??!1},mn=function(e){D(this,I,vn).call(this),P(this,_e,e),!(lt||pe(this.options.enabled,p(this,O))===!1||!cn(p(this,_e))||p(this,_e)===0)&&P(this,ot,setInterval(()=>{(this.options.refetchIntervalInBackground||Wn.isFocused())&&D(this,I,Gt).call(this)},p(this,_e)))},yn=function(){D(this,I,fn).call(this),D(this,I,mn).call(this,D(this,I,pn).call(this))},gn=function(){p(this,it)&&(clearTimeout(p(this,it)),P(this,it,void 0))},vn=function(){p(this,ot)&&(clearInterval(p(this,ot)),P(this,ot,void 0))},xn=function(){const e=p(this,se).getQueryCache().build(p(this,se),this.options);if(e===p(this,O))return;const s=p(this,O);P(this,O,e),P(this,is,e.state),this.hasListeners()&&(s==null||s.removeObserver(this),e.addObserver(this))},Yo=function(e){q.batch(()=>{e.listeners&&this.listeners.forEach(s=>{s(p(this,te))}),p(this,se).getQueryCache().notify({query:p(this,O),type:"observerResultsUpdated"})})},xo);function ou(t,e){return pe(e.enabled,t)!==!1&&t.state.data===void 0&&!(t.state.status==="error"&&e.retryOnMount===!1)}function Hr(t,e){return ou(t,e)||t.state.data!==void 0&&bn(t,e,e.refetchOnMount)}function bn(t,e,s){if(pe(e.enabled,t)!==!1){const n=typeof s=="function"?s(t):s;return n==="always"||n!==!1&&Qn(t,e)}return!1}function Gr(t,e,s,n){return(t!==e||pe(n.enabled,t)===!1)&&(!s.suspense||t.state.status!=="error")&&Qn(t,s)}function Qn(t,e){return pe(e.enabled,t)!==!1&&t.isStaleByTime(vt(e.staleTime,t))}function au(t,e){return!Ss(t.getCurrentResult(),e)}var Ke,ze,ne,Ce,Ee,vs,wn,bo,lu=(bo=class extends Ft{constructor(s,n){super();j(this,Ee);j(this,Ke);j(this,ze);j(this,ne);j(this,Ce);P(this,Ke,s),this.setOptions(n),this.bindMethods(),D(this,Ee,vs).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(s){var r;const n=this.options;this.options=p(this,Ke).defaultMutationOptions(s),Ss(this.options,n)||p(this,Ke).getMutationCache().notify({type:"observerOptionsUpdated",mutation:p(this,ne),observer:this}),n!=null&&n.mutationKey&&this.options.mutationKey&&ct(n.mutationKey)!==ct(this.options.mutationKey)?this.reset():((r=p(this,ne))==null?void 0:r.state.status)==="pending"&&p(this,ne).setOptions(this.options)}onUnsubscribe(){var s;this.hasListeners()||(s=p(this,ne))==null||s.removeObserver(this)}onMutationUpdate(s){D(this,Ee,vs).call(this),D(this,Ee,wn).call(this,s)}getCurrentResult(){return p(this,ze)}reset(){var s;(s=p(this,ne))==null||s.removeObserver(this),P(this,ne,void 0),D(this,Ee,vs).call(this),D(this,Ee,wn).call(this)}mutate(s,n){var r;return P(this,Ce,n),(r=p(this,ne))==null||r.removeObserver(this),P(this,ne,p(this,Ke).getMutationCache().build(p(this,Ke),this.options)),p(this,ne).addObserver(this),p(this,ne).execute(s)}},Ke=new WeakMap,ze=new WeakMap,ne=new WeakMap,Ce=new WeakMap,Ee=new WeakSet,vs=function(){var n;const s=((n=p(this,ne))==null?void 0:n.state)??Qo();P(this,ze,{...s,isPending:s.status==="pending",isSuccess:s.status==="success",isError:s.status==="error",isIdle:s.status==="idle",mutate:this.mutate,reset:this.reset})},wn=function(s){q.batch(()=>{var n,r,o,i,a,l,c,u;if(p(this,Ce)&&this.hasListeners()){const d=p(this,ze).variables,f=p(this,ze).context;(s==null?void 0:s.type)==="success"?((r=(n=p(this,Ce)).onSuccess)==null||r.call(n,s.data,d,f),(i=(o=p(this,Ce)).onSettled)==null||i.call(o,s.data,null,d,f)):(s==null?void 0:s.type)==="error"&&((l=(a=p(this,Ce)).onError)==null||l.call(a,s.error,d,f),(u=(c=p(this,Ce)).onSettled)==null||u.call(c,void 0,s.error,d,f))}this.listeners.forEach(d=>{d(p(this,ze))})})},bo),Xo=g.createContext(void 0),Yn=t=>{const e=g.useContext(Xo);if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},cu=({client:t,children:e})=>(g.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),h.jsx(Xo.Provider,{value:t,children:e})),Zo=g.createContext(!1),uu=()=>g.useContext(Zo);Zo.Provider;function du(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var hu=g.createContext(du()),fu=()=>g.useContext(hu);function Jo(t,e){return typeof t=="function"?t(...e):!!t}function Tn(){}var pu=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))},mu=t=>{g.useEffect(()=>{t.clearReset()},[t])},yu=({result:t,errorResetBoundary:e,throwOnError:s,query:n})=>t.isError&&!e.isReset()&&!t.isFetching&&n&&Jo(s,[t.error,n]),gu=t=>{t.suspense&&(t.staleTime===void 0&&(t.staleTime=1e3),typeof t.gcTime=="number"&&(t.gcTime=Math.max(t.gcTime,1e3)))},vu=(t,e)=>t.isLoading&&t.isFetching&&!e,xu=(t,e)=>(t==null?void 0:t.suspense)&&e.isPending,qr=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function bu(t,e,s){var u,d,f,m,x;const n=Yn(),r=uu(),o=fu(),i=n.defaultQueryOptions(t);(d=(u=n.getDefaultOptions().queries)==null?void 0:u._experimental_beforeQuery)==null||d.call(u,i),i._optimisticResults=r?"isRestoring":"optimistic",gu(i),pu(i,o),mu(o);const a=!n.getQueryCache().get(i.queryHash),[l]=g.useState(()=>new e(n,i)),c=l.getOptimisticResult(i);if(g.useSyncExternalStore(g.useCallback(y=>{const v=r?Tn:l.subscribe(q.batchCalls(y));return l.updateResult(),v},[l,r]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),g.useEffect(()=>{l.setOptions(i,{listeners:!1})},[i,l]),xu(i,c))throw qr(i,l,o);if(yu({result:c,errorResetBoundary:o,throwOnError:i.throwOnError,query:n.getQueryCache().get(i.queryHash)}))throw c.error;if((m=(f=n.getDefaultOptions().queries)==null?void 0:f._experimental_afterQuery)==null||m.call(f,i,c),i.experimental_prefetchInRender&&!lt&&vu(c,r)){const y=a?qr(i,l,o):(x=n.getQueryCache().get(i.queryHash))==null?void 0:x.promise;y==null||y.catch(Tn).finally(()=>{l.updateResult()})}return i.notifyOnChangeProps?c:l.trackResult(c)}function ea(t,e){return bu(t,iu)}function wu(t,e){const s=Yn(),[n]=g.useState(()=>new lu(s,t));g.useEffect(()=>{n.setOptions(t)},[n,t]);const r=g.useSyncExternalStore(g.useCallback(i=>n.subscribe(q.batchCalls(i)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),o=g.useCallback((i,a)=>{n.mutate(i,a).catch(Tn)},[n]);if(r.error&&Jo(n.options.throwOnError,[r.error]))throw r.error;return{...r,mutate:o,mutateAsync:r.mutate}}async function ta(t){if(!t.ok){const e=await t.text()||t.statusText;throw new Error(`${t.status}: ${e}`)}}async function Tu(t,e,s){const n=await fetch(e,{method:t,headers:s?{"Content-Type":"application/json"}:{},body:s?JSON.stringify(s):void 0,credentials:"include"});return await ta(n),n}const Pu=({on401:t})=>async({queryKey:e})=>{const s=await fetch(e.join("/"),{credentials:"include"});return t==="returnNull"&&s.status===401?null:(await ta(s),await s.json())},Su=new ru({defaultOptions:{queries:{queryFn:Pu({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}}),Cu=1,Au=1e6;let Gs=0;function ju(){return Gs=(Gs+1)%Number.MAX_SAFE_INTEGER,Gs.toString()}const qs=new Map,$r=t=>{if(qs.has(t))return;const e=setTimeout(()=>{qs.delete(t),Wt({type:"REMOVE_TOAST",toastId:t})},Au);qs.set(t,e)},Ru=(t,e)=>{switch(e.type){case"ADD_TOAST":return{...t,toasts:[e.toast,...t.toasts].slice(0,Cu)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(s=>s.id===e.toast.id?{...s,...e.toast}:s)};case"DISMISS_TOAST":{const{toastId:s}=e;return s?$r(s):t.toasts.forEach(n=>{$r(n.id)}),{...t,toasts:t.toasts.map(n=>n.id===s||s===void 0?{...n,open:!1}:n)}}case"REMOVE_TOAST":return e.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(s=>s.id!==e.toastId)}}},xs=[];let bs={toasts:[]};function Wt(t){bs=Ru(bs,t),xs.forEach(e=>{e(bs)})}function Eu({...t}){const e=ju(),s=r=>Wt({type:"UPDATE_TOAST",toast:{...r,id:e}}),n=()=>Wt({type:"DISMISS_TOAST",toastId:e});return Wt({type:"ADD_TOAST",toast:{...t,id:e,open:!0,onOpenChange:r=>{r||n()}}}),{id:e,dismiss:n,update:s}}function sa(){const[t,e]=g.useState(bs);return g.useEffect(()=>(xs.push(e),()=>{const s=xs.indexOf(e);s>-1&&xs.splice(s,1)}),[t]),{...t,toast:Eu,dismiss:s=>Wt({type:"DISMISS_TOAST",toastId:s})}}function na(t){var e,s,n="";if(typeof t=="string"||typeof t=="number")n+=t;else if(typeof t=="object")if(Array.isArray(t)){var r=t.length;for(e=0;e<r;e++)t[e]&&(s=na(t[e]))&&(n&&(n+=" "),n+=s)}else for(s in t)t[s]&&(n&&(n+=" "),n+=s);return n}function ra(){for(var t,e,s=0,n="",r=arguments.length;s<r;s++)(t=arguments[s])&&(e=na(t))&&(n&&(n+=" "),n+=e);return n}const Wr=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,Qr=ra,Mu=(t,e)=>s=>{var n;if((e==null?void 0:e.variants)==null)return Qr(t,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:r,defaultVariants:o}=e,i=Object.keys(r).map(c=>{const u=s==null?void 0:s[c],d=o==null?void 0:o[c];if(u===null)return null;const f=Wr(u)||Wr(d);return r[c][f]}),a=s&&Object.entries(s).reduce((c,u)=>{let[d,f]=u;return f===void 0||(c[d]=f),c},{}),l=e==null||(n=e.compoundVariants)===null||n===void 0?void 0:n.reduce((c,u)=>{let{class:d,className:f,...m}=u;return Object.entries(m).every(x=>{let[y,v]=x;return Array.isArray(v)?v.includes({...o,...a}[y]):{...o,...a}[y]===v})?[...c,d,f]:c},[]);return Qr(t,i,l,s==null?void 0:s.class,s==null?void 0:s.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ku=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ia=(...t)=>t.filter((e,s,n)=>!!e&&n.indexOf(e)===s).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Nu={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Du=g.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:r="",children:o,iconNode:i,...a},l)=>g.createElement("svg",{ref:l,...Nu,width:e,height:e,stroke:t,strokeWidth:n?Number(s)*24/Number(e):s,className:ia("lucide",r),...a},[...i.map(([c,u])=>g.createElement(c,u)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=(t,e)=>{const s=g.forwardRef(({className:n,...r},o)=>g.createElement(Du,{ref:o,iconNode:e,className:ia(`lucide-${ku(t)}`,n),...r}));return s.displayName=`${t}`,s};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vu=G("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ou=G("Bike",[["circle",{cx:"18.5",cy:"17.5",r:"3.5",key:"15x4ox"}],["circle",{cx:"5.5",cy:"17.5",r:"3.5",key:"1noe27"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["path",{d:"M12 17.5V14l-3-3 4-3 2 3h2",key:"1npguv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lu=G("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iu=G("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fu=G("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bu=G("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uu=G("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _u=G("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ku=G("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zu=G("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hu=G("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gu=G("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qu=G("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $u=G("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wu=G("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qu=G("Sandwich",[["path",{d:"m2.37 11.223 8.372-6.777a2 2 0 0 1 2.516 0l8.371 6.777",key:"f1wd0e"}],["path",{d:"M21 15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5.25",key:"1pfu07"}],["path",{d:"M3 15a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h9",key:"1oq9qw"}],["path",{d:"m6.67 15 6.13 4.6a2 2 0 0 0 2.8-.4l3.15-4.2",key:"1fnwu5"}],["rect",{width:"20",height:"4",x:"2",y:"11",rx:"1",key:"itshg"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pn=G("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yu=G("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xu=G("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oa=G("Utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aa=G("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Xn="-",Zu=t=>{const e=ed(t),{conflictingClassGroups:s,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:i=>{const a=i.split(Xn);return a[0]===""&&a.length!==1&&a.shift(),la(a,e)||Ju(i)},getConflictingClassGroupIds:(i,a)=>{const l=s[i]||[];return a&&n[i]?[...l,...n[i]]:l}}},la=(t,e)=>{var i;if(t.length===0)return e.classGroupId;const s=t[0],n=e.nextPart.get(s),r=n?la(t.slice(1),n):void 0;if(r)return r;if(e.validators.length===0)return;const o=t.join(Xn);return(i=e.validators.find(({validator:a})=>a(o)))==null?void 0:i.classGroupId},Yr=/^\[(.+)\]$/,Ju=t=>{if(Yr.test(t)){const e=Yr.exec(t)[1],s=e==null?void 0:e.substring(0,e.indexOf(":"));if(s)return"arbitrary.."+s}},ed=t=>{const{theme:e,prefix:s}=t,n={nextPart:new Map,validators:[]};return sd(Object.entries(t.classGroups),s).forEach(([o,i])=>{Sn(i,n,o,e)}),n},Sn=(t,e,s,n)=>{t.forEach(r=>{if(typeof r=="string"){const o=r===""?e:Xr(e,r);o.classGroupId=s;return}if(typeof r=="function"){if(td(r)){Sn(r(n),e,s,n);return}e.validators.push({validator:r,classGroupId:s});return}Object.entries(r).forEach(([o,i])=>{Sn(i,Xr(e,o),s,n)})})},Xr=(t,e)=>{let s=t;return e.split(Xn).forEach(n=>{s.nextPart.has(n)||s.nextPart.set(n,{nextPart:new Map,validators:[]}),s=s.nextPart.get(n)}),s},td=t=>t.isThemeGetter,sd=(t,e)=>e?t.map(([s,n])=>{const r=n.map(o=>typeof o=="string"?e+o:typeof o=="object"?Object.fromEntries(Object.entries(o).map(([i,a])=>[e+i,a])):o);return[s,r]}):t,nd=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,s=new Map,n=new Map;const r=(o,i)=>{s.set(o,i),e++,e>t&&(e=0,n=s,s=new Map)};return{get(o){let i=s.get(o);if(i!==void 0)return i;if((i=n.get(o))!==void 0)return r(o,i),i},set(o,i){s.has(o)?s.set(o,i):r(o,i)}}},ca="!",rd=t=>{const{separator:e,experimentalParseClassName:s}=t,n=e.length===1,r=e[0],o=e.length,i=a=>{const l=[];let c=0,u=0,d;for(let v=0;v<a.length;v++){let b=a[v];if(c===0){if(b===r&&(n||a.slice(v,v+o)===e)){l.push(a.slice(u,v)),u=v+o;continue}if(b==="/"){d=v;continue}}b==="["?c++:b==="]"&&c--}const f=l.length===0?a:a.substring(u),m=f.startsWith(ca),x=m?f.substring(1):f,y=d&&d>u?d-u:void 0;return{modifiers:l,hasImportantModifier:m,baseClassName:x,maybePostfixModifierPosition:y}};return s?a=>s({className:a,parseClassName:i}):i},id=t=>{if(t.length<=1)return t;const e=[];let s=[];return t.forEach(n=>{n[0]==="["?(e.push(...s.sort(),n),s=[]):s.push(n)}),e.push(...s.sort()),e},od=t=>({cache:nd(t.cacheSize),parseClassName:rd(t),...Zu(t)}),ad=/\s+/,ld=(t,e)=>{const{parseClassName:s,getClassGroupId:n,getConflictingClassGroupIds:r}=e,o=[],i=t.trim().split(ad);let a="";for(let l=i.length-1;l>=0;l-=1){const c=i[l],{modifiers:u,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:m}=s(c);let x=!!m,y=n(x?f.substring(0,m):f);if(!y){if(!x){a=c+(a.length>0?" "+a:a);continue}if(y=n(f),!y){a=c+(a.length>0?" "+a:a);continue}x=!1}const v=id(u).join(":"),b=d?v+ca:v,w=b+y;if(o.includes(w))continue;o.push(w);const T=r(y,x);for(let A=0;A<T.length;++A){const S=T[A];o.push(b+S)}a=c+(a.length>0?" "+a:a)}return a};function cd(){let t=0,e,s,n="";for(;t<arguments.length;)(e=arguments[t++])&&(s=ua(e))&&(n&&(n+=" "),n+=s);return n}const ua=t=>{if(typeof t=="string")return t;let e,s="";for(let n=0;n<t.length;n++)t[n]&&(e=ua(t[n]))&&(s&&(s+=" "),s+=e);return s};function ud(t,...e){let s,n,r,o=i;function i(l){const c=e.reduce((u,d)=>d(u),t());return s=od(c),n=s.cache.get,r=s.cache.set,o=a,a(l)}function a(l){const c=n(l);if(c)return c;const u=ld(l,s);return r(l,u),u}return function(){return o(cd.apply(null,arguments))}}const B=t=>{const e=s=>s[t]||[];return e.isThemeGetter=!0,e},da=/^\[(?:([a-z-]+):)?(.+)\]$/i,dd=/^\d+\/\d+$/,hd=new Set(["px","full","screen"]),fd=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,pd=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,md=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,yd=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,gd=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Pe=t=>xt(t)||hd.has(t)||dd.test(t),ke=t=>Bt(t,"length",Cd),xt=t=>!!t&&!Number.isNaN(Number(t)),$s=t=>Bt(t,"number",xt),Kt=t=>!!t&&Number.isInteger(Number(t)),vd=t=>t.endsWith("%")&&xt(t.slice(0,-1)),N=t=>da.test(t),Ne=t=>fd.test(t),xd=new Set(["length","size","percentage"]),bd=t=>Bt(t,xd,ha),wd=t=>Bt(t,"position",ha),Td=new Set(["image","url"]),Pd=t=>Bt(t,Td,jd),Sd=t=>Bt(t,"",Ad),zt=()=>!0,Bt=(t,e,s)=>{const n=da.exec(t);return n?n[1]?typeof e=="string"?n[1]===e:e.has(n[1]):s(n[2]):!1},Cd=t=>pd.test(t)&&!md.test(t),ha=()=>!1,Ad=t=>yd.test(t),jd=t=>gd.test(t),Rd=()=>{const t=B("colors"),e=B("spacing"),s=B("blur"),n=B("brightness"),r=B("borderColor"),o=B("borderRadius"),i=B("borderSpacing"),a=B("borderWidth"),l=B("contrast"),c=B("grayscale"),u=B("hueRotate"),d=B("invert"),f=B("gap"),m=B("gradientColorStops"),x=B("gradientColorStopPositions"),y=B("inset"),v=B("margin"),b=B("opacity"),w=B("padding"),T=B("saturate"),A=B("scale"),S=B("sepia"),k=B("skew"),V=B("space"),C=B("translate"),M=()=>["auto","contain","none"],U=()=>["auto","hidden","clip","visible","scroll"],W=()=>["auto",N,e],L=()=>[N,e],hs=()=>["",Pe,ke],Qe=()=>["auto",xt,N],_s=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],_t=()=>["solid","dashed","dotted","double","none"],$=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],re=()=>["start","end","center","between","around","evenly","stretch"],we=()=>["","0",N],dt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Te=()=>[xt,N];return{cacheSize:500,separator:":",theme:{colors:[zt],spacing:[Pe,ke],blur:["none","",Ne,N],brightness:Te(),borderColor:[t],borderRadius:["none","","full",Ne,N],borderSpacing:L(),borderWidth:hs(),contrast:Te(),grayscale:we(),hueRotate:Te(),invert:we(),gap:L(),gradientColorStops:[t],gradientColorStopPositions:[vd,ke],inset:W(),margin:W(),opacity:Te(),padding:L(),saturate:Te(),scale:Te(),sepia:we(),skew:Te(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[Ne]}],"break-after":[{"break-after":dt()}],"break-before":[{"break-before":dt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[..._s(),N]}],overflow:[{overflow:U()}],"overflow-x":[{"overflow-x":U()}],"overflow-y":[{"overflow-y":U()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Kt,N]}],basis:[{basis:W()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:we()}],shrink:[{shrink:we()}],order:[{order:["first","last","none",Kt,N]}],"grid-cols":[{"grid-cols":[zt]}],"col-start-end":[{col:["auto",{span:["full",Kt,N]},N]}],"col-start":[{"col-start":Qe()}],"col-end":[{"col-end":Qe()}],"grid-rows":[{"grid-rows":[zt]}],"row-start-end":[{row:["auto",{span:[Kt,N]},N]}],"row-start":[{"row-start":Qe()}],"row-end":[{"row-end":Qe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...re()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...re(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...re(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[w]}],px:[{px:[w]}],py:[{py:[w]}],ps:[{ps:[w]}],pe:[{pe:[w]}],pt:[{pt:[w]}],pr:[{pr:[w]}],pb:[{pb:[w]}],pl:[{pl:[w]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[V]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[V]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",N,e]}],"min-w":[{"min-w":[N,e,"min","max","fit"]}],"max-w":[{"max-w":[N,e,"none","full","min","max","fit","prose",{screen:[Ne]},Ne]}],h:[{h:[N,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[N,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[N,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[N,e,"auto","min","max","fit"]}],"font-size":[{text:["base",Ne,ke]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",$s]}],"font-family":[{font:[zt]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",xt,$s]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Pe,N]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[..._t(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Pe,ke]}],"underline-offset":[{"underline-offset":["auto",Pe,N]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[..._s(),wd]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",bd]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Pd]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:[..._t(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:_t()}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["",..._t()]}],"outline-offset":[{"outline-offset":[Pe,N]}],"outline-w":[{outline:[Pe,ke]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:hs()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[Pe,ke]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",Ne,Sd]}],"shadow-color":[{shadow:[zt]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":[...$(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":$()}],filter:[{filter:["","none"]}],blur:[{blur:[s]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Ne,N]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[d]}],saturate:[{saturate:[T]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[s]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[T]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:Te()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:Te()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[A]}],"scale-x":[{"scale-x":[A]}],"scale-y":[{"scale-y":[A]}],rotate:[{rotate:[Kt,N]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[Pe,ke,$s]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Ed=ud(Rd);function ae(...t){return Ed(ra(t))}const Md=sc,fa=g.forwardRef(({className:t,...e},s)=>h.jsx(wo,{ref:s,className:ae("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),...e}));fa.displayName=wo.displayName;const kd=Mu("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),pa=g.forwardRef(({className:t,variant:e,...s},n)=>h.jsx(To,{ref:n,className:ae(kd({variant:e}),t),...s}));pa.displayName=To.displayName;const Nd=g.forwardRef(({className:t,...e},s)=>h.jsx(Po,{ref:s,className:ae("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",t),...e}));Nd.displayName=Po.displayName;const ma=g.forwardRef(({className:t,...e},s)=>h.jsx(So,{ref:s,className:ae("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),"toast-close":"",...e,children:h.jsx(aa,{className:"h-4 w-4"})}));ma.displayName=So.displayName;const ya=g.forwardRef(({className:t,...e},s)=>h.jsx(Co,{ref:s,className:ae("text-sm font-semibold",t),...e}));ya.displayName=Co.displayName;const ga=g.forwardRef(({className:t,...e},s)=>h.jsx(Ao,{ref:s,className:ae("text-sm opacity-90",t),...e}));ga.displayName=Ao.displayName;function Dd(){const{toasts:t}=sa();return h.jsxs(Md,{children:[t.map(function({id:e,title:s,description:n,action:r,...o}){return h.jsxs(pa,{...o,children:[h.jsxs("div",{className:"grid gap-1",children:[s&&h.jsx(ya,{children:s}),n&&h.jsx(ga,{children:n})]}),r,h.jsx(ma,{})]},e)}),h.jsx(fa,{})]})}var[Vs,Ky]=nc("Tooltip",[jo]),Zn=jo(),va="TooltipProvider",Vd=700,Zr="tooltip.open",[Od,xa]=Vs(va),ba=t=>{const{__scopeTooltip:e,delayDuration:s=Vd,skipDelayDuration:n=300,disableHoverableContent:r=!1,children:o}=t,i=g.useRef(!0),a=g.useRef(!1),l=g.useRef(0);return g.useEffect(()=>{const c=l.current;return()=>window.clearTimeout(c)},[]),h.jsx(Od,{scope:e,isOpenDelayedRef:i,delayDuration:s,onOpen:g.useCallback(()=>{window.clearTimeout(l.current),i.current=!1},[]),onClose:g.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.current=!0,n)},[n]),isPointerInTransitRef:a,onPointerInTransitChange:g.useCallback(c=>{a.current=c},[]),disableHoverableContent:r,children:o})};ba.displayName=va;var wa="Tooltip",[zy,Os]=Vs(wa),Cn="TooltipTrigger",Ld=g.forwardRef((t,e)=>{const{__scopeTooltip:s,...n}=t,r=Os(Cn,s),o=xa(Cn,s),i=Zn(s),a=g.useRef(null),l=Ro(e,a,r.onTriggerChange),c=g.useRef(!1),u=g.useRef(!1),d=g.useCallback(()=>c.current=!1,[]);return g.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),h.jsx(rc,{asChild:!0,...i,children:h.jsx(ic.button,{"aria-describedby":r.open?r.contentId:void 0,"data-state":r.stateAttribute,...n,ref:l,onPointerMove:ht(t.onPointerMove,f=>{f.pointerType!=="touch"&&!u.current&&!o.isPointerInTransitRef.current&&(r.onTriggerEnter(),u.current=!0)}),onPointerLeave:ht(t.onPointerLeave,()=>{r.onTriggerLeave(),u.current=!1}),onPointerDown:ht(t.onPointerDown,()=>{r.open&&r.onClose(),c.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:ht(t.onFocus,()=>{c.current||r.onOpen()}),onBlur:ht(t.onBlur,r.onClose),onClick:ht(t.onClick,r.onClose)})})});Ld.displayName=Cn;var Id="TooltipPortal",[Hy,Fd]=Vs(Id,{forceMount:void 0}),Vt="TooltipContent",Ta=g.forwardRef((t,e)=>{const s=Fd(Vt,t.__scopeTooltip),{forceMount:n=s.forceMount,side:r="top",...o}=t,i=Os(Vt,t.__scopeTooltip);return h.jsx(oc,{present:n||i.open,children:i.disableHoverableContent?h.jsx(Pa,{side:r,...o,ref:e}):h.jsx(Bd,{side:r,...o,ref:e})})}),Bd=g.forwardRef((t,e)=>{const s=Os(Vt,t.__scopeTooltip),n=xa(Vt,t.__scopeTooltip),r=g.useRef(null),o=Ro(e,r),[i,a]=g.useState(null),{trigger:l,onClose:c}=s,u=r.current,{onPointerInTransitChange:d}=n,f=g.useCallback(()=>{a(null),d(!1)},[d]),m=g.useCallback((x,y)=>{const v=x.currentTarget,b={x:x.clientX,y:x.clientY},w=Hd(b,v.getBoundingClientRect()),T=Gd(b,w),A=qd(y.getBoundingClientRect()),S=Wd([...T,...A]);a(S),d(!0)},[d]);return g.useEffect(()=>()=>f(),[f]),g.useEffect(()=>{if(l&&u){const x=v=>m(v,u),y=v=>m(v,l);return l.addEventListener("pointerleave",x),u.addEventListener("pointerleave",y),()=>{l.removeEventListener("pointerleave",x),u.removeEventListener("pointerleave",y)}}},[l,u,m,f]),g.useEffect(()=>{if(i){const x=y=>{const v=y.target,b={x:y.clientX,y:y.clientY},w=(l==null?void 0:l.contains(v))||(u==null?void 0:u.contains(v)),T=!$d(b,i);w?f():T&&(f(),c())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[l,u,i,c,f]),h.jsx(Pa,{...t,ref:o})}),[Ud,_d]=Vs(wa,{isInside:!1}),Kd=dc("TooltipContent"),Pa=g.forwardRef((t,e)=>{const{__scopeTooltip:s,children:n,"aria-label":r,onEscapeKeyDown:o,onPointerDownOutside:i,...a}=t,l=Os(Vt,s),c=Zn(s),{onClose:u}=l;return g.useEffect(()=>(document.addEventListener(Zr,u),()=>document.removeEventListener(Zr,u)),[u]),g.useEffect(()=>{if(l.trigger){const d=f=>{const m=f.target;m!=null&&m.contains(l.trigger)&&u()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[l.trigger,u]),h.jsx(ac,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:d=>d.preventDefault(),onDismiss:u,children:h.jsxs(lc,{"data-state":l.stateAttribute,...c,...a,ref:e,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[h.jsx(Kd,{children:n}),h.jsx(Ud,{scope:s,isInside:!0,children:h.jsx(cc,{id:l.contentId,role:"tooltip",children:r||n})})]})})});Ta.displayName=Vt;var Sa="TooltipArrow",zd=g.forwardRef((t,e)=>{const{__scopeTooltip:s,...n}=t,r=Zn(s);return _d(Sa,s).isInside?null:h.jsx(uc,{...r,...n,ref:e})});zd.displayName=Sa;function Hd(t,e){const s=Math.abs(e.top-t.y),n=Math.abs(e.bottom-t.y),r=Math.abs(e.right-t.x),o=Math.abs(e.left-t.x);switch(Math.min(s,n,r,o)){case o:return"left";case r:return"right";case s:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function Gd(t,e,s=5){const n=[];switch(e){case"top":n.push({x:t.x-s,y:t.y+s},{x:t.x+s,y:t.y+s});break;case"bottom":n.push({x:t.x-s,y:t.y-s},{x:t.x+s,y:t.y-s});break;case"left":n.push({x:t.x+s,y:t.y-s},{x:t.x+s,y:t.y+s});break;case"right":n.push({x:t.x-s,y:t.y-s},{x:t.x-s,y:t.y+s});break}return n}function qd(t){const{top:e,right:s,bottom:n,left:r}=t;return[{x:r,y:e},{x:s,y:e},{x:s,y:n},{x:r,y:n}]}function $d(t,e){const{x:s,y:n}=t;let r=!1;for(let o=0,i=e.length-1;o<e.length;i=o++){const a=e[o].x,l=e[o].y,c=e[i].x,u=e[i].y;l>n!=u>n&&s<(c-a)*(n-l)/(u-l)+a&&(r=!r)}return r}function Wd(t){const e=t.slice();return e.sort((s,n)=>s.x<n.x?-1:s.x>n.x?1:s.y<n.y?-1:s.y>n.y?1:0),Qd(e)}function Qd(t){if(t.length<=1)return t.slice();const e=[];for(let n=0;n<t.length;n++){const r=t[n];for(;e.length>=2;){const o=e[e.length-1],i=e[e.length-2];if((o.x-i.x)*(r.y-i.y)>=(o.y-i.y)*(r.x-i.x))e.pop();else break}e.push(r)}e.pop();const s=[];for(let n=t.length-1;n>=0;n--){const r=t[n];for(;s.length>=2;){const o=s[s.length-1],i=s[s.length-2];if((o.x-i.x)*(r.y-i.y)>=(o.y-i.y)*(r.x-i.x))s.pop();else break}s.push(r)}return s.pop(),e.length===1&&s.length===1&&e[0].x===s[0].x&&e[0].y===s[0].y?e:e.concat(s)}var Yd=ba,Ca=Ta;const Xd=Yd,Zd=g.forwardRef(({className:t,sideOffset:e=4,...s},n)=>h.jsx(Ca,{ref:n,sideOffset:e,className:ae("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",t),...s}));Zd.displayName=Ca.displayName;const Aa=g.forwardRef(({className:t,...e},s)=>h.jsx("div",{ref:s,className:ae("rounded-lg border bg-card text-card-foreground shadow-sm",t),...e}));Aa.displayName="Card";const Jd=g.forwardRef(({className:t,...e},s)=>h.jsx("div",{ref:s,className:ae("flex flex-col space-y-1.5 p-6",t),...e}));Jd.displayName="CardHeader";const eh=g.forwardRef(({className:t,...e},s)=>h.jsx("div",{ref:s,className:ae("text-2xl font-semibold leading-none tracking-tight",t),...e}));eh.displayName="CardTitle";const th=g.forwardRef(({className:t,...e},s)=>h.jsx("div",{ref:s,className:ae("text-sm text-muted-foreground",t),...e}));th.displayName="CardDescription";const ja=g.forwardRef(({className:t,...e},s)=>h.jsx("div",{ref:s,className:ae("p-6 pt-0",t),...e}));ja.displayName="CardContent";const sh=g.forwardRef(({className:t,...e},s)=>h.jsx("div",{ref:s,className:ae("flex items-center p-6 pt-0",t),...e}));sh.displayName="CardFooter";function nh(){return h.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:h.jsx(Aa,{className:"w-full max-w-md mx-4",children:h.jsxs(ja,{className:"pt-6",children:[h.jsxs("div",{className:"flex mb-4 gap-2",children:[h.jsx(Fu,{className:"h-8 w-8 text-red-500"}),h.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),h.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}function rh(t){if(typeof Proxy>"u")return t;const e=new Map,s=(...n)=>t(...n);return new Proxy(s,{get:(n,r)=>r==="create"?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}function Ls(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const An=t=>Array.isArray(t);function Ra(t,e){if(!Array.isArray(e))return!1;const s=e.length;if(s!==t.length)return!1;for(let n=0;n<s;n++)if(e[n]!==t[n])return!1;return!0}function Jt(t){return typeof t=="string"||Array.isArray(t)}function Jr(t){const e=[{},{}];return t==null||t.values.forEach((s,n)=>{e[0][n]=s.get(),e[1][n]=s.getVelocity()}),e}function Jn(t,e,s,n){if(typeof e=="function"){const[r,o]=Jr(n);e=e(s!==void 0?s:t.custom,r,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[r,o]=Jr(n);e=e(s!==void 0?s:t.custom,r,o)}return e}function Is(t,e,s){const n=t.getProps();return Jn(n,e,s!==void 0?s:n.custom,t)}const er=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tr=["initial",...er],as=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ut=new Set(as),je=t=>t*1e3,Re=t=>t/1e3,ih={type:"spring",stiffness:500,damping:25,restSpeed:10},oh=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),ah={type:"keyframes",duration:.8},lh={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ch=(t,{keyframes:e})=>e.length>2?ah:ut.has(t)?t.startsWith("scale")?oh(e[1]):ih:lh;function sr(t,e){return t?t[e]||t.default||t:void 0}const uh={skipAnimations:!1,useManualTiming:!1},dh=t=>t!==null;function Fs(t,{repeat:e,repeatType:s="loop"},n){const r=t.filter(dh),o=e&&s!=="loop"&&e%2===1?0:r.length-1;return!o||n===void 0?r[o]:n}const J=t=>t;let jn=J;function hh(t){let e=new Set,s=new Set,n=!1,r=!1;const o=new WeakSet;let i={delta:0,timestamp:0,isProcessing:!1};function a(c){o.has(c)&&(l.schedule(c),t()),c(i)}const l={schedule:(c,u=!1,d=!1)=>{const m=d&&n?e:s;return u&&o.add(c),m.has(c)||m.add(c),c},cancel:c=>{s.delete(c),o.delete(c)},process:c=>{if(i=c,n){r=!0;return}n=!0,[e,s]=[s,e],s.clear(),e.forEach(a),n=!1,r&&(r=!1,l.process(c))}};return l}const ms=["read","resolveKeyframes","update","preRender","render","postRender"],fh=40;function Ea(t,e){let s=!1,n=!0;const r={delta:0,timestamp:0,isProcessing:!1},o=()=>s=!0,i=ms.reduce((b,w)=>(b[w]=hh(o),b),{}),{read:a,resolveKeyframes:l,update:c,preRender:u,render:d,postRender:f}=i,m=()=>{const b=performance.now();s=!1,r.delta=n?1e3/60:Math.max(Math.min(b-r.timestamp,fh),1),r.timestamp=b,r.isProcessing=!0,a.process(r),l.process(r),c.process(r),u.process(r),d.process(r),f.process(r),r.isProcessing=!1,s&&e&&(n=!1,t(m))},x=()=>{s=!0,n=!0,r.isProcessing||t(m)};return{schedule:ms.reduce((b,w)=>{const T=i[w];return b[w]=(A,S=!1,k=!1)=>(s||x(),T.schedule(A,S,k)),b},{}),cancel:b=>{for(let w=0;w<ms.length;w++)i[ms[w]].cancel(b)},state:r,steps:i}}const{schedule:F,cancel:qe,state:Q,steps:Ws}=Ea(typeof requestAnimationFrame<"u"?requestAnimationFrame:J,!0),Ma=(t,e,s)=>(((1-3*s+3*e)*t+(3*s-6*e))*t+3*e)*t,ph=1e-7,mh=12;function yh(t,e,s,n,r){let o,i,a=0;do i=e+(s-e)/2,o=Ma(i,n,r)-t,o>0?s=i:e=i;while(Math.abs(o)>ph&&++a<mh);return i}function ls(t,e,s,n){if(t===e&&s===n)return J;const r=o=>yh(o,0,1,t,s);return o=>o===0||o===1?o:Ma(r(o),e,n)}const ka=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Na=t=>e=>1-t(1-e),Da=ls(.33,1.53,.69,.99),nr=Na(Da),Va=ka(nr),Oa=t=>(t*=2)<1?.5*nr(t):.5*(2-Math.pow(2,-10*(t-1))),rr=t=>1-Math.sin(Math.acos(t)),La=Na(rr),Ia=ka(rr),Fa=t=>/^0[^.\s]+$/u.test(t);function gh(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Fa(t):!0}const Ba=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Ua=t=>e=>typeof e=="string"&&e.startsWith(t),_a=Ua("--"),vh=Ua("var(--"),ir=t=>vh(t)?xh.test(t.split("/*")[0].trim()):!1,xh=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,bh=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function wh(t){const e=bh.exec(t);if(!e)return[,];const[,s,n,r]=e;return[`--${s??n}`,r]}function Ka(t,e,s=1){const[n,r]=wh(t);if(!n)return;const o=window.getComputedStyle(e).getPropertyValue(n);if(o){const i=o.trim();return Ba(i)?parseFloat(i):i}return ir(r)?Ka(r,e,s+1):r}const Me=(t,e,s)=>s>e?e:s<t?t:s,Ut={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},es={...Ut,transform:t=>Me(0,1,t)},ys={...Ut,default:1},cs=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Ve=cs("deg"),xe=cs("%"),R=cs("px"),Th=cs("vh"),Ph=cs("vw"),ei={...xe,parse:t=>xe.parse(t)/100,transform:t=>xe.transform(t*100)},Sh=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),ti=t=>t===Ut||t===R,si=(t,e)=>parseFloat(t.split(", ")[e]),ni=(t,e)=>(s,{transform:n})=>{if(n==="none"||!n)return 0;const r=n.match(/^matrix3d\((.+)\)$/u);if(r)return si(r[1],e);{const o=n.match(/^matrix\((.+)\)$/u);return o?si(o[1],t):0}},Ch=new Set(["x","y","z"]),Ah=as.filter(t=>!Ch.has(t));function jh(t){const e=[];return Ah.forEach(s=>{const n=t.getValue(s);n!==void 0&&(e.push([s,n.get()]),n.set(s.startsWith("scale")?1:0))}),e}const Ot={width:({x:t},{paddingLeft:e="0",paddingRight:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),height:({y:t},{paddingTop:e="0",paddingBottom:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:ni(4,13),y:ni(5,14)};Ot.translateX=Ot.x;Ot.translateY=Ot.y;const za=t=>e=>e.test(t),Rh={test:t=>t==="auto",parse:t=>t},Ha=[Ut,R,xe,Ve,Ph,Th,Rh],ri=t=>Ha.find(za(t)),at=new Set;let Rn=!1,En=!1;function Ga(){if(En){const t=Array.from(at).filter(n=>n.needsMeasurement),e=new Set(t.map(n=>n.element)),s=new Map;e.forEach(n=>{const r=jh(n);r.length&&(s.set(n,r),n.render())}),t.forEach(n=>n.measureInitialState()),e.forEach(n=>{n.render();const r=s.get(n);r&&r.forEach(([o,i])=>{var a;(a=n.getValue(o))===null||a===void 0||a.set(i)})}),t.forEach(n=>n.measureEndState()),t.forEach(n=>{n.suspendedScrollY!==void 0&&window.scrollTo(0,n.suspendedScrollY)})}En=!1,Rn=!1,at.forEach(t=>t.complete()),at.clear()}function qa(){at.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(En=!0)})}function Eh(){qa(),Ga()}class or{constructor(e,s,n,r,o,i=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=s,this.name=n,this.motionValue=r,this.element=o,this.isAsync=i}scheduleResolve(){this.isScheduled=!0,this.isAsync?(at.add(this),Rn||(Rn=!0,F.read(qa),F.resolveKeyframes(Ga))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:s,element:n,motionValue:r}=this;for(let o=0;o<e.length;o++)if(e[o]===null)if(o===0){const i=r==null?void 0:r.get(),a=e[e.length-1];if(i!==void 0)e[0]=i;else if(n&&s){const l=n.readValue(s,a);l!=null&&(e[0]=l)}e[0]===void 0&&(e[0]=a),r&&i===void 0&&r.set(e[0])}else e[o]=e[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),at.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,at.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Qt=t=>Math.round(t*1e5)/1e5,ar=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Mh(t){return t==null}const kh=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,lr=(t,e)=>s=>!!(typeof s=="string"&&kh.test(s)&&s.startsWith(t)||e&&!Mh(s)&&Object.prototype.hasOwnProperty.call(s,e)),$a=(t,e,s)=>n=>{if(typeof n!="string")return n;const[r,o,i,a]=n.match(ar);return{[t]:parseFloat(r),[e]:parseFloat(o),[s]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},Nh=t=>Me(0,255,t),Qs={...Ut,transform:t=>Math.round(Nh(t))},Je={test:lr("rgb","red"),parse:$a("red","green","blue"),transform:({red:t,green:e,blue:s,alpha:n=1})=>"rgba("+Qs.transform(t)+", "+Qs.transform(e)+", "+Qs.transform(s)+", "+Qt(es.transform(n))+")"};function Dh(t){let e="",s="",n="",r="";return t.length>5?(e=t.substring(1,3),s=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),s=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,s+=s,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(s,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}}const Mn={test:lr("#"),parse:Dh,transform:Je.transform},ft={test:lr("hsl","hue"),parse:$a("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:s,alpha:n=1})=>"hsla("+Math.round(t)+", "+xe.transform(Qt(e))+", "+xe.transform(Qt(s))+", "+Qt(es.transform(n))+")"},X={test:t=>Je.test(t)||Mn.test(t)||ft.test(t),parse:t=>Je.test(t)?Je.parse(t):ft.test(t)?ft.parse(t):Mn.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?Je.transform(t):ft.transform(t)},Vh=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Oh(t){var e,s;return isNaN(t)&&typeof t=="string"&&(((e=t.match(ar))===null||e===void 0?void 0:e.length)||0)+(((s=t.match(Vh))===null||s===void 0?void 0:s.length)||0)>0}const Wa="number",Qa="color",Lh="var",Ih="var(",ii="${}",Fh=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ts(t){const e=t.toString(),s=[],n={color:[],number:[],var:[]},r=[];let o=0;const a=e.replace(Fh,l=>(X.test(l)?(n.color.push(o),r.push(Qa),s.push(X.parse(l))):l.startsWith(Ih)?(n.var.push(o),r.push(Lh),s.push(l)):(n.number.push(o),r.push(Wa),s.push(parseFloat(l))),++o,ii)).split(ii);return{values:s,split:a,indexes:n,types:r}}function Ya(t){return ts(t).values}function Xa(t){const{split:e,types:s}=ts(t),n=e.length;return r=>{let o="";for(let i=0;i<n;i++)if(o+=e[i],r[i]!==void 0){const a=s[i];a===Wa?o+=Qt(r[i]):a===Qa?o+=X.transform(r[i]):o+=r[i]}return o}}const Bh=t=>typeof t=="number"?0:t;function Uh(t){const e=Ya(t);return Xa(t)(e.map(Bh))}const $e={test:Oh,parse:Ya,createTransformer:Xa,getAnimatableNone:Uh},_h=new Set(["brightness","contrast","saturate","opacity"]);function Kh(t){const[e,s]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[n]=s.match(ar)||[];if(!n)return t;const r=s.replace(n,"");let o=_h.has(e)?1:0;return n!==s&&(o*=100),e+"("+o+r+")"}const zh=/\b([a-z-]*)\(.*?\)/gu,kn={...$e,getAnimatableNone:t=>{const e=t.match(zh);return e?e.map(Kh).join(" "):t}},Hh={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R},Gh={rotate:Ve,rotateX:Ve,rotateY:Ve,rotateZ:Ve,scale:ys,scaleX:ys,scaleY:ys,scaleZ:ys,skew:Ve,skewX:Ve,skewY:Ve,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:es,originX:ei,originY:ei,originZ:R},oi={...Ut,transform:Math.round},cr={...Hh,...Gh,zIndex:oi,size:R,fillOpacity:es,strokeOpacity:es,numOctaves:oi},qh={...cr,color:X,backgroundColor:X,outlineColor:X,fill:X,stroke:X,borderColor:X,borderTopColor:X,borderRightColor:X,borderBottomColor:X,borderLeftColor:X,filter:kn,WebkitFilter:kn},ur=t=>qh[t];function Za(t,e){let s=ur(t);return s!==kn&&(s=$e),s.getAnimatableNone?s.getAnimatableNone(e):void 0}const $h=new Set(["auto","none","0"]);function Wh(t,e,s){let n=0,r;for(;n<t.length&&!r;){const o=t[n];typeof o=="string"&&!$h.has(o)&&ts(o).values.length&&(r=t[n]),n++}if(r&&s)for(const o of e)t[o]=Za(s,r)}class Ja extends or{constructor(e,s,n,r,o){super(e,s,n,r,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:s,name:n}=this;if(!s||!s.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),ir(c))){const u=Ka(c,s.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!Sh.has(n)||e.length!==2)return;const[r,o]=e,i=ri(r),a=ri(o);if(i!==a)if(ti(i)&&ti(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:s}=this,n=[];for(let r=0;r<e.length;r++)gh(e[r])&&n.push(r);n.length&&Wh(e,n,s)}measureInitialState(){const{element:e,unresolvedKeyframes:s,name:n}=this;if(!e||!e.current)return;n==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ot[n](e.measureViewportBox(),window.getComputedStyle(e.current)),s[0]=this.measuredOrigin;const r=s[s.length-1];r!==void 0&&e.getValue(n,r).jump(r,!1)}measureEndState(){var e;const{element:s,name:n,unresolvedKeyframes:r}=this;if(!s||!s.current)return;const o=s.getValue(n);o&&o.jump(this.measuredOrigin,!1);const i=r.length-1,a=r[i];r[i]=Ot[n](s.measureViewportBox(),window.getComputedStyle(s.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([l,c])=>{s.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function dr(t){return typeof t=="function"}let ws;function Qh(){ws=void 0}const be={now:()=>(ws===void 0&&be.set(Q.isProcessing||uh.useManualTiming?Q.timestamp:performance.now()),ws),set:t=>{ws=t,queueMicrotask(Qh)}},ai=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&($e.test(t)||t==="0")&&!t.startsWith("url("));function Yh(t){const e=t[0];if(t.length===1)return!0;for(let s=0;s<t.length;s++)if(t[s]!==e)return!0}function Xh(t,e,s,n){const r=t[0];if(r===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],i=ai(r,e),a=ai(o,e);return!i||!a?!1:Yh(t)||(s==="spring"||dr(s))&&n}const Zh=40;class el{constructor({autoplay:e=!0,delay:s=0,type:n="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:i="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=be.now(),this.options={autoplay:e,delay:s,type:n,repeat:r,repeatDelay:o,repeatType:i,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Zh?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Eh(),this._resolved}onKeyframesResolved(e,s){this.resolvedAt=be.now(),this.hasAttemptedResolve=!0;const{name:n,type:r,velocity:o,delay:i,onComplete:a,onUpdate:l,isGenerator:c}=this.options;if(!c&&!Xh(e,n,r,o))if(i)this.options.duration=0;else{l==null||l(Fs(e,this.options,s)),a==null||a(),this.resolveFinishedPromise();return}const u=this.initPlayback(e,s);u!==!1&&(this._resolved={keyframes:e,finalKeyframe:s,...u},this.onPostResolved())}onPostResolved(){}then(e,s){return this.currentFinishedPromise.then(e,s)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}const Lt=(t,e,s)=>{const n=e-t;return n===0?1:(s-t)/n},tl=(t,e,s=10)=>{let n="";const r=Math.max(Math.round(e/s),2);for(let o=0;o<r;o++)n+=t(Lt(0,r-1,o))+", ";return`linear(${n.substring(0,n.length-2)})`};function sl(t,e){return e?t*(1e3/e):0}const Jh=5;function nl(t,e,s){const n=Math.max(e-Jh,0);return sl(s-t(n),e-n)}const z={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Ys=.001;function ef({duration:t=z.duration,bounce:e=z.bounce,velocity:s=z.velocity,mass:n=z.mass}){let r,o,i=1-e;i=Me(z.minDamping,z.maxDamping,i),t=Me(z.minDuration,z.maxDuration,Re(t)),i<1?(r=c=>{const u=c*i,d=u*t,f=u-s,m=Nn(c,i),x=Math.exp(-d);return Ys-f/m*x},o=c=>{const d=c*i*t,f=d*s+s,m=Math.pow(i,2)*Math.pow(c,2)*t,x=Math.exp(-d),y=Nn(Math.pow(c,2),i);return(-r(c)+Ys>0?-1:1)*((f-m)*x)/y}):(r=c=>{const u=Math.exp(-c*t),d=(c-s)*t+1;return-Ys+u*d},o=c=>{const u=Math.exp(-c*t),d=(s-c)*(t*t);return u*d});const a=5/t,l=sf(r,o,a);if(t=je(t),isNaN(l))return{stiffness:z.stiffness,damping:z.damping,duration:t};{const c=Math.pow(l,2)*n;return{stiffness:c,damping:i*2*Math.sqrt(n*c),duration:t}}}const tf=12;function sf(t,e,s){let n=s;for(let r=1;r<tf;r++)n=n-t(n)/e(n);return n}function Nn(t,e){return t*Math.sqrt(1-e*e)}const Dn=2e4;function rl(t){let e=0;const s=50;let n=t.next(e);for(;!n.done&&e<Dn;)e+=s,n=t.next(e);return e>=Dn?1/0:e}const nf=["duration","bounce"],rf=["stiffness","damping","mass"];function li(t,e){return e.some(s=>t[s]!==void 0)}function of(t){let e={velocity:z.velocity,stiffness:z.stiffness,damping:z.damping,mass:z.mass,isResolvedFromDuration:!1,...t};if(!li(t,rf)&&li(t,nf))if(t.visualDuration){const s=t.visualDuration,n=2*Math.PI/(s*1.2),r=n*n,o=2*Me(.05,1,1-t.bounce)*Math.sqrt(r);e={...e,mass:z.mass,stiffness:r,damping:o}}else{const s=ef(t);e={...e,...s,mass:z.mass},e.isResolvedFromDuration=!0}return e}function il(t=z.visualDuration,e=z.bounce){const s=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:n,restDelta:r}=s;const o=s.keyframes[0],i=s.keyframes[s.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:d,velocity:f,isResolvedFromDuration:m}=of({...s,velocity:-Re(s.velocity||0)}),x=f||0,y=c/(2*Math.sqrt(l*u)),v=i-o,b=Re(Math.sqrt(l/u)),w=Math.abs(v)<5;n||(n=w?z.restSpeed.granular:z.restSpeed.default),r||(r=w?z.restDelta.granular:z.restDelta.default);let T;if(y<1){const S=Nn(b,y);T=k=>{const V=Math.exp(-y*b*k);return i-V*((x+y*b*v)/S*Math.sin(S*k)+v*Math.cos(S*k))}}else if(y===1)T=S=>i-Math.exp(-b*S)*(v+(x+b*v)*S);else{const S=b*Math.sqrt(y*y-1);T=k=>{const V=Math.exp(-y*b*k),C=Math.min(S*k,300);return i-V*((x+y*b*v)*Math.sinh(C)+S*v*Math.cosh(C))/S}}const A={calculatedDuration:m&&d||null,next:S=>{const k=T(S);if(m)a.done=S>=d;else{let V=0;y<1&&(V=S===0?je(x):nl(T,S,k));const C=Math.abs(V)<=n,M=Math.abs(i-k)<=r;a.done=C&&M}return a.value=a.done?i:k,a},toString:()=>{const S=Math.min(rl(A),Dn),k=tl(V=>A.next(S*V).value,S,30);return S+"ms "+k}};return A}function ci({keyframes:t,velocity:e=0,power:s=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:o=500,modifyTarget:i,min:a,max:l,restDelta:c=.5,restSpeed:u}){const d=t[0],f={done:!1,value:d},m=C=>a!==void 0&&C<a||l!==void 0&&C>l,x=C=>a===void 0?l:l===void 0||Math.abs(a-C)<Math.abs(l-C)?a:l;let y=s*e;const v=d+y,b=i===void 0?v:i(v);b!==v&&(y=b-d);const w=C=>-y*Math.exp(-C/n),T=C=>b+w(C),A=C=>{const M=w(C),U=T(C);f.done=Math.abs(M)<=c,f.value=f.done?b:U};let S,k;const V=C=>{m(f.value)&&(S=C,k=il({keyframes:[f.value,x(f.value)],velocity:nl(T,C,f.value),damping:r,stiffness:o,restDelta:c,restSpeed:u}))};return V(0),{calculatedDuration:null,next:C=>{let M=!1;return!k&&S===void 0&&(M=!0,A(C),V(C)),S!==void 0&&C>=S?k.next(C-S):(!M&&A(C),f)}}}const af=ls(.42,0,1,1),lf=ls(0,0,.58,1),ol=ls(.42,0,.58,1),cf=t=>Array.isArray(t)&&typeof t[0]!="number",hr=t=>Array.isArray(t)&&typeof t[0]=="number",ui={linear:J,easeIn:af,easeInOut:ol,easeOut:lf,circIn:rr,circInOut:Ia,circOut:La,backIn:nr,backInOut:Va,backOut:Da,anticipate:Oa},di=t=>{if(hr(t)){jn(t.length===4);const[e,s,n,r]=t;return ls(e,s,n,r)}else if(typeof t=="string")return jn(ui[t]!==void 0),ui[t];return t},uf=(t,e)=>s=>e(t(s)),He=(...t)=>t.reduce(uf),_=(t,e,s)=>t+(e-t)*s;function Xs(t,e,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?t+(e-t)*6*s:s<1/2?e:s<2/3?t+(e-t)*(2/3-s)*6:t}function df({hue:t,saturation:e,lightness:s,alpha:n}){t/=360,e/=100,s/=100;let r=0,o=0,i=0;if(!e)r=o=i=s;else{const a=s<.5?s*(1+e):s+e-s*e,l=2*s-a;r=Xs(l,a,t+1/3),o=Xs(l,a,t),i=Xs(l,a,t-1/3)}return{red:Math.round(r*255),green:Math.round(o*255),blue:Math.round(i*255),alpha:n}}function As(t,e){return s=>s>0?e:t}const Zs=(t,e,s)=>{const n=t*t,r=s*(e*e-n)+n;return r<0?0:Math.sqrt(r)},hf=[Mn,Je,ft],ff=t=>hf.find(e=>e.test(t));function hi(t){const e=ff(t);if(!e)return!1;let s=e.parse(t);return e===ft&&(s=df(s)),s}const fi=(t,e)=>{const s=hi(t),n=hi(e);if(!s||!n)return As(t,e);const r={...s};return o=>(r.red=Zs(s.red,n.red,o),r.green=Zs(s.green,n.green,o),r.blue=Zs(s.blue,n.blue,o),r.alpha=_(s.alpha,n.alpha,o),Je.transform(r))},Vn=new Set(["none","hidden"]);function pf(t,e){return Vn.has(t)?s=>s<=0?t:e:s=>s>=1?e:t}function mf(t,e){return s=>_(t,e,s)}function fr(t){return typeof t=="number"?mf:typeof t=="string"?ir(t)?As:X.test(t)?fi:vf:Array.isArray(t)?al:typeof t=="object"?X.test(t)?fi:yf:As}function al(t,e){const s=[...t],n=s.length,r=t.map((o,i)=>fr(o)(o,e[i]));return o=>{for(let i=0;i<n;i++)s[i]=r[i](o);return s}}function yf(t,e){const s={...t,...e},n={};for(const r in s)t[r]!==void 0&&e[r]!==void 0&&(n[r]=fr(t[r])(t[r],e[r]));return r=>{for(const o in n)s[o]=n[o](r);return s}}function gf(t,e){var s;const n=[],r={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const i=e.types[o],a=t.indexes[i][r[i]],l=(s=t.values[a])!==null&&s!==void 0?s:0;n[o]=l,r[i]++}return n}const vf=(t,e)=>{const s=$e.createTransformer(e),n=ts(t),r=ts(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?Vn.has(t)&&!r.values.length||Vn.has(e)&&!n.values.length?pf(t,e):He(al(gf(n,r),r.values),s):As(t,e)};function ll(t,e,s){return typeof t=="number"&&typeof e=="number"&&typeof s=="number"?_(t,e,s):fr(t)(t,e)}function xf(t,e,s){const n=[],r=s||ll,o=t.length-1;for(let i=0;i<o;i++){let a=r(t[i],t[i+1]);if(e){const l=Array.isArray(e)?e[i]||J:e;a=He(l,a)}n.push(a)}return n}function bf(t,e,{clamp:s=!0,ease:n,mixer:r}={}){const o=t.length;if(jn(o===e.length),o===1)return()=>e[0];if(o===2&&t[0]===t[1])return()=>e[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const i=xf(e,n,r),a=i.length,l=c=>{let u=0;if(a>1)for(;u<t.length-2&&!(c<t[u+1]);u++);const d=Lt(t[u],t[u+1],c);return i[u](d)};return s?c=>l(Me(t[0],t[o-1],c)):l}function wf(t,e){const s=t[t.length-1];for(let n=1;n<=e;n++){const r=Lt(0,e,n);t.push(_(s,1,r))}}function Tf(t){const e=[0];return wf(e,t.length-1),e}function Pf(t,e){return t.map(s=>s*e)}function Sf(t,e){return t.map(()=>e||ol).splice(0,t.length-1)}function js({duration:t=300,keyframes:e,times:s,ease:n="easeInOut"}){const r=cf(n)?n.map(di):di(n),o={done:!1,value:e[0]},i=Pf(s&&s.length===e.length?s:Tf(e),t),a=bf(i,e,{ease:Array.isArray(r)?r:Sf(e,r)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const Cf=t=>{const e=({timestamp:s})=>t(s);return{start:()=>F.update(e,!0),stop:()=>qe(e),now:()=>Q.isProcessing?Q.timestamp:be.now()}},Af={decay:ci,inertia:ci,tween:js,keyframes:js,spring:il},jf=t=>t/100;class pr extends el{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:s,motionValue:n,element:r,keyframes:o}=this.options,i=(r==null?void 0:r.KeyframeResolver)||or,a=(l,c)=>this.onKeyframesResolved(l,c);this.resolver=new i(o,a,s,n,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:i=0}=this.options,a=dr(s)?s:Af[s]||js;let l,c;a!==js&&typeof e[0]!="number"&&(l=He(jf,ll(e[0],e[1])),e=[0,100]);const u=a({...this.options,keyframes:e});o==="mirror"&&(c=a({...this.options,keyframes:[...e].reverse(),velocity:-i})),u.calculatedDuration===null&&(u.calculatedDuration=rl(u));const{calculatedDuration:d}=u,f=d+r,m=f*(n+1)-r;return{generator:u,mirroredGenerator:c,mapPercentToKeyframes:l,calculatedDuration:d,resolvedDuration:f,totalDuration:m}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,s=!1){const{resolved:n}=this;if(!n){const{keyframes:C}=this.options;return{done:!0,value:C[C.length-1]}}const{finalKeyframe:r,generator:o,mirroredGenerator:i,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:c,totalDuration:u,resolvedDuration:d}=n;if(this.startTime===null)return o.next(0);const{delay:f,repeat:m,repeatType:x,repeatDelay:y,onUpdate:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),s?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const b=this.currentTime-f*(this.speed>=0?1:-1),w=this.speed>=0?b<0:b>u;this.currentTime=Math.max(b,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let T=this.currentTime,A=o;if(m){const C=Math.min(this.currentTime,u)/d;let M=Math.floor(C),U=C%1;!U&&C>=1&&(U=1),U===1&&M--,M=Math.min(M,m+1),!!(M%2)&&(x==="reverse"?(U=1-U,y&&(U-=y/d)):x==="mirror"&&(A=i)),T=Me(0,1,U)*d}const S=w?{done:!1,value:l[0]}:A.next(T);a&&(S.value=a(S.value));let{done:k}=S;!w&&c!==null&&(k=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const V=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&k);return V&&r!==void 0&&(S.value=Fs(l,this.options,r)),v&&v(S.value),V&&this.finish(),S}get duration(){const{resolved:e}=this;return e?Re(e.calculatedDuration):0}get time(){return Re(this.currentTime)}set time(e){e=je(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const s=this.playbackSpeed!==e;this.playbackSpeed=e,s&&(this.time=Re(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=Cf,onPlay:s,startTime:n}=this.options;this.driver||(this.driver=e(o=>this.tick(o))),s&&s();const r=this.driver.now();this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=r):this.startTime=n??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const Rf=new Set(["opacity","clipPath","filter","transform"]);function mr(t){let e;return()=>(e===void 0&&(e=t()),e)}const Ef={linearEasing:void 0};function Mf(t,e){const s=mr(t);return()=>{var n;return(n=Ef[e])!==null&&n!==void 0?n:s()}}const Rs=Mf(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing");function cl(t){return!!(typeof t=="function"&&Rs()||!t||typeof t=="string"&&(t in On||Rs())||hr(t)||Array.isArray(t)&&t.every(cl))}const qt=([t,e,s,n])=>`cubic-bezier(${t}, ${e}, ${s}, ${n})`,On={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:qt([0,.65,.55,1]),circOut:qt([.55,0,1,.45]),backIn:qt([.31,.01,.66,-.59]),backOut:qt([.33,1.53,.69,.99])};function ul(t,e){if(t)return typeof t=="function"&&Rs()?tl(t,e):hr(t)?qt(t):Array.isArray(t)?t.map(s=>ul(s,e)||On.easeOut):On[t]}function kf(t,e,s,{delay:n=0,duration:r=300,repeat:o=0,repeatType:i="loop",ease:a="easeInOut",times:l}={}){const c={[e]:s};l&&(c.offset=l);const u=ul(a,r);return Array.isArray(u)&&(c.easing=u),t.animate(c,{delay:n,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:o+1,direction:i==="reverse"?"alternate":"normal"})}function pi(t,e){t.timeline=e,t.onfinish=null}const Nf=mr(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Es=10,Df=2e4;function Vf(t){return dr(t.type)||t.type==="spring"||!cl(t.ease)}function Of(t,e){const s=new pr({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let n={done:!1,value:t[0]};const r=[];let o=0;for(;!n.done&&o<Df;)n=s.sample(o),r.push(n.value),o+=Es;return{times:void 0,keyframes:r,duration:o-Es,ease:"linear"}}const dl={anticipate:Oa,backInOut:Va,circInOut:Ia};function Lf(t){return t in dl}class mi extends el{constructor(e){super(e);const{name:s,motionValue:n,element:r,keyframes:o}=this.options;this.resolver=new Ja(o,(i,a)=>this.onKeyframesResolved(i,a),s,n,r),this.resolver.scheduleResolve()}initPlayback(e,s){var n;let{duration:r=300,times:o,ease:i,type:a,motionValue:l,name:c,startTime:u}=this.options;if(!(!((n=l.owner)===null||n===void 0)&&n.current))return!1;if(typeof i=="string"&&Rs()&&Lf(i)&&(i=dl[i]),Vf(this.options)){const{onComplete:f,onUpdate:m,motionValue:x,element:y,...v}=this.options,b=Of(e,v);e=b.keyframes,e.length===1&&(e[1]=e[0]),r=b.duration,o=b.times,i=b.ease,a="keyframes"}const d=kf(l.owner.current,c,e,{...this.options,duration:r,times:o,ease:i});return d.startTime=u??this.calcStartTime(),this.pendingTimeline?(pi(d,this.pendingTimeline),this.pendingTimeline=void 0):d.onfinish=()=>{const{onComplete:f}=this.options;l.set(Fs(e,this.options,s)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:d,duration:r,times:o,type:a,ease:i,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:s}=e;return Re(s)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:s}=e;return Re(s.currentTime||0)}set time(e){const{resolved:s}=this;if(!s)return;const{animation:n}=s;n.currentTime=je(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:s}=e;return s.playbackRate}set speed(e){const{resolved:s}=this;if(!s)return;const{animation:n}=s;n.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:s}=e;return s.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:s}=e;return s.startTime}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:s}=this;if(!s)return J;const{animation:n}=s;pi(n,e)}return J}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:s}=e;s.playState==="finished"&&this.updateFinishedPromise(),s.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:s}=e;s.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:s,keyframes:n,duration:r,type:o,ease:i,times:a}=e;if(s.playState==="idle"||s.playState==="finished")return;if(this.time){const{motionValue:c,onUpdate:u,onComplete:d,element:f,...m}=this.options,x=new pr({...m,keyframes:n,duration:r,type:o,ease:i,times:a,isGenerator:!0}),y=je(this.time);c.setWithVelocity(x.sample(y-Es).value,x.sample(y).value,Es)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:s,name:n,repeatDelay:r,repeatType:o,damping:i,type:a}=e;return Nf()&&n&&Rf.has(n)&&s&&s.owner&&s.owner.current instanceof HTMLElement&&!s.owner.getProps().onUpdate&&!r&&o!=="mirror"&&i!==0&&a!=="inertia"}}const If=mr(()=>window.ScrollTimeline!==void 0);class Ff{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}then(e,s){return Promise.all(this.animations).then(e).catch(s)}getAll(e){return this.animations[0][e]}setAll(e,s){for(let n=0;n<this.animations.length;n++)this.animations[n][e]=s}attachTimeline(e,s){const n=this.animations.map(r=>If()&&r.attachTimeline?r.attachTimeline(e):s(r));return()=>{n.forEach((r,o)=>{r&&r(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let s=0;s<this.animations.length;s++)e=Math.max(e,this.animations[s].duration);return e}runAll(e){this.animations.forEach(s=>s[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function Bf({when:t,delay:e,delayChildren:s,staggerChildren:n,staggerDirection:r,repeat:o,repeatType:i,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const yr=(t,e,s,n={},r,o)=>i=>{const a=sr(n,t)||{},l=a.delay||n.delay||0;let{elapsed:c=0}=n;c=c-je(l);let u={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{i(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:r};Bf(a)||(u={...u,...ch(t,u)}),u.duration&&(u.duration=je(u.duration)),u.repeatDelay&&(u.repeatDelay=je(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let d=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(d=!0)),d&&!o&&e.get()!==void 0){const f=Fs(u.keyframes,a);if(f!==void 0)return F.update(()=>{u.onUpdate(f),u.onComplete()}),new Ff([])}return!o&&mi.supports(u)?new mi(u):new pr(u)},Uf=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),_f=t=>An(t)?t[t.length-1]||0:t;function gr(t,e){t.indexOf(e)===-1&&t.push(e)}function vr(t,e){const s=t.indexOf(e);s>-1&&t.splice(s,1)}class xr{constructor(){this.subscriptions=[]}add(e){return gr(this.subscriptions,e),()=>vr(this.subscriptions,e)}notify(e,s,n){const r=this.subscriptions.length;if(r)if(r===1)this.subscriptions[0](e,s,n);else for(let o=0;o<r;o++){const i=this.subscriptions[o];i&&i(e,s,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const yi=30,Kf=t=>!isNaN(parseFloat(t));class zf{constructor(e,s={}){this.version="11.13.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(n,r=!0)=>{const o=be.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(n),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),r&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=s.owner}setCurrent(e){this.current=e,this.updatedAt=be.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Kf(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,s){this.events[e]||(this.events[e]=new xr);const n=this.events[e].add(s);return e==="change"?()=>{n(),F.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,s){this.passiveEffect=e,this.stopPassiveEffect=s}set(e,s=!0){!s||!this.passiveEffect?this.updateAndNotify(e,s):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,s,n){this.set(s),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,s=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=be.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>yi)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,yi);return sl(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(e){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=e(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ss(t,e){return new zf(t,e)}function Hf(t,e,s){t.hasValue(e)?t.getValue(e).set(s):t.addValue(e,ss(s))}function Gf(t,e){const s=Is(t,e);let{transitionEnd:n={},transition:r={},...o}=s||{};o={...o,...n};for(const i in o){const a=_f(o[i]);Hf(t,i,a)}}const br=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),qf="framerAppearId",hl="data-"+br(qf);function fl(t){return t.props[hl]}const Z=t=>!!(t&&t.getVelocity);function $f(t){return!!(Z(t)&&t.add)}function Ln(t,e){const s=t.getValue("willChange");if($f(s))return s.add(e)}function Wf({protectedKeys:t,needsAnimating:e},s){const n=t.hasOwnProperty(s)&&e[s]!==!0;return e[s]=!1,n}function pl(t,e,{delay:s=0,transitionOverride:n,type:r}={}){var o;let{transition:i=t.getDefaultTransition(),transitionEnd:a,...l}=e;n&&(i=n);const c=[],u=r&&t.animationState&&t.animationState.getState()[r];for(const d in l){const f=t.getValue(d,(o=t.latestValues[d])!==null&&o!==void 0?o:null),m=l[d];if(m===void 0||u&&Wf(u,d))continue;const x={delay:s,...sr(i||{},d)};let y=!1;if(window.MotionHandoffAnimation){const b=fl(t);if(b){const w=window.MotionHandoffAnimation(b,d,F);w!==null&&(x.startTime=w,y=!0)}}Ln(t,d),f.start(yr(d,f,m,t.shouldReduceMotion&&ut.has(d)?{type:!1}:x,t,y));const v=f.animation;v&&c.push(v)}return a&&Promise.all(c).then(()=>{F.update(()=>{a&&Gf(t,a)})}),c}function In(t,e,s={}){var n;const r=Is(t,e,s.type==="exit"?(n=t.presenceContext)===null||n===void 0?void 0:n.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=r||{};s.transitionOverride&&(o=s.transitionOverride);const i=r?()=>Promise.all(pl(t,r,s)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:d,staggerDirection:f}=o;return Qf(t,e,u+c,d,f,s)}:()=>Promise.resolve(),{when:l}=o;if(l){const[c,u]=l==="beforeChildren"?[i,a]:[a,i];return c().then(()=>u())}else return Promise.all([i(),a(s.delay)])}function Qf(t,e,s=0,n=0,r=1,o){const i=[],a=(t.variantChildren.size-1)*n,l=r===1?(c=0)=>c*n:(c=0)=>a-c*n;return Array.from(t.variantChildren).sort(Yf).forEach((c,u)=>{c.notify("AnimationStart",e),i.push(In(c,e,{...o,delay:s+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(i)}function Yf(t,e){return t.sortNodePosition(e)}function Xf(t,e,s={}){t.notify("AnimationStart",e);let n;if(Array.isArray(e)){const r=e.map(o=>In(t,o,s));n=Promise.all(r)}else if(typeof e=="string")n=In(t,e,s);else{const r=typeof e=="function"?Is(t,e,s.custom):e;n=Promise.all(pl(t,r,s))}return n.then(()=>{t.notify("AnimationComplete",e)})}const Zf=tr.length;function ml(t){if(!t)return;if(!t.isControllingVariants){const s=t.parent?ml(t.parent)||{}:{};return t.props.initial!==void 0&&(s.initial=t.props.initial),s}const e={};for(let s=0;s<Zf;s++){const n=tr[s],r=t.props[n];(Jt(r)||r===!1)&&(e[n]=r)}return e}const Jf=[...er].reverse(),ep=er.length;function tp(t){return e=>Promise.all(e.map(({animation:s,options:n})=>Xf(t,s,n)))}function sp(t){let e=tp(t),s=gi(),n=!0;const r=l=>(c,u)=>{var d;const f=Is(t,u,l==="exit"?(d=t.presenceContext)===null||d===void 0?void 0:d.custom:void 0);if(f){const{transition:m,transitionEnd:x,...y}=f;c={...c,...y,...x}}return c};function o(l){e=l(t)}function i(l){const{props:c}=t,u=ml(t.parent)||{},d=[],f=new Set;let m={},x=1/0;for(let v=0;v<ep;v++){const b=Jf[v],w=s[b],T=c[b]!==void 0?c[b]:u[b],A=Jt(T),S=b===l?w.isActive:null;S===!1&&(x=v);let k=T===u[b]&&T!==c[b]&&A;if(k&&n&&t.manuallyAnimateOnMount&&(k=!1),w.protectedKeys={...m},!w.isActive&&S===null||!T&&!w.prevProp||Ls(T)||typeof T=="boolean")continue;const V=np(w.prevProp,T);let C=V||b===l&&w.isActive&&!k&&A||v>x&&A,M=!1;const U=Array.isArray(T)?T:[T];let W=U.reduce(r(b),{});S===!1&&(W={});const{prevResolvedValues:L={}}=w,hs={...L,...W},Qe=$=>{C=!0,f.has($)&&(M=!0,f.delete($)),w.needsAnimating[$]=!0;const re=t.getValue($);re&&(re.liveStyle=!1)};for(const $ in hs){const re=W[$],we=L[$];if(m.hasOwnProperty($))continue;let dt=!1;An(re)&&An(we)?dt=!Ra(re,we):dt=re!==we,dt?re!=null?Qe($):f.add($):re!==void 0&&f.has($)?Qe($):w.protectedKeys[$]=!0}w.prevProp=T,w.prevResolvedValues=W,w.isActive&&(m={...m,...W}),n&&t.blockInitialAnimation&&(C=!1),C&&(!(k&&V)||M)&&d.push(...U.map($=>({animation:$,options:{type:b}})))}if(f.size){const v={};f.forEach(b=>{const w=t.getBaseTarget(b),T=t.getValue(b);T&&(T.liveStyle=!0),v[b]=w??null}),d.push({animation:v})}let y=!!d.length;return n&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(d):Promise.resolve()}function a(l,c){var u;if(s[l].isActive===c)return Promise.resolve();(u=t.variantChildren)===null||u===void 0||u.forEach(f=>{var m;return(m=f.animationState)===null||m===void 0?void 0:m.setActive(l,c)}),s[l].isActive=c;const d=i(l);for(const f in s)s[f].protectedKeys={};return d}return{animateChanges:i,setActive:a,setAnimateFunction:o,getState:()=>s,reset:()=>{s=gi(),n=!0}}}function np(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Ra(e,t):!1}function Ye(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function gi(){return{animate:Ye(!0),whileInView:Ye(),whileHover:Ye(),whileTap:Ye(),whileDrag:Ye(),whileFocus:Ye(),exit:Ye()}}class We{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rp extends We{constructor(e){super(e),e.animationState||(e.animationState=sp(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Ls(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:s}=this.node.prevProps||{};e!==s&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)===null||e===void 0||e.call(this)}}let ip=0;class op extends We{constructor(){super(...arguments),this.id=ip++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:s}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;const r=this.node.animationState.setActive("exit",!e);s&&!e&&r.then(()=>s(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const ap={animation:{Feature:rp},exit:{Feature:op}};function yl(t,e,s){var n;if(t instanceof Element)return[t];if(typeof t=="string"){let r=document;const o=(n=void 0)!==null&&n!==void 0?n:r.querySelectorAll(t);return o?Array.from(o):[]}return Array.from(t)}const he={x:!1,y:!1};function gl(){return he.x||he.y}function vi(t){return e=>{e.pointerType==="touch"||gl()||t(e)}}function lp(t,e,s={}){const n=new AbortController,r={passive:!0,...s,signal:n.signal},o=vi(i=>{const{target:a}=i,l=e(i);if(!l||!a)return;const c=vi(u=>{l(u),a.removeEventListener("pointerleave",c)});a.addEventListener("pointerleave",c,r)});return yl(t).forEach(i=>{i.addEventListener("pointerenter",o,r)}),()=>n.abort()}function cp(t){return t==="x"||t==="y"?he[t]?null:(he[t]=!0,()=>{he[t]=!1}):he.x||he.y?null:(he.x=he.y=!0,()=>{he.x=he.y=!1})}const vl=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function us(t){return{point:{x:t.pageX,y:t.pageY}}}const up=t=>e=>vl(e)&&t(e,us(e));function Ae(t,e,s,n={passive:!0}){return t.addEventListener(e,s,n),()=>t.removeEventListener(e,s)}function Ge(t,e,s,n){return Ae(t,e,up(s),n)}const xi=(t,e)=>Math.abs(t-e);function dp(t,e){const s=xi(t.x,e.x),n=xi(t.y,e.y);return Math.sqrt(s**2+n**2)}class xl{constructor(e,s,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=en(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=dp(d.offset,{x:0,y:0})>=3;if(!f&&!m)return;const{point:x}=d,{timestamp:y}=Q;this.history.push({...x,timestamp:y});const{onStart:v,onMove:b}=this.handlers;f||(v&&v(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),b&&b(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Js(f,this.transformPagePoint),F.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:m,onSessionEnd:x,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=en(d.type==="pointercancel"?this.lastMoveEventInfo:Js(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,v),x&&x(d,v)},!vl(e))return;this.dragSnapToOrigin=o,this.handlers=s,this.transformPagePoint=n,this.contextWindow=r||window;const i=us(e),a=Js(i,this.transformPagePoint),{point:l}=a,{timestamp:c}=Q;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=s;u&&u(e,en(a,this.history)),this.removeListeners=He(Ge(this.contextWindow,"pointermove",this.handlePointerMove),Ge(this.contextWindow,"pointerup",this.handlePointerUp),Ge(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),qe(this.updatePoint)}}function Js(t,e){return e?{point:e(t.point)}:t}function bi(t,e){return{x:t.x-e.x,y:t.y-e.y}}function en({point:t},e){return{point:t,delta:bi(t,bl(e)),offset:bi(t,hp(e)),velocity:fp(e,.1)}}function hp(t){return t[0]}function bl(t){return t[t.length-1]}function fp(t,e){if(t.length<2)return{x:0,y:0};let s=t.length-1,n=null;const r=bl(t);for(;s>=0&&(n=t[s],!(r.timestamp-n.timestamp>je(e)));)s--;if(!n)return{x:0,y:0};const o=Re(r.timestamp-n.timestamp);if(o===0)return{x:0,y:0};const i={x:(r.x-n.x)/o,y:(r.y-n.y)/o};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function pt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}const wl=1e-4,pp=1-wl,mp=1+wl,Tl=.01,yp=0-Tl,gp=0+Tl;function oe(t){return t.max-t.min}function vp(t,e,s){return Math.abs(t-e)<=s}function wi(t,e,s,n=.5){t.origin=n,t.originPoint=_(e.min,e.max,t.origin),t.scale=oe(s)/oe(e),t.translate=_(s.min,s.max,t.origin)-t.originPoint,(t.scale>=pp&&t.scale<=mp||isNaN(t.scale))&&(t.scale=1),(t.translate>=yp&&t.translate<=gp||isNaN(t.translate))&&(t.translate=0)}function Yt(t,e,s,n){wi(t.x,e.x,s.x,n?n.originX:void 0),wi(t.y,e.y,s.y,n?n.originY:void 0)}function Ti(t,e,s){t.min=s.min+e.min,t.max=t.min+oe(e)}function xp(t,e,s){Ti(t.x,e.x,s.x),Ti(t.y,e.y,s.y)}function Pi(t,e,s){t.min=e.min-s.min,t.max=t.min+oe(e)}function Xt(t,e,s){Pi(t.x,e.x,s.x),Pi(t.y,e.y,s.y)}function bp(t,{min:e,max:s},n){return e!==void 0&&t<e?t=n?_(e,t,n.min):Math.max(t,e):s!==void 0&&t>s&&(t=n?_(s,t,n.max):Math.min(t,s)),t}function Si(t,e,s){return{min:e!==void 0?t.min+e:void 0,max:s!==void 0?t.max+s-(t.max-t.min):void 0}}function wp(t,{top:e,left:s,bottom:n,right:r}){return{x:Si(t.x,s,r),y:Si(t.y,e,n)}}function Ci(t,e){let s=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([s,n]=[n,s]),{min:s,max:n}}function Tp(t,e){return{x:Ci(t.x,e.x),y:Ci(t.y,e.y)}}function Pp(t,e){let s=.5;const n=oe(t),r=oe(e);return r>n?s=Lt(e.min,e.max-n,t.min):n>r&&(s=Lt(t.min,t.max-r,e.min)),Me(0,1,s)}function Sp(t,e){const s={};return e.min!==void 0&&(s.min=e.min-t.min),e.max!==void 0&&(s.max=e.max-t.min),s}const Fn=.35;function Cp(t=Fn){return t===!1?t=0:t===!0&&(t=Fn),{x:Ai(t,"left","right"),y:Ai(t,"top","bottom")}}function Ai(t,e,s){return{min:ji(t,e),max:ji(t,s)}}function ji(t,e){return typeof t=="number"?t:t[e]||0}const Ri=()=>({translate:0,scale:1,origin:0,originPoint:0}),mt=()=>({x:Ri(),y:Ri()}),Ei=()=>({min:0,max:0}),H=()=>({x:Ei(),y:Ei()});function ce(t){return[t("x"),t("y")]}function Pl({top:t,left:e,right:s,bottom:n}){return{x:{min:e,max:s},y:{min:t,max:n}}}function Ap({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function jp(t,e){if(!e)return t;const s=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:s.y,left:s.x,bottom:n.y,right:n.x}}function tn(t){return t===void 0||t===1}function Bn({scale:t,scaleX:e,scaleY:s}){return!tn(t)||!tn(e)||!tn(s)}function Xe(t){return Bn(t)||Sl(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Sl(t){return Mi(t.x)||Mi(t.y)}function Mi(t){return t&&t!=="0%"}function Ms(t,e,s){const n=t-s,r=e*n;return s+r}function ki(t,e,s,n,r){return r!==void 0&&(t=Ms(t,r,n)),Ms(t,s,n)+e}function Un(t,e=0,s=1,n,r){t.min=ki(t.min,e,s,n,r),t.max=ki(t.max,e,s,n,r)}function Cl(t,{x:e,y:s}){Un(t.x,e.translate,e.scale,e.originPoint),Un(t.y,s.translate,s.scale,s.originPoint)}const Ni=.999999999999,Di=1.0000000000001;function Rp(t,e,s,n=!1){const r=s.length;if(!r)return;e.x=e.y=1;let o,i;for(let a=0;a<r;a++){o=s[a],i=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(n&&o.options.layoutScroll&&o.scroll&&o!==o.root&&gt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),i&&(e.x*=i.x.scale,e.y*=i.y.scale,Cl(t,i)),n&&Xe(o.latestValues)&&gt(t,o.latestValues))}e.x<Di&&e.x>Ni&&(e.x=1),e.y<Di&&e.y>Ni&&(e.y=1)}function yt(t,e){t.min=t.min+e,t.max=t.max+e}function Vi(t,e,s,n,r=.5){const o=_(t.min,t.max,r);Un(t,e,s,o,n)}function gt(t,e){Vi(t.x,e.x,e.scaleX,e.scale,e.originX),Vi(t.y,e.y,e.scaleY,e.scale,e.originY)}function Al(t,e){return Pl(jp(t.getBoundingClientRect(),e))}function Ep(t,e,s){const n=Al(t,s),{scroll:r}=e;return r&&(yt(n.x,r.offset.x),yt(n.y,r.offset.y)),n}const jl=({current:t})=>t?t.ownerDocument.defaultView:null,Mp=new WeakMap;class kp{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=H(),this.visualElement=e}start(e,{snapToCursor:s=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&n.isPresent===!1)return;const r=u=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(us(u).point)},o=(u,d)=>{const{drag:f,dragPropagation:m,onDragStart:x}=this.getProps();if(f&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=cp(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ce(v=>{let b=this.getAxisMotionValue(v).get()||0;if(xe.test(b)){const{projection:w}=this.visualElement;if(w&&w.layout){const T=w.layout.layoutBox[v];T&&(b=oe(T)*(parseFloat(b)/100))}}this.originPoint[v]=b}),x&&F.postRender(()=>x(u,d)),Ln(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},i=(u,d)=>{const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:x,onDrag:y}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:v}=d;if(m&&this.currentDirection===null){this.currentDirection=Np(v),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",d.point,v),this.updateAxis("y",d.point,v),this.visualElement.render(),y&&y(u,d)},a=(u,d)=>this.stop(u,d),l=()=>ce(u=>{var d;return this.getAnimationState(u)==="paused"&&((d=this.getAxisMotionValue(u).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new xl(e,{onSessionStart:r,onStart:o,onMove:i,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:jl(this.visualElement)})}stop(e,s){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=s;this.startAnimation(r);const{onDragEnd:o}=this.getProps();o&&F.postRender(()=>o(e,s))}cancel(){this.isDragging=!1;const{projection:e,animationState:s}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(e,s,n){const{drag:r}=this.getProps();if(!n||!gs(e,r,this.currentDirection))return;const o=this.getAxisMotionValue(e);let i=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(i=bp(i,this.constraints[e],this.elastic[e])),o.set(i)}resolveConstraints(){var e;const{dragConstraints:s,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,o=this.constraints;s&&pt(s)?this.constraints||(this.constraints=this.resolveRefConstraints()):s&&r?this.constraints=wp(r.layoutBox,s):this.constraints=!1,this.elastic=Cp(n),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&ce(i=>{this.constraints!==!1&&this.getAxisMotionValue(i)&&(this.constraints[i]=Sp(r.layoutBox[i],this.constraints[i]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:s}=this.getProps();if(!e||!pt(e))return!1;const n=e.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const o=Ep(n,r.root,this.visualElement.getTransformPagePoint());let i=Tp(r.layout.layoutBox,o);if(s){const a=s(Ap(i));this.hasMutatedConstraints=!!a,a&&(i=Pl(a))}return i}startAnimation(e){const{drag:s,dragMomentum:n,dragElastic:r,dragTransition:o,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=ce(u=>{if(!gs(u,s,this.currentDirection))return;let d=l&&l[u]||{};i&&(d={min:0,max:0});const f=r?200:1e6,m=r?40:1e7,x={type:"inertia",velocity:n?e[u]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...d};return this.startAxisValueAnimation(u,x)});return Promise.all(c).then(a)}startAxisValueAnimation(e,s){const n=this.getAxisMotionValue(e);return Ln(this.visualElement,e),n.start(yr(e,n,0,s,this.visualElement,!1))}stopAnimation(){ce(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){ce(e=>{var s;return(s=this.getAxisMotionValue(e).animation)===null||s===void 0?void 0:s.pause()})}getAnimationState(e){var s;return(s=this.getAxisMotionValue(e).animation)===null||s===void 0?void 0:s.state}getAxisMotionValue(e){const s=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps(),r=n[s];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){ce(s=>{const{drag:n}=this.getProps();if(!gs(s,n,this.currentDirection))return;const{projection:r}=this.visualElement,o=this.getAxisMotionValue(s);if(r&&r.layout){const{min:i,max:a}=r.layout.layoutBox[s];o.set(e[s]-_(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:s}=this.getProps(),{projection:n}=this.visualElement;if(!pt(s)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};ce(i=>{const a=this.getAxisMotionValue(i);if(a&&this.constraints!==!1){const l=a.get();r[i]=Pp({min:l,max:l},this.constraints[i])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),ce(i=>{if(!gs(i,e,null))return;const a=this.getAxisMotionValue(i),{min:l,max:c}=this.constraints[i];a.set(_(l,c,r[i]))})}addListeners(){if(!this.visualElement.current)return;Mp.set(this.visualElement,this);const e=this.visualElement.current,s=Ge(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),n=()=>{const{dragConstraints:l}=this.getProps();pt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,o=r.addEventListener("measure",n);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),F.read(n);const i=Ae(window,"resize",()=>this.scalePositionWithinConstraints()),a=r.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(ce(u=>{const d=this.getAxisMotionValue(u);d&&(this.originPoint[u]+=l[u].translate,d.set(d.get()+l[u].translate))}),this.visualElement.render())});return()=>{i(),s(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:o=!1,dragElastic:i=Fn,dragMomentum:a=!0}=e;return{...e,drag:s,dragDirectionLock:n,dragPropagation:r,dragConstraints:o,dragElastic:i,dragMomentum:a}}}function gs(t,e,s){return(e===!0||e===t)&&(s===null||s===t)}function Np(t,e=10){let s=null;return Math.abs(t.y)>e?s="y":Math.abs(t.x)>e&&(s="x"),s}class Dp extends We{constructor(e){super(e),this.removeGroupControls=J,this.removeListeners=J,this.controls=new kp(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||J}unmount(){this.removeGroupControls(),this.removeListeners()}}const Oi=t=>(e,s)=>{t&&F.postRender(()=>t(e,s))};class Vp extends We{constructor(){super(...arguments),this.removePointerDownListener=J}onPointerDown(e){this.session=new xl(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:jl(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:s,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:Oi(e),onStart:Oi(s),onMove:n,onEnd:(o,i)=>{delete this.session,r&&F.postRender(()=>r(o,i))}}}mount(){this.removePointerDownListener=Ge(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const wr=g.createContext(null);function Op(){const t=g.useContext(wr);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:s,register:n}=t,r=g.useId();g.useEffect(()=>n(r),[]);const o=g.useCallback(()=>s&&s(r),[r,s]);return!e&&s?[!1,o]:[!0]}const Rl=g.createContext({}),El=g.createContext({}),Ts={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Li(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Ht={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(R.test(t))t=parseFloat(t);else return t;const s=Li(t,e.target.x),n=Li(t,e.target.y);return`${s}% ${n}%`}},Lp={correct:(t,{treeScale:e,projectionDelta:s})=>{const n=t,r=$e.parse(t);if(r.length>5)return n;const o=$e.createTransformer(t),i=typeof r[0]!="number"?1:0,a=s.x.scale*e.x,l=s.y.scale*e.y;r[0+i]/=a,r[1+i]/=l;const c=_(a,l,.5);return typeof r[2+i]=="number"&&(r[2+i]/=c),typeof r[3+i]=="number"&&(r[3+i]/=c),o(r)}},ks={};function Ip(t){Object.assign(ks,t)}const{schedule:Tr,cancel:Gy}=Ea(queueMicrotask,!1);class Fp extends g.Component{componentDidMount(){const{visualElement:e,layoutGroup:s,switchLayoutGroup:n,layoutId:r}=this.props,{projection:o}=e;Ip(Bp),o&&(s.group&&s.group.add(o),n&&n.register&&r&&n.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Ts.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:s,visualElement:n,drag:r,isPresent:o}=this.props,i=n.projection;return i&&(i.isPresent=o,r||e.layoutDependency!==s||s===void 0?i.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?i.promote():i.relegate()||F.postRender(()=>{const a=i.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Tr.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:s,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Ml(t){const[e,s]=Op(),n=g.useContext(Rl);return h.jsx(Fp,{...t,layoutGroup:n,switchLayoutGroup:g.useContext(El),isPresent:e,safeToRemove:s})}const Bp={borderRadius:{...Ht,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Ht,borderTopRightRadius:Ht,borderBottomLeftRadius:Ht,borderBottomRightRadius:Ht,boxShadow:Lp},kl=["TopLeft","TopRight","BottomLeft","BottomRight"],Up=kl.length,Ii=t=>typeof t=="string"?parseFloat(t):t,Fi=t=>typeof t=="number"||R.test(t);function _p(t,e,s,n,r,o){r?(t.opacity=_(0,s.opacity!==void 0?s.opacity:1,Kp(n)),t.opacityExit=_(e.opacity!==void 0?e.opacity:1,0,zp(n))):o&&(t.opacity=_(e.opacity!==void 0?e.opacity:1,s.opacity!==void 0?s.opacity:1,n));for(let i=0;i<Up;i++){const a=`border${kl[i]}Radius`;let l=Bi(e,a),c=Bi(s,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||Fi(l)===Fi(c)?(t[a]=Math.max(_(Ii(l),Ii(c),n),0),(xe.test(c)||xe.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||s.rotate)&&(t.rotate=_(e.rotate||0,s.rotate||0,n))}function Bi(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Kp=Nl(0,.5,La),zp=Nl(.5,.95,J);function Nl(t,e,s){return n=>n<t?0:n>e?1:s(Lt(t,e,n))}function Ui(t,e){t.min=e.min,t.max=e.max}function le(t,e){Ui(t.x,e.x),Ui(t.y,e.y)}function _i(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Ki(t,e,s,n,r){return t-=e,t=Ms(t,1/s,n),r!==void 0&&(t=Ms(t,1/r,n)),t}function Hp(t,e=0,s=1,n=.5,r,o=t,i=t){if(xe.test(e)&&(e=parseFloat(e),e=_(i.min,i.max,e/100)-i.min),typeof e!="number")return;let a=_(o.min,o.max,n);t===o&&(a-=e),t.min=Ki(t.min,e,s,a,r),t.max=Ki(t.max,e,s,a,r)}function zi(t,e,[s,n,r],o,i){Hp(t,e[s],e[n],e[r],e.scale,o,i)}const Gp=["x","scaleX","originX"],qp=["y","scaleY","originY"];function Hi(t,e,s,n){zi(t.x,e,Gp,s?s.x:void 0,n?n.x:void 0),zi(t.y,e,qp,s?s.y:void 0,n?n.y:void 0)}function Gi(t){return t.translate===0&&t.scale===1}function Dl(t){return Gi(t.x)&&Gi(t.y)}function qi(t,e){return t.min===e.min&&t.max===e.max}function $p(t,e){return qi(t.x,e.x)&&qi(t.y,e.y)}function $i(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Vl(t,e){return $i(t.x,e.x)&&$i(t.y,e.y)}function Wi(t){return oe(t.x)/oe(t.y)}function Qi(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Wp{constructor(){this.members=[]}add(e){gr(this.members,e),e.scheduleRender()}remove(e){if(vr(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(e){const s=this.members.findIndex(r=>e===r);if(s===0)return!1;let n;for(let r=s;r>=0;r--){const o=this.members[r];if(o.isPresent!==!1){n=o;break}}return n?(this.promote(n),!0):!1}promote(e,s){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,s&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;r===!1&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:s,resumingFrom:n}=e;s.onExitComplete&&s.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Qp(t,e,s){let n="";const r=t.x.translate/e.x,o=t.y.translate/e.y,i=(s==null?void 0:s.z)||0;if((r||o||i)&&(n=`translate3d(${r}px, ${o}px, ${i}px) `),(e.x!==1||e.y!==1)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),s){const{transformPerspective:c,rotate:u,rotateX:d,rotateY:f,skewX:m,skewY:x}=s;c&&(n=`perspective(${c}px) ${n}`),u&&(n+=`rotate(${u}deg) `),d&&(n+=`rotateX(${d}deg) `),f&&(n+=`rotateY(${f}deg) `),m&&(n+=`skewX(${m}deg) `),x&&(n+=`skewY(${x}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(n+=`scale(${a}, ${l})`),n||"none"}const Yp=(t,e)=>t.depth-e.depth;class Xp{constructor(){this.children=[],this.isDirty=!1}add(e){gr(this.children,e),this.isDirty=!0}remove(e){vr(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Yp),this.isDirty=!1,this.children.forEach(e)}}function Ps(t){const e=Z(t)?t.get():t;return Uf(e)?e.toValue():e}function Zp(t,e){const s=be.now(),n=({timestamp:r})=>{const o=r-s;o>=e&&(qe(n),t(o-e))};return F.read(n,!0),()=>qe(n)}function Jp(t){return t instanceof SVGElement&&t.tagName!=="svg"}function em(t,e,s){const n=Z(t)?t:ss(t);return n.start(yr("",n,e,s)),n.animation}const Ze={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},$t=typeof window<"u"&&window.MotionDebug!==void 0,sn=["","X","Y","Z"],tm={visibility:"hidden"},Yi=1e3;let sm=0;function nn(t,e,s,n){const{latestValues:r}=e;r[t]&&(s[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function Ol(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const s=fl(e);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:r,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(s,"transform",F,!(r||o))}const{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&Ol(n)}function Ll({attachResizeListener:t,defaultParent:e,measureScroll:s,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(i={},a=e==null?void 0:e()){this.id=sm++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,$t&&(Ze.totalNodes=Ze.resolvedTargetDeltas=Ze.recalculatedProjection=0),this.nodes.forEach(im),this.nodes.forEach(um),this.nodes.forEach(dm),this.nodes.forEach(om),$t&&window.MotionDebug.record(Ze)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Xp)}addEventListener(i,a){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new xr),this.eventHandlers.get(i).add(a)}notifyListeners(i,...a){const l=this.eventHandlers.get(i);l&&l.notify(...a)}hasListeners(i){return this.eventHandlers.has(i)}mount(i,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Jp(i),this.instance=i;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),t){let d;const f=()=>this.root.updateBlockedByResize=!1;t(i,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=Zp(f,250),Ts.hasAnimatedSinceResize&&(Ts.hasAnimatedSinceResize=!1,this.nodes.forEach(Zi))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:f,hasRelativeTargetChanged:m,layout:x})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||u.getDefaultTransition()||ym,{onLayoutAnimationStart:v,onLayoutAnimationComplete:b}=u.getProps(),w=!this.targetLayout||!Vl(this.targetLayout,x)||m,T=!f&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||T||f&&(w||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,T);const A={...sr(y,"layout"),onPlay:v,onComplete:b};(u.shouldReduceMotion||this.options.layoutRoot)&&(A.delay=0,A.type=!1),this.startAnimation(A)}else f||Zi(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=x})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,qe(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(hm),this.animationId++)}getTransformTemplate(){const{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Ol(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const d=this.path[u];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Xi);return}this.isUpdating||this.nodes.forEach(lm),this.isUpdating=!1,this.nodes.forEach(cm),this.nodes.forEach(nm),this.nodes.forEach(rm),this.clearAllSnapshots();const a=be.now();Q.delta=Me(0,1e3/60,a-Q.timestamp),Q.timestamp=a,Q.isProcessing=!0,Ws.update.process(Q),Ws.preRender.process(Q),Ws.render.process(Q),Q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Tr.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(am),this.sharedNodes.forEach(fm)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,F.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){F.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=H(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(a=!1),a){const l=n(this.instance);this.scroll={animationId:this.root.animationId,phase:i,isRoot:l,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!r)return;const i=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Dl(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;i&&(a||Xe(this.latestValues)||u)&&(r(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return i&&(l=this.removeTransform(l)),gm(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var i;const{visualElement:a}=this.options;if(!a)return H();const l=a.measureViewportBox();if(!(((i=this.scroll)===null||i===void 0?void 0:i.wasRoot)||this.path.some(vm))){const{scroll:u}=this.root;u&&(yt(l.x,u.offset.x),yt(l.y,u.offset.y))}return l}removeElementScroll(i){var a;const l=H();if(le(l,i),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:d,options:f}=u;u!==this.root&&d&&f.layoutScroll&&(d.wasRoot&&le(l,i),yt(l.x,d.offset.x),yt(l.y,d.offset.y))}return l}applyTransform(i,a=!1){const l=H();le(l,i);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&gt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),Xe(u.latestValues)&&gt(l,u.latestValues)}return Xe(this.latestValues)&&gt(l,this.latestValues),l}removeTransform(i){const a=H();le(a,i);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!Xe(c.latestValues))continue;Bn(c.latestValues)&&c.updateSnapshot();const u=H(),d=c.measurePageBox();le(u,d),Hi(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return Xe(this.latestValues)&&Hi(a,this.latestValues),a}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(i||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:d,layoutId:f}=this.options;if(!(!this.layout||!(d||f))){if(this.resolvedRelativeTargetAt=Q.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=H(),this.relativeTargetOrigin=H(),Xt(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),le(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=H(),this.targetWithTransforms=H()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),xp(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):le(this.target,this.layout.layoutBox),Cl(this.target,this.targetDelta)):le(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=H(),this.relativeTargetOrigin=H(),Xt(this.relativeTargetOrigin,this.target,m.target),le(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}$t&&Ze.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Bn(this.parent.latestValues)||Sl(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var i;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((i=this.parent)===null||i===void 0)&&i.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===Q.timestamp&&(c=!1),c)return;const{layout:u,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||d))return;le(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;Rp(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=H());const{target:x}=a;if(!x){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(_i(this.prevProjectionDelta.x,this.projectionDelta.x),_i(this.prevProjectionDelta.y,this.projectionDelta.y)),Yt(this.projectionDelta,this.layoutCorrected,x,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==m||!Qi(this.projectionDelta.x,this.prevProjectionDelta.x)||!Qi(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",x)),$t&&Ze.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),i){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=mt(),this.projectionDelta=mt(),this.projectionDeltaWithTransform=mt()}setAnimationOrigin(i,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},d=mt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=H(),m=l?l.source:void 0,x=this.layout?this.layout.source:void 0,y=m!==x,v=this.getStack(),b=!v||v.members.length<=1,w=!!(y&&!b&&this.options.crossfade===!0&&!this.path.some(mm));this.animationProgress=0;let T;this.mixTargetDelta=A=>{const S=A/1e3;Ji(d.x,i.x,S),Ji(d.y,i.y,S),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Xt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),pm(this.relativeTarget,this.relativeTargetOrigin,f,S),T&&$p(this.relativeTarget,T)&&(this.isProjectionDirty=!1),T||(T=H()),le(T,this.relativeTarget)),y&&(this.animationValues=u,_p(u,c,this.latestValues,S,w,b)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(qe(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=F.update(()=>{Ts.hasAnimatedSinceResize=!0,this.currentAnimation=em(0,Yi,{...i,onUpdate:a=>{this.mixTargetDelta(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Yi),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=i;if(!(!a||!l||!c)){if(this!==i&&this.layout&&c&&Il(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||H();const d=oe(this.layout.layoutBox.x);l.x.min=i.target.x.min,l.x.max=l.x.min+d;const f=oe(this.layout.layoutBox.y);l.y.min=i.target.y.min,l.y.max=l.y.min+f}le(a,l),gt(a,u),Yt(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(i,a){this.sharedNodes.has(i)||this.sharedNodes.set(i,new Wp),this.sharedNodes.get(i).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){var i;const{layoutId:a}=this.options;return a?((i=this.getStack())===null||i===void 0?void 0:i.lead)||this:this}getPrevLead(){var i;const{layoutId:a}=this.options;return a?(i=this.getStack())===null||i===void 0?void 0:i.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),i&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetSkewAndRotation(){const{visualElement:i}=this.options;if(!i)return;let a=!1;const{latestValues:l}=i;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&nn("z",i,c,this.animationValues);for(let u=0;u<sn.length;u++)nn(`rotate${sn[u]}`,i,c,this.animationValues),nn(`skew${sn[u]}`,i,c,this.animationValues);i.render();for(const u in c)i.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);i.scheduleRender()}getProjectionStyles(i){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return tm;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Ps(i==null?void 0:i.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=Ps(i==null?void 0:i.pointerEvents)||""),this.hasProjected&&!Xe(this.latestValues)&&(y.transform=u?u({},""):"none",this.hasProjected=!1),y}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),c.transform=Qp(this.projectionDeltaWithTransform,this.treeScale,f),u&&(c.transform=u(f,c.transform));const{x:m,y:x}=this.projectionDelta;c.transformOrigin=`${m.origin*100}% ${x.origin*100}% 0`,d.animationValues?c.opacity=d===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const y in ks){if(f[y]===void 0)continue;const{correct:v,applyTo:b}=ks[y],w=c.transform==="none"?f[y]:v(f[y],d);if(b){const T=b.length;for(let A=0;A<T;A++)c[b[A]]=w}else c[y]=w}return this.options.layoutId&&(c.pointerEvents=d===this?Ps(i==null?void 0:i.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>{var a;return(a=i.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Xi),this.root.sharedNodes.clear()}}}function nm(t){t.updateLayout()}function rm(t){var e;const s=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&s&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:r}=t.layout,{animationType:o}=t.options,i=s.source!==t.layout.source;o==="size"?ce(d=>{const f=i?s.measuredBox[d]:s.layoutBox[d],m=oe(f);f.min=n[d].min,f.max=f.min+m}):Il(o,s.layoutBox,n)&&ce(d=>{const f=i?s.measuredBox[d]:s.layoutBox[d],m=oe(n[d]);f.max=f.min+m,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[d].max=t.relativeTarget[d].min+m)});const a=mt();Yt(a,n,s.layoutBox);const l=mt();i?Yt(l,t.applyTransform(r,!0),s.measuredBox):Yt(l,n,s.layoutBox);const c=!Dl(a);let u=!1;if(!t.resumeFrom){const d=t.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:m}=d;if(f&&m){const x=H();Xt(x,s.layoutBox,f.layoutBox);const y=H();Xt(y,n,m.layoutBox),Vl(x,y)||(u=!0),d.options.layoutRoot&&(t.relativeTarget=y,t.relativeTargetOrigin=x,t.relativeParent=d)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:s,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:n}=t.options;n&&n()}t.options.transition=void 0}function im(t){$t&&Ze.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function om(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function am(t){t.clearSnapshot()}function Xi(t){t.clearMeasurements()}function lm(t){t.isLayoutDirty=!1}function cm(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Zi(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function um(t){t.resolveTargetDelta()}function dm(t){t.calcProjection()}function hm(t){t.resetSkewAndRotation()}function fm(t){t.removeLeadSnapshot()}function Ji(t,e,s){t.translate=_(e.translate,0,s),t.scale=_(e.scale,1,s),t.origin=e.origin,t.originPoint=e.originPoint}function eo(t,e,s,n){t.min=_(e.min,s.min,n),t.max=_(e.max,s.max,n)}function pm(t,e,s,n){eo(t.x,e.x,s.x,n),eo(t.y,e.y,s.y,n)}function mm(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const ym={duration:.45,ease:[.4,0,.1,1]},to=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),so=to("applewebkit/")&&!to("chrome/")?Math.round:J;function no(t){t.min=so(t.min),t.max=so(t.max)}function gm(t){no(t.x),no(t.y)}function Il(t,e,s){return t==="position"||t==="preserve-aspect"&&!vp(Wi(e),Wi(s),.2)}function vm(t){var e;return t!==t.root&&((e=t.scroll)===null||e===void 0?void 0:e.wasRoot)}const xm=Ll({attachResizeListener:(t,e)=>Ae(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rn={current:void 0},Fl=Ll({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rn.current){const t=new xm({});t.mount(window),t.setOptions({layoutScroll:!0}),rn.current=t}return rn.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),bm={pan:{Feature:Vp},drag:{Feature:Dp,ProjectionNode:Fl,MeasureLayout:Ml}};function ro(t,e,s){const{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover",s);const r=n[s?"onHoverStart":"onHoverEnd"];r&&F.postRender(()=>r(e,us(e)))}class wm extends We{mount(){const{current:e,props:s}=this.node;e&&(this.unmount=lp(e,n=>(ro(this.node,n,!0),r=>ro(this.node,r,!1)),{passive:!s.onHoverStart&&!s.onHoverEnd}))}unmount(){}}class Tm extends We{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=He(Ae(this.node.current,"focus",()=>this.onFocus()),Ae(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Bl=(t,e)=>e?t===e?!0:Bl(t,e.parentElement):!1;function on(t,e){if(!e)return;const s=new PointerEvent("pointer"+t);e(s,us(s))}class Pm extends We{constructor(){super(...arguments),this.removeStartListeners=J,this.removeEndListeners=J,this.removeAccessibleListeners=J,this.startPointerPress=(e,s)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),o=Ge(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:u,globalTapTarget:d}=this.node.getProps(),f=!d&&!Bl(this.node.current,a.target)?u:c;f&&F.update(()=>f(a,l))},{passive:!(n.onTap||n.onPointerUp)}),i=Ge(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=He(o,i),this.startPress(e,s)},this.startAccessiblePress=()=>{const e=o=>{if(o.key!=="Enter"||this.isPressing)return;const i=a=>{a.key!=="Enter"||!this.checkPressEnd()||on("up",(l,c)=>{const{onTap:u}=this.node.getProps();u&&F.postRender(()=>u(l,c))})};this.removeEndListeners(),this.removeEndListeners=Ae(this.node.current,"keyup",i),on("down",(a,l)=>{this.startPress(a,l)})},s=Ae(this.node.current,"keydown",e),n=()=>{this.isPressing&&on("cancel",(o,i)=>this.cancelPress(o,i))},r=Ae(this.node.current,"blur",n);this.removeAccessibleListeners=He(s,r)}}startPress(e,s){this.isPressing=!0;const{onTapStart:n,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&F.postRender(()=>n(e,s))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!gl()}cancelPress(e,s){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&F.postRender(()=>n(e,s))}mount(){const e=this.node.getProps(),s=Ge(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),n=Ae(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=He(s,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const _n=new WeakMap,an=new WeakMap,Sm=t=>{const e=_n.get(t.target);e&&e(t)},Cm=t=>{t.forEach(Sm)};function Am({root:t,...e}){const s=t||document;an.has(s)||an.set(s,{});const n=an.get(s),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(Cm,{root:t,...e})),n[r]}function jm(t,e,s){const n=Am(e);return _n.set(t,s),n.observe(t),()=>{_n.delete(t),n.unobserve(t)}}const Rm={some:0,all:1};class Em extends We{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:s,margin:n,amount:r="some",once:o}=e,i={root:s?s.current:void 0,rootMargin:n,threshold:typeof r=="number"?r:Rm[r]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:d}=this.node.getProps(),f=c?u:d;f&&f(l)};return jm(this.node.current,i,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:s}=this.node;["amount","margin","root"].some(Mm(e,s))&&this.startObserver()}unmount(){}}function Mm({viewport:t={}},{viewport:e={}}={}){return s=>t[s]!==e[s]}const km={inView:{Feature:Em},tap:{Feature:Pm},focus:{Feature:Tm},hover:{Feature:wm}},Nm={layout:{ProjectionNode:Fl,MeasureLayout:Ml}},Ul=g.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Bs=g.createContext({}),Pr=typeof window<"u",Dm=Pr?g.useLayoutEffect:g.useEffect,_l=g.createContext({strict:!1});function Vm(t,e,s,n,r){var o,i;const{visualElement:a}=g.useContext(Bs),l=g.useContext(_l),c=g.useContext(wr),u=g.useContext(Ul).reducedMotion,d=g.useRef();n=n||l.renderer,!d.current&&n&&(d.current=n(t,{visualState:e,parent:a,props:s,presenceContext:c,blockInitialAnimation:c?c.initial===!1:!1,reducedMotionConfig:u}));const f=d.current,m=g.useContext(El);f&&!f.projection&&r&&(f.type==="html"||f.type==="svg")&&Om(d.current,s,r,m);const x=g.useRef(!1);g.useInsertionEffect(()=>{f&&x.current&&f.update(s,c)});const y=s[hl],v=g.useRef(!!y&&!(!((o=window.MotionHandoffIsComplete)===null||o===void 0)&&o.call(window,y))&&((i=window.MotionHasOptimisedAnimation)===null||i===void 0?void 0:i.call(window,y)));return Dm(()=>{f&&(x.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Tr.render(f.render),v.current&&f.animationState&&f.animationState.animateChanges())}),g.useEffect(()=>{f&&(!v.current&&f.animationState&&f.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var b;(b=window.MotionHandoffMarkAsComplete)===null||b===void 0||b.call(window,y)}),v.current=!1))}),f}function Om(t,e,s,n){const{layoutId:r,layout:o,drag:i,dragConstraints:a,layoutScroll:l,layoutRoot:c}=e;t.projection=new s(t.latestValues,e["data-framer-portal-id"]?void 0:Kl(t.parent)),t.projection.setOptions({layoutId:r,layout:o,alwaysMeasureLayout:!!i||a&&pt(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:c})}function Kl(t){if(t)return t.options.allowProjection!==!1?t.projection:Kl(t.parent)}function Lm(t,e,s){return g.useCallback(n=>{n&&t.mount&&t.mount(n),e&&(n?e.mount(n):e.unmount()),s&&(typeof s=="function"?s(n):pt(s)&&(s.current=n))},[e])}function Us(t){return Ls(t.animate)||tr.some(e=>Jt(t[e]))}function zl(t){return!!(Us(t)||t.variants)}function Im(t,e){if(Us(t)){const{initial:s,animate:n}=t;return{initial:s===!1||Jt(s)?s:void 0,animate:Jt(n)?n:void 0}}return t.inherit!==!1?e:{}}function Fm(t){const{initial:e,animate:s}=Im(t,g.useContext(Bs));return g.useMemo(()=>({initial:e,animate:s}),[io(e),io(s)])}function io(t){return Array.isArray(t)?t.join(" "):t}const oo={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},It={};for(const t in oo)It[t]={isEnabled:e=>oo[t].some(s=>!!e[s])};function Bm(t){for(const e in t)It[e]={...It[e],...t[e]}}const Um=Symbol.for("motionComponentSymbol");function _m({preloadedFeatures:t,createVisualElement:e,useRender:s,useVisualState:n,Component:r}){t&&Bm(t);function o(a,l){let c;const u={...g.useContext(Ul),...a,layoutId:Km(a)},{isStatic:d}=u,f=Fm(a),m=n(a,d);if(!d&&Pr){zm();const x=Hm(u);c=x.MeasureLayout,f.visualElement=Vm(r,m,u,e,x.ProjectionNode)}return h.jsxs(Bs.Provider,{value:f,children:[c&&f.visualElement?h.jsx(c,{visualElement:f.visualElement,...u}):null,s(r,a,Lm(m,f.visualElement,l),m,d,f.visualElement)]})}const i=g.forwardRef(o);return i[Um]=r,i}function Km({layoutId:t}){const e=g.useContext(Rl).id;return e&&t!==void 0?e+"-"+t:t}function zm(t,e){g.useContext(_l).strict}function Hm(t){const{drag:e,layout:s}=It;if(!e&&!s)return{};const n={...e,...s};return{MeasureLayout:e!=null&&e.isEnabled(t)||s!=null&&s.isEnabled(t)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}const Gm=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Sr(t){return typeof t!="string"||t.includes("-")?!1:!!(Gm.indexOf(t)>-1||/[A-Z]/u.test(t))}function Hl(t,{style:e,vars:s},n,r){Object.assign(t.style,e,r&&r.getProjectionStyles(n));for(const o in s)t.style.setProperty(o,s[o])}const Gl=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ql(t,e,s,n){Hl(t,e,void 0,n);for(const r in e.attrs)t.setAttribute(Gl.has(r)?r:br(r),e.attrs[r])}function $l(t,{layout:e,layoutId:s}){return ut.has(t)||t.startsWith("origin")||(e||s!==void 0)&&(!!ks[t]||t==="opacity")}function Cr(t,e,s){var n;const{style:r}=t,o={};for(const i in r)(Z(r[i])||e.style&&Z(e.style[i])||$l(i,t)||((n=s==null?void 0:s.getValue(i))===null||n===void 0?void 0:n.liveStyle)!==void 0)&&(o[i]=r[i]);return o}function Wl(t,e,s){const n=Cr(t,e,s);for(const r in t)if(Z(t[r])||Z(e[r])){const o=as.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[o]=t[r]}return n}function qm(t){const e=g.useRef(null);return e.current===null&&(e.current=t()),e.current}function $m({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:s},n,r,o){const i={latestValues:Wm(n,r,o,t),renderState:e()};return s&&(i.mount=a=>s(n,a,i)),i}const Ql=t=>(e,s)=>{const n=g.useContext(Bs),r=g.useContext(wr),o=()=>$m(t,e,n,r);return s?o():qm(o)};function Wm(t,e,s,n){const r={},o=n(t,{});for(const f in o)r[f]=Ps(o[f]);let{initial:i,animate:a}=t;const l=Us(t),c=zl(t);e&&c&&!l&&t.inherit!==!1&&(i===void 0&&(i=e.initial),a===void 0&&(a=e.animate));let u=s?s.initial===!1:!1;u=u||i===!1;const d=u?a:i;if(d&&typeof d!="boolean"&&!Ls(d)){const f=Array.isArray(d)?d:[d];for(let m=0;m<f.length;m++){const x=Jn(t,f[m]);if(x){const{transitionEnd:y,transition:v,...b}=x;for(const w in b){let T=b[w];if(Array.isArray(T)){const A=u?T.length-1:0;T=T[A]}T!==null&&(r[w]=T)}for(const w in y)r[w]=y[w]}}}return r}const Ar=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Yl=()=>({...Ar(),attrs:{}}),Xl=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Qm={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ym=as.length;function Xm(t,e,s){let n="",r=!0;for(let o=0;o<Ym;o++){const i=as[o],a=t[i];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(i.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||s){const c=Xl(a,cr[i]);if(!l){r=!1;const u=Qm[i]||i;n+=`${u}(${c}) `}s&&(e[i]=c)}}return n=n.trim(),s?n=s(e,r?"":n):r&&(n="none"),n}function jr(t,e,s){const{style:n,vars:r,transformOrigin:o}=t;let i=!1,a=!1;for(const l in e){const c=e[l];if(ut.has(l)){i=!0;continue}else if(_a(l)){r[l]=c;continue}else{const u=Xl(c,cr[l]);l.startsWith("origin")?(a=!0,o[l]=u):n[l]=u}}if(e.transform||(i||s?n.transform=Xm(e,t.transform,s):n.transform&&(n.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;n.transformOrigin=`${l} ${c} ${u}`}}function ao(t,e,s){return typeof t=="string"?t:R.transform(e+s*t)}function Zm(t,e,s){const n=ao(e,t.x,t.width),r=ao(s,t.y,t.height);return`${n} ${r}`}const Jm={offset:"stroke-dashoffset",array:"stroke-dasharray"},ey={offset:"strokeDashoffset",array:"strokeDasharray"};function ty(t,e,s=1,n=0,r=!0){t.pathLength=1;const o=r?Jm:ey;t[o.offset]=R.transform(-n);const i=R.transform(e),a=R.transform(s);t[o.array]=`${i} ${a}`}function Rr(t,{attrX:e,attrY:s,attrScale:n,originX:r,originY:o,pathLength:i,pathSpacing:a=1,pathOffset:l=0,...c},u,d){if(jr(t,c,d),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:f,style:m,dimensions:x}=t;f.transform&&(x&&(m.transform=f.transform),delete f.transform),x&&(r!==void 0||o!==void 0||m.transform)&&(m.transformOrigin=Zm(x,r!==void 0?r:.5,o!==void 0?o:.5)),e!==void 0&&(f.x=e),s!==void 0&&(f.y=s),n!==void 0&&(f.scale=n),i!==void 0&&ty(f,i,a,l,!1)}const Er=t=>typeof t=="string"&&t.toLowerCase()==="svg",sy={useVisualState:Ql({scrapeMotionValuesFromProps:Wl,createRenderState:Yl,onMount:(t,e,{renderState:s,latestValues:n})=>{F.read(()=>{try{s.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{s.dimensions={x:0,y:0,width:0,height:0}}}),F.render(()=>{Rr(s,n,Er(e.tagName),t.transformTemplate),ql(e,s)})}})},ny={useVisualState:Ql({scrapeMotionValuesFromProps:Cr,createRenderState:Ar})};function Zl(t,e,s){for(const n in e)!Z(e[n])&&!$l(n,s)&&(t[n]=e[n])}function ry({transformTemplate:t},e){return g.useMemo(()=>{const s=Ar();return jr(s,e,t),Object.assign({},s.vars,s.style)},[e])}function iy(t,e){const s=t.style||{},n={};return Zl(n,s,t),Object.assign(n,ry(t,e)),n}function oy(t,e){const s={},n=iy(t,e);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=n,s}const ay=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ns(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ay.has(t)}let Jl=t=>!Ns(t);function ly(t){t&&(Jl=e=>e.startsWith("on")?!Ns(e):t(e))}try{ly(require("@emotion/is-prop-valid").default)}catch{}function cy(t,e,s){const n={};for(const r in t)r==="values"&&typeof t.values=="object"||(Jl(r)||s===!0&&Ns(r)||!e&&!Ns(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}function uy(t,e,s,n){const r=g.useMemo(()=>{const o=Yl();return Rr(o,e,Er(n),t.transformTemplate),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};Zl(o,t.style,t),r.style={...o,...r.style}}return r}function dy(t=!1){return(s,n,r,{latestValues:o},i)=>{const l=(Sr(s)?uy:oy)(n,o,i,s),c=cy(n,typeof s=="string",t),u=s!==g.Fragment?{...c,...l,ref:r}:{},{children:d}=n,f=g.useMemo(()=>Z(d)?d.get():d,[d]);return g.createElement(s,{...u,children:f})}}function hy(t,e){return function(n,{forwardMotionProps:r}={forwardMotionProps:!1}){const i={...Sr(n)?sy:ny,preloadedFeatures:t,useRender:dy(r),createVisualElement:e,Component:n};return _m(i)}}const Kn={current:null},ec={current:!1};function fy(){if(ec.current=!0,!!Pr)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Kn.current=t.matches;t.addListener(e),e()}else Kn.current=!1}function py(t,e,s){for(const n in e){const r=e[n],o=s[n];if(Z(r))t.addValue(n,r);else if(Z(o))t.addValue(n,ss(r,{owner:t}));else if(o!==r)if(t.hasValue(n)){const i=t.getValue(n);i.liveStyle===!0?i.jump(r):i.hasAnimated||i.set(r)}else{const i=t.getStaticValue(n);t.addValue(n,ss(i!==void 0?i:r,{owner:t}))}}for(const n in s)e[n]===void 0&&t.removeValue(n);return e}const lo=new WeakMap,my=[...Ha,X,$e],yy=t=>my.find(za(t)),co=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class gy{scrapeMotionValuesFromProps(e,s,n){return{}}constructor({parent:e,props:s,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:o,visualState:i},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=or,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=be.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,F.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=i;this.latestValues=l,this.baseTarget={...l},this.initialValues=s.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=s,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Us(s),this.isVariantNode=zl(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...d}=this.scrapeMotionValuesFromProps(s,{},this);for(const f in d){const m=d[f];l[f]!==void 0&&Z(m)&&m.set(l[f],!1)}}mount(e){this.current=e,lo.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,n)=>this.bindToMotionValue(n,s)),ec.current||fy(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Kn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){lo.delete(this.current),this.projection&&this.projection.unmount(),qe(this.notifyUpdate),qe(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const s=this.features[e];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(e,s){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const n=ut.has(e),r=s.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&F.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=s.on("renderRequest",this.scheduleRender);let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,s)),this.valueSubscriptions.set(e,()=>{r(),o(),i&&i(),s.owner&&s.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in It){const s=It[e];if(!s)continue;const{isEnabled:n,Feature:r}=s;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):H()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,s){this.latestValues[e]=s}update(e,s){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let n=0;n<co.length;n++){const r=co[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const o="on"+r,i=e[o];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=py(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(e),()=>s.variantChildren.delete(e)}addValue(e,s){const n=this.values.get(e);s!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,s),this.values.set(e,s),this.latestValues[e]=s.get())}removeValue(e){this.values.delete(e);const s=this.valueSubscriptions.get(e);s&&(s(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,s){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return n===void 0&&s!==void 0&&(n=ss(s===null?void 0:s,{owner:this}),this.addValue(e,n)),n}readValue(e,s){var n;let r=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(n=this.getBaseTargetFromProps(this.props,e))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,e,this.options);return r!=null&&(typeof r=="string"&&(Ba(r)||Fa(r))?r=parseFloat(r):!yy(r)&&$e.test(s)&&(r=Za(e,s)),this.setBaseTarget(e,Z(r)?r.get():r)),Z(r)?r.get():r}setBaseTarget(e,s){this.baseTarget[e]=s}getBaseTarget(e){var s;const{initial:n}=this.props;let r;if(typeof n=="string"||typeof n=="object"){const i=Jn(this.props,n,(s=this.presenceContext)===null||s===void 0?void 0:s.custom);i&&(r=i[e])}if(n&&r!==void 0)return r;const o=this.getBaseTargetFromProps(this.props,e);return o!==void 0&&!Z(o)?o:this.initialValues[e]!==void 0&&r===void 0?void 0:this.baseTarget[e]}on(e,s){return this.events[e]||(this.events[e]=new xr),this.events[e].add(s)}notify(e,...s){this.events[e]&&this.events[e].notify(...s)}}class tc extends gy{constructor(){super(...arguments),this.KeyframeResolver=Ja}sortInstanceNodePosition(e,s){return e.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(e,s){return e.style?e.style[s]:void 0}removeValueFromRenderState(e,{vars:s,style:n}){delete s[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;Z(e)&&(this.childSubscription=e.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function vy(t){return window.getComputedStyle(t)}class xy extends tc{constructor(){super(...arguments),this.type="html",this.renderInstance=Hl}readValueFromInstance(e,s){if(ut.has(s)){const n=ur(s);return n&&n.default||0}else{const n=vy(e),r=(_a(s)?n.getPropertyValue(s):n[s])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:s}){return Al(e,s)}build(e,s,n){jr(e,s,n.transformTemplate)}scrapeMotionValuesFromProps(e,s,n){return Cr(e,s,n)}}class by extends tc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=H}getBaseTargetFromProps(e,s){return e[s]}readValueFromInstance(e,s){if(ut.has(s)){const n=ur(s);return n&&n.default||0}return s=Gl.has(s)?s:br(s),e.getAttribute(s)}scrapeMotionValuesFromProps(e,s,n){return Wl(e,s,n)}build(e,s,n){Rr(e,s,this.isSVGTag,n.transformTemplate)}renderInstance(e,s,n,r){ql(e,s,n,r)}mount(e){this.isSVGTag=Er(e.tagName),super.mount(e)}}const wy=(t,e)=>Sr(t)?new by(e):new xy(e,{allowProjection:t!==g.Fragment}),Ty=hy({...ap,...km,...bm,...Nm},wy),E=rh(Ty),Py={some:0,all:1};function Sy(t,e,{root:s,margin:n,amount:r="some"}={}){const o=yl(t),i=new WeakMap,a=c=>{c.forEach(u=>{const d=i.get(u.target);if(u.isIntersecting!==!!d)if(u.isIntersecting){const f=e(u);typeof f=="function"?i.set(u.target,f):l.unobserve(u.target)}else d&&(d(u),i.delete(u.target))})},l=new IntersectionObserver(a,{root:s,rootMargin:n,threshold:typeof r=="number"?r:Py[r]});return o.forEach(c=>l.observe(c)),()=>l.disconnect()}function ds(t,{root:e,margin:s,amount:n,once:r=!1}={}){const[o,i]=g.useState(!1);return g.useEffect(()=>{if(!t.current||r&&o)return;const a=()=>(i(!0),r?void 0:()=>i(!1)),l={root:e&&e.current||void 0,margin:s,amount:n};return Sy(t.current,a,l)},[e,t,s,r,n]),o}const Cy="/assets/BullBuster_1752063257019-D-NwXa2E.png";function Ay(){const[t,e]=g.useState(!1),[s,n]=g.useState(!1);g.useEffect(()=>{const o=()=>{e(window.scrollY>100)};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)},[]);const r=o=>{const i=document.getElementById(o);i&&(i.scrollIntoView({behavior:"smooth",block:"start"}),n(!1))};return h.jsx(E.nav,{initial:{y:-100},animate:{y:0},className:"fixed top-0 w-full z-50 transition-all duration-300 glass-navbar",children:h.jsxs("div",{className:"max-w-7xl mx-auto px-6 py-4",children:[h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsxs(E.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-3",children:[h.jsx("img",{src:Cy,alt:"BullBuster Logo",className:"w-12 h-12 object-contain"}),h.jsx("span",{className:"text-2xl font-black text-white",children:"BullBuster"})]}),h.jsx("div",{className:"hidden md:flex items-center space-x-8",children:["home","about","menu","gallery","track","contact"].map(o=>h.jsx("button",{onClick:()=>r(o),className:"text-white hover:text-brand-yellow transition-colors duration-300 font-medium capitalize",children:o==="track"?"Track Order":o},o))}),h.jsxs("div",{className:"flex items-center space-x-4",children:[h.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>r("menu"),className:"bg-brand-yellow text-brand-black px-6 py-2 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 shadow-lg",children:"Order Now"}),h.jsx("button",{className:"md:hidden text-white text-xl",onClick:()=>n(!s),children:s?h.jsx(aa,{}):h.jsx(Gu,{})})]})]}),s&&h.jsx(E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden mt-4 pb-4 border-t border-brand-yellow/30",children:h.jsx("div",{className:"flex flex-col space-y-3 pt-4",children:["home","about","menu","gallery","track","contact"].map(o=>h.jsx("button",{onClick:()=>r(o),className:"text-white hover:text-brand-yellow transition-colors duration-300 font-medium capitalize text-left",children:o==="track"?"Track Order":o},o))})})]})})}function jy(){const t=e=>{const s=document.getElementById(e);s&&s.scrollIntoView({behavior:"smooth",block:"start"})};return h.jsxs("section",{id:"home",className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[h.jsx("div",{className:"absolute inset-0 parallax-bg",style:{backgroundImage:"url('https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080')"}}),h.jsx("div",{className:"absolute inset-0 hero-overlay"}),h.jsx(E.div,{animate:{y:[0,-20,0]},transition:{duration:6,repeat:1/0,ease:"easeInOut"},className:"absolute top-20 left-10 text-brand-yellow text-4xl opacity-20",children:"🍕"}),h.jsx(E.div,{animate:{y:[0,-20,0]},transition:{duration:6,repeat:1/0,ease:"easeInOut",delay:-2},className:"absolute top-40 right-20 text-brand-yellow text-3xl opacity-30",children:"🍗"}),h.jsx(E.div,{animate:{y:[0,-20,0]},transition:{duration:6,repeat:1/0,ease:"easeInOut",delay:-4},className:"absolute bottom-32 left-20 text-brand-yellow text-5xl opacity-25",children:"🍔"}),h.jsxs("div",{className:"relative z-10 text-center text-white px-6 max-w-6xl mx-auto",children:[h.jsxs(E.h1,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{duration:.8,ease:"easeOut"},className:"text-6xl md:text-8xl font-black mb-6",children:[h.jsx("span",{className:"text-brand-yellow",children:"BULL"}),"BUSTER"]}),h.jsx(E.p,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{duration:1,ease:"easeOut",delay:.3},className:"text-xl md:text-2xl mb-8 font-light max-w-3xl mx-auto",children:"Experience the finest fast food in Lahore. Premium burgers, legendary taste, unforgettable moments."}),h.jsxs(E.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5,ease:"easeOut",delay:.6},className:"flex flex-col sm:flex-row gap-6 justify-center items-center",children:[h.jsxs(E.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:()=>t("menu"),className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full text-lg font-bold hover:bg-brand-yellow-light transition-all duration-300 shadow-2xl flex items-center gap-2",children:[h.jsx(oa,{className:"w-5 h-5"}),"Explore Menu"]}),h.jsxs(E.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:()=>t("about"),className:"border-2 border-brand-yellow text-brand-yellow px-8 py-4 rounded-full text-lg font-bold hover:bg-brand-yellow hover:text-brand-black transition-all duration-300 flex items-center gap-2",children:[h.jsx($u,{className:"w-5 h-5"}),"Watch Story"]})]})]}),h.jsx(E.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer",onClick:()=>t("about"),children:h.jsx(Iu,{className:"text-brand-yellow text-2xl"})})]})}function Ry(){const t=g.useRef(null),e=ds(t,{once:!0,amount:.3});return h.jsx("section",{id:"about",ref:t,className:"py-20 bg-brand-black relative overflow-hidden",children:h.jsx("div",{className:"max-w-7xl mx-auto px-6",children:h.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[h.jsxs(E.div,{initial:{x:-50,opacity:0},animate:e?{x:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut"},children:[h.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Our Story"}),h.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Crafting Perfection Since Day One"}),h.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-8",children:"Born in the heart of Lahore, BullBuster represents a revolution in fast food excellence. We combine traditional Pakistani hospitality with international culinary standards to create an unforgettable dining experience."}),h.jsxs("div",{className:"grid grid-cols-2 gap-8 mb-8",children:[h.jsxs(E.div,{initial:{scale:.8,opacity:0},animate:e?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.2},className:"text-center",children:[h.jsx("div",{className:"text-3xl font-black text-brand-yellow mb-2",children:"50K+"}),h.jsx("div",{className:"text-gray-300 font-medium",children:"Happy Customers"})]}),h.jsxs(E.div,{initial:{scale:.8,opacity:0},animate:e?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.4},className:"text-center",children:[h.jsx("div",{className:"text-3xl font-black text-brand-yellow mb-2",children:"24/7"}),h.jsx("div",{className:"text-gray-300 font-medium",children:"Service Available"})]})]}),h.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300",children:"Learn More About Us"})]}),h.jsxs(E.div,{initial:{x:50,opacity:0},animate:e?{x:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut",delay:.3},className:"relative",children:[h.jsx("img",{src:"https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",alt:"Luxury restaurant interior with warm ambiance",className:"rounded-2xl shadow-2xl w-full h-96 object-cover"}),h.jsx(E.div,{initial:{scale:0,rotate:-180},animate:e?{scale:1,rotate:0}:{},transition:{duration:.6,delay:.6},className:"absolute -top-8 -left-8 w-24 h-24 bg-brand-yellow rounded-full flex items-center justify-center shadow-lg",children:h.jsx(Vu,{className:"text-brand-black text-2xl"})})]})]})})})}function Ey(){const t=g.useRef(null),e=ds(t,{once:!0,amount:.2}),[s,n]=g.useState("All"),{data:r=[],isLoading:o}=ea({queryKey:["/api/menu"]}),i=["All",...Array.from(new Set(r.map(c=>c.category)))],a=s==="All"?r:r.filter(c=>c.category===s),l=c=>`Rs. ${(c/100).toFixed(0)}`;return o?h.jsx("section",{className:"py-20 bg-brand-dark-gray",children:h.jsx("div",{className:"max-w-7xl mx-auto px-6",children:h.jsxs("div",{className:"text-center",children:[h.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-brand-yellow mx-auto"}),h.jsx("p",{className:"mt-4 text-gray-300",children:"Loading delicious menu..."})]})})}):h.jsx("section",{id:"menu",ref:t,className:"py-20 bg-brand-dark-gray",children:h.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[h.jsxs(E.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[h.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Our Menu"}),h.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Delicious Selections"}),h.jsx("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"From signature burgers to crispy delights, every bite tells a story of quality and passion."})]}),h.jsx(E.div,{initial:{y:30,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.2},className:"flex justify-center mb-12",children:h.jsx("div",{className:"flex flex-wrap gap-4 bg-black/50 backdrop-blur-sm rounded-full p-2 shadow-lg border border-brand-yellow/20",children:i.map(c=>h.jsx("button",{onClick:()=>n(c),className:`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${s===c?"bg-brand-yellow text-brand-black":"hover:bg-white/10 text-white"}`,children:c},c))})}),h.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:a.map((c,u)=>h.jsxs(E.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.1*u},whileHover:{y:-8},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300",children:[h.jsxs("div",{className:"relative overflow-hidden",children:[h.jsx("img",{src:c.image,alt:c.name,className:"w-full h-64 object-cover transition-transform duration-300 hover:scale-110"}),!c.available&&h.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:h.jsx("span",{className:"text-white font-bold text-lg",children:"Currently Unavailable"})})]}),h.jsxs("div",{className:"p-6",children:[h.jsx("h3",{className:"text-xl font-black text-white mb-2",children:c.name}),h.jsx("p",{className:"text-gray-300 mb-4",children:c.description}),h.jsxs("div",{className:"flex justify-between items-center",children:[h.jsx("span",{className:"text-2xl font-black text-brand-yellow",children:l(c.price)}),h.jsx(E.button,{whileHover:{scale:1.1},whileTap:{scale:.9},disabled:!c.available,className:"bg-brand-yellow text-brand-black px-4 py-2 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1",children:h.jsx(Wu,{className:"w-4 h-4"})})]})]})]},c.id))}),h.jsx(E.div,{initial:{y:30,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.5},className:"text-center mt-12",children:h.jsx(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300",children:"View Full Menu"})})]})})}const My=[{url:"https://images.unsplash.com/photo-1512152272829-e3139592d56f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Artistic burger presentation with professional plating"},{url:"https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Modern restaurant interior with sleek design"},{url:"https://images.unsplash.com/photo-1551782450-17144efb9c50?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Gourmet burger with dramatic lighting and composition"},{url:"https://images.unsplash.com/photo-1565299507177-b0ac66763828?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Artistic food plating with professional garnish"},{url:"https://images.unsplash.com/photo-1544148103-0773bf10d330?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Luxury restaurant ambiance with elegant lighting"},{url:"https://images.unsplash.com/photo-1586190848861-99aa4a171e90?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Premium burger presentation with professional photography"}];function ky(){const t=g.useRef(null),e=ds(t,{once:!0,amount:.2});return h.jsx("section",{id:"gallery",ref:t,className:"py-20 bg-brand-black",children:h.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[h.jsxs(E.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[h.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Gallery"}),h.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Visual Feast"}),h.jsx("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"Every dish is a masterpiece, every moment is memorable."})]}),h.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:My.map((s,n)=>h.jsxs(E.div,{initial:{y:50,opacity:0,scale:.9},animate:e?{y:0,opacity:1,scale:1}:{},transition:{duration:.6,delay:.1*n},whileHover:{scale:1.05},className:"relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer group",children:[h.jsx("img",{src:s.url,alt:s.alt,className:"w-full h-80 object-cover transition-transform duration-300 group-hover:scale-110"}),h.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center",children:h.jsx(Pn,{className:"text-white text-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"})})]},n))})]})})}const ln=[{id:"confirmed",label:"Order Confirmed",description:"Your order has been received and confirmed",icon:Lu},{id:"preparing",label:"Preparing",description:"Our chefs are preparing your delicious meal",icon:oa},{id:"out_for_delivery",label:"Out for Delivery",description:"Your order is on its way to you",icon:Ou},{id:"delivered",label:"Delivered",description:"Order delivered successfully",icon:_u}];function Ny(){const t=g.useRef(null),e=ds(t,{once:!0,amount:.2}),[s,n]=g.useState(""),[r,o]=g.useState(""),{data:i,isLoading:a,error:l}=ea({queryKey:["/api/orders",r],enabled:!!r}),c=()=>{s.trim()&&o(s.trim())},u=f=>{if(!i)return"pending";const m=ln.findIndex(y=>y.id===i.status),x=ln.findIndex(y=>y.id===f);return x<m?"completed":x===m?"active":"pending"},d=f=>new Date(f).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0});return h.jsx("section",{id:"track",ref:t,className:"py-20 bg-brand-dark-gray",children:h.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[h.jsxs(E.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[h.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Track Your Order"}),h.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Real-Time Updates"}),h.jsx("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"Stay informed with live tracking and precise delivery estimates."})]}),h.jsxs("div",{className:"max-w-4xl mx-auto",children:[h.jsx(E.div,{initial:{y:30,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.2},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl p-8 shadow-lg mb-12",children:h.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[h.jsx("input",{type:"text",placeholder:"Enter your order ID (e.g., BB123456789)",value:s,onChange:f=>n(f.target.value),className:"flex-1 px-6 py-4 border border-brand-yellow/30 bg-black/20 text-white placeholder-gray-400 rounded-full focus:border-brand-yellow focus:outline-none transition-colors duration-300",onKeyPress:f=>f.key==="Enter"&&c()}),h.jsxs(E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:c,disabled:a,className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 flex items-center gap-2 disabled:opacity-50",children:[h.jsx(Pn,{className:"w-5 h-5"}),a?"Searching...":"Track Order"]})]})}),l&&h.jsx(E.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl p-8 shadow-lg text-center",children:h.jsxs("div",{className:"text-red-500 mb-4",children:[h.jsx(Pn,{className:"w-12 h-12 mx-auto mb-2"}),h.jsx("h3",{className:"text-xl font-bold text-white",children:"Order Not Found"}),h.jsx("p",{className:"text-gray-300 mt-2",children:"Please check your order number and try again."})]})}),i&&h.jsxs(E.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},transition:{duration:.6},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl p-8 shadow-lg",children:[h.jsxs("div",{className:"mb-8",children:[h.jsxs("h3",{className:"text-2xl font-black text-white mb-2",children:["Order #",i.orderNumber]}),h.jsxs("p",{className:"text-gray-300",children:["Customer: ",i.customerName]}),h.jsxs("p",{className:"text-gray-300",children:["Total: Rs. ",(i.total/100).toFixed(0)]}),h.jsx("p",{className:"text-gray-300",children:"Estimated delivery: 25-30 minutes"})]}),h.jsx("div",{className:"space-y-6",children:ln.map((f,m)=>{const x=u(f.id),y=f.icon;return h.jsxs(E.div,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{duration:.4,delay:.1*m},className:"flex items-center space-x-4",children:[h.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center font-bold transition-all duration-300 ${x==="completed"?"bg-brand-yellow text-brand-black":x==="active"?"bg-brand-yellow text-brand-black animate-pulse":"bg-gray-200 text-gray-400"}`,children:h.jsx(y,{className:"w-6 h-6"})}),h.jsxs("div",{className:"flex-1",children:[h.jsx("h4",{className:`font-semibold ${x==="pending"?"text-gray-500":"text-white"}`,children:f.label}),h.jsx("p",{className:`text-sm ${x==="pending"?"text-gray-500":"text-gray-300"}`,children:f.description}),x!=="pending"&&h.jsx("span",{className:"text-xs text-gray-400",children:x==="active"?"In Progress":d(i.updatedAt)})]})]},f.id)})})]})]})]})})}function Dy(){const t=g.useRef(null),e=ds(t,{once:!0,amount:.2}),{toast:s}=sa();Yn();const[n,r]=g.useState({name:"",email:"",subject:"",message:""}),o=wu({mutationFn:async l=>(await Tu("POST","/api/contact",l)).json(),onSuccess:()=>{s({title:"Message Sent!",description:"Thank you for contacting us. We'll get back to you soon."}),r({name:"",email:"",subject:"",message:""})},onError:()=>{s({title:"Error",description:"Failed to send message. Please try again.",variant:"destructive"})}}),i=l=>{l.preventDefault(),o.mutate(n)},a=l=>{r(c=>({...c,[l.target.name]:l.target.value}))};return h.jsxs("section",{id:"contact",ref:t,className:"py-20 bg-brand-black text-white relative overflow-hidden",children:[h.jsxs("div",{className:"absolute inset-0 opacity-10",children:[h.jsx("div",{className:"text-9xl absolute top-20 left-20",children:"🍔"}),h.jsx("div",{className:"text-7xl absolute bottom-32 right-32",children:"🍕"})]}),h.jsxs("div",{className:"max-w-7xl mx-auto px-6 relative z-10",children:[h.jsxs(E.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[h.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Contact Us"}),h.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Get In Touch"}),h.jsx("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"Visit our locations or reach out to us for any inquiries."})]}),h.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16",children:[h.jsxs(E.div,{initial:{x:-50,opacity:0},animate:e?{x:0,opacity:1}:{},transition:{duration:.6,delay:.2},className:"space-y-8",children:[h.jsxs("div",{className:"flex items-start space-x-4",children:[h.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:h.jsx(Hu,{className:"text-brand-black"})}),h.jsxs("div",{children:[h.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Main Location"}),h.jsx("p",{className:"text-gray-300",children:"MM Alam Road, Gulberg III, Lahore, Pakistan"})]})]}),h.jsxs("div",{className:"flex items-start space-x-4",children:[h.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:h.jsx(qu,{className:"text-brand-black"})}),h.jsxs("div",{children:[h.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Phone"}),h.jsx("p",{className:"text-gray-300",children:"+92 42 1234 5678"})]})]}),h.jsxs("div",{className:"flex items-start space-x-4",children:[h.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:h.jsx(Bu,{className:"text-brand-black"})}),h.jsxs("div",{children:[h.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Hours"}),h.jsx("p",{className:"text-gray-300",children:"Open 24/7 for your convenience"})]})]}),h.jsxs("div",{className:"flex items-start space-x-4",children:[h.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:h.jsx(zu,{className:"text-brand-black"})}),h.jsxs("div",{children:[h.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Email"}),h.jsx("p",{className:"text-gray-300",children:"<EMAIL>"})]})]})]}),h.jsxs(E.div,{initial:{x:50,opacity:0},animate:e?{x:0,opacity:1}:{},transition:{duration:.6,delay:.4},className:"glass-effect rounded-2xl p-8",children:[h.jsx("h3",{className:"text-2xl font-bold text-white mb-6",children:"Send us a Message"}),h.jsxs("form",{onSubmit:i,className:"space-y-6",children:[h.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[h.jsx("input",{type:"text",name:"name",placeholder:"Your Name",value:n.name,onChange:a,required:!0,className:"bg-white/20 border border-white/30 rounded-full px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),h.jsx("input",{type:"email",name:"email",placeholder:"Your Email",value:n.email,onChange:a,required:!0,className:"bg-white/20 border border-white/30 rounded-full px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"})]}),h.jsx("input",{type:"text",name:"subject",placeholder:"Subject",value:n.subject,onChange:a,required:!0,className:"w-full bg-white/20 border border-white/30 rounded-full px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),h.jsx("textarea",{name:"message",placeholder:"Your Message",rows:5,value:n.message,onChange:a,required:!0,className:"w-full bg-white/20 border border-white/30 rounded-2xl px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300 resize-none"}),h.jsx(E.button,{type:"submit",disabled:o.isPending,whileHover:{scale:1.05},whileTap:{scale:.95},className:"w-full bg-brand-yellow text-brand-black py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 disabled:opacity-50",children:o.isPending?"Sending...":"Send Message"})]})]})]})]})]})}function Vy(){const[t,e]=g.useState(""),s=r=>{r.preventDefault(),console.log("Newsletter subscription:",t),e("")},n=r=>{const o=document.getElementById(r);o&&o.scrollIntoView({behavior:"smooth",block:"start"})};return h.jsx("footer",{className:"bg-brand-black-soft text-white py-16",children:h.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[h.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[h.jsxs(E.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6},viewport:{once:!0},children:[h.jsxs("div",{className:"flex items-center space-x-2 mb-6",children:[h.jsx(Qu,{className:"text-3xl text-brand-yellow"}),h.jsx("span",{className:"text-2xl font-black text-white",children:"BullBuster"})]}),h.jsx("p",{className:"text-gray-400 mb-6",children:"Premium fast food experience in the heart of Lahore, serving quality meals with passion and excellence."}),h.jsxs("div",{className:"flex space-x-4",children:[h.jsx(E.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:h.jsx(Uu,{className:"w-5 h-5"})}),h.jsx(E.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:h.jsx(Ku,{className:"w-5 h-5"})}),h.jsx(E.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:h.jsx(Xu,{className:"w-5 h-5"})})]})]}),h.jsxs(E.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.1},viewport:{once:!0},children:[h.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Quick Links"}),h.jsx("ul",{className:"space-y-3",children:["home","about","menu","gallery","contact"].map(r=>h.jsx("li",{children:h.jsx("button",{onClick:()=>n(r),className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300 capitalize",children:r==="home"?"Home":r==="about"?"About Us":r.charAt(0).toUpperCase()+r.slice(1)})},r))})]}),h.jsxs(E.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[h.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Services"}),h.jsxs("ul",{className:"space-y-3",children:[h.jsx("li",{children:h.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Dine In"})}),h.jsx("li",{children:h.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Takeaway"})}),h.jsx("li",{children:h.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Home Delivery"})}),h.jsx("li",{children:h.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Catering"})}),h.jsx("li",{children:h.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Party Orders"})})]})]}),h.jsxs(E.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.3},viewport:{once:!0},children:[h.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Newsletter"}),h.jsx("p",{className:"text-gray-400 mb-4",children:"Subscribe to get special offers and updates."}),h.jsxs("form",{onSubmit:s,className:"flex",children:[h.jsx("input",{type:"email",placeholder:"Your email",value:t,onChange:r=>e(r.target.value),required:!0,className:"flex-1 bg-white/20 border border-white/30 rounded-l-full px-4 py-3 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),h.jsx(E.button,{type:"submit",whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-6 py-3 rounded-r-full hover:bg-brand-yellow-light transition-colors duration-300",children:h.jsx(Yu,{className:"w-5 h-5"})})]})]})]}),h.jsx(E.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"border-t border-gray-700 pt-8 text-center",children:h.jsx("p",{className:"text-gray-400",children:"© 2024 BullBsuter. All rights reserved. | Privacy Policy | Terms of Service"})})]})})}function Oy(){return h.jsxs("div",{className:"min-h-screen bg-brand-black",children:[h.jsx(Ay,{}),h.jsx(jy,{}),h.jsx(Ry,{}),h.jsx(Ey,{}),h.jsx(ky,{}),h.jsx(Ny,{}),h.jsx(Dy,{}),h.jsx(Vy,{})]})}function Ly(){return h.jsxs(zc,{children:[h.jsx(Ir,{path:"/",component:Oy}),h.jsx(Ir,{component:nh})]})}function Iy(){return h.jsx(cu,{client:Su,children:h.jsxs(Xd,{children:[h.jsx(Dd,{}),h.jsx(Ly,{})]})})}Eo(document.getElementById("root")).render(h.jsx(Iy,{}));
