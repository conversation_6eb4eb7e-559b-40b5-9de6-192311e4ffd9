import{r as p,a as ne}from"./vendor-DbAb9B2p.js";var oe={exports:{}},wt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ke=p,Be=Symbol.for("react.element"),Ie=Symbol.for("react.fragment"),je=Object.prototype.hasOwnProperty,He=ke.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ze={key:!0,ref:!0,__self:!0,__source:!0};function re(t,e,o){var n,r={},i=null,s=null;o!==void 0&&(i=""+o),e.key!==void 0&&(i=""+e.key),e.ref!==void 0&&(s=e.ref);for(n in e)je.call(e,n)&&!ze.hasOwnProperty(n)&&(r[n]=e[n]);if(t&&t.defaultProps)for(n in e=t.defaultProps,e)r[n]===void 0&&(r[n]=e[n]);return{$$typeof:Be,type:t,key:i,ref:s,props:r,_owner:He.current}}wt.Fragment=Ie;wt.jsx=re;wt.jsxs=re;oe.exports=wt;var _=oe.exports;function Et(t,e,{checkForDefaultPrevented:o=!0}={}){return function(r){if(t==null||t(r),o===!1||!r.defaultPrevented)return e==null?void 0:e(r)}}function Ut(t,e){if(typeof t=="function")return t(e);t!=null&&(t.current=e)}function ie(...t){return e=>{let o=!1;const n=t.map(r=>{const i=Ut(r,e);return!o&&typeof i=="function"&&(o=!0),i});if(o)return()=>{for(let r=0;r<n.length;r++){const i=n[r];typeof i=="function"?i():Ut(t[r],null)}}}}function st(...t){return p.useCallback(ie(...t),t)}function Ve(t,e=[]){let o=[];function n(i,s){const c=p.createContext(s),l=o.length;o=[...o,s];const a=u=>{var y;const{scope:m,children:d,...h}=u,g=((y=m==null?void 0:m[t])==null?void 0:y[l])||c,w=p.useMemo(()=>h,Object.values(h));return _.jsx(g.Provider,{value:w,children:d})};a.displayName=i+"Provider";function f(u,m){var g;const d=((g=m==null?void 0:m[t])==null?void 0:g[l])||c,h=p.useContext(d);if(h)return h;if(s!==void 0)return s;throw new Error(`\`${u}\` must be used within \`${i}\``)}return[a,f]}const r=()=>{const i=o.map(s=>p.createContext(s));return function(c){const l=(c==null?void 0:c[t])||i;return p.useMemo(()=>({[`__scope${t}`]:{...c,[t]:l}}),[c,l])}};return r.scopeName=t,[n,Ue(r,...e)]}function Ue(...t){const e=t[0];if(t.length===1)return e;const o=()=>{const n=t.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(i){const s=n.reduce((c,{useScope:l,scopeName:a})=>{const u=l(i)[`__scope${a}`];return{...c,...u}},{});return p.useMemo(()=>({[`__scope${e.scopeName}`]:s}),[s])}};return o.scopeName=e.scopeName,o}function Ye(t){const e=Xe(t),o=p.forwardRef((n,r)=>{const{children:i,...s}=n,c=p.Children.toArray(i),l=c.find(qe);if(l){const a=l.props.children,f=c.map(u=>u===l?p.Children.count(a)>1?p.Children.only(null):p.isValidElement(a)?a.props.children:null:u);return _.jsx(e,{...s,ref:r,children:p.isValidElement(a)?p.cloneElement(a,void 0,f):null})}return _.jsx(e,{...s,ref:r,children:i})});return o.displayName=`${t}.Slot`,o}function Xe(t){const e=p.forwardRef((o,n)=>{const{children:r,...i}=o;if(p.isValidElement(r)){const s=Ze(r),c=Ke(i,r.props);return r.type!==p.Fragment&&(c.ref=n?ie(n,s):s),p.cloneElement(r,c)}return p.Children.count(r)>1?p.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}var se=Symbol("radix.slottable");function vo(t){const e=({children:o})=>_.jsx(_.Fragment,{children:o});return e.displayName=`${t}.Slottable`,e.__radixId=se,e}function qe(t){return p.isValidElement(t)&&typeof t.type=="function"&&"__radixId"in t.type&&t.type.__radixId===se}function Ke(t,e){const o={...e};for(const n in e){const r=t[n],i=e[n];/^on[A-Z]/.test(n)?r&&i?o[n]=(...c)=>{i(...c),r(...c)}:r&&(o[n]=r):n==="style"?o[n]={...r,...i}:n==="className"&&(o[n]=[r,i].filter(Boolean).join(" "))}return{...t,...o}}function Ze(t){var n,r;let e=(n=Object.getOwnPropertyDescriptor(t.props,"ref"))==null?void 0:n.get,o=e&&"isReactWarning"in e&&e.isReactWarning;return o?t.ref:(e=(r=Object.getOwnPropertyDescriptor(t,"ref"))==null?void 0:r.get,o=e&&"isReactWarning"in e&&e.isReactWarning,o?t.props.ref:t.props.ref||t.ref)}var Je=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ct=Je.reduce((t,e)=>{const o=Ye(`Primitive.${e}`),n=p.forwardRef((r,i)=>{const{asChild:s,...c}=r,l=s?o:e;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),_.jsx(l,{...c,ref:i})});return n.displayName=`Primitive.${e}`,{...t,[e]:n}},{});function Ge(t,e){t&&ne.flushSync(()=>t.dispatchEvent(e))}function xt(t){const e=p.useRef(t);return p.useEffect(()=>{e.current=t}),p.useMemo(()=>(...o)=>{var n;return(n=e.current)==null?void 0:n.call(e,...o)},[])}function Qe(t,e=globalThis==null?void 0:globalThis.document){const o=xt(t);p.useEffect(()=>{const n=r=>{r.key==="Escape"&&o(r)};return e.addEventListener("keydown",n,{capture:!0}),()=>e.removeEventListener("keydown",n,{capture:!0})},[o,e])}var tn="DismissableLayer",Rt="dismissableLayer.update",en="dismissableLayer.pointerDownOutside",nn="dismissableLayer.focusOutside",Yt,ce=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),on=p.forwardRef((t,e)=>{const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:n,onPointerDownOutside:r,onFocusOutside:i,onInteractOutside:s,onDismiss:c,...l}=t,a=p.useContext(ce),[f,u]=p.useState(null),m=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,d]=p.useState({}),h=st(e,x=>u(x)),g=Array.from(a.layers),[w]=[...a.layersWithOutsidePointerEventsDisabled].slice(-1),y=g.indexOf(w),v=f?g.indexOf(f):-1,E=a.layersWithOutsidePointerEventsDisabled.size>0,b=v>=y,A=cn(x=>{const R=x.target,T=[...a.branches].some(C=>C.contains(R));!b||T||(r==null||r(x),s==null||s(x),x.defaultPrevented||c==null||c())},m),P=an(x=>{const R=x.target;[...a.branches].some(C=>C.contains(R))||(i==null||i(x),s==null||s(x),x.defaultPrevented||c==null||c())},m);return Qe(x=>{v===a.layers.size-1&&(n==null||n(x),!x.defaultPrevented&&c&&(x.preventDefault(),c()))},m),p.useEffect(()=>{if(f)return o&&(a.layersWithOutsidePointerEventsDisabled.size===0&&(Yt=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),a.layersWithOutsidePointerEventsDisabled.add(f)),a.layers.add(f),Xt(),()=>{o&&a.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=Yt)}},[f,m,o,a]),p.useEffect(()=>()=>{f&&(a.layers.delete(f),a.layersWithOutsidePointerEventsDisabled.delete(f),Xt())},[f,a]),p.useEffect(()=>{const x=()=>d({});return document.addEventListener(Rt,x),()=>document.removeEventListener(Rt,x)},[]),_.jsx(ct.div,{...l,ref:h,style:{pointerEvents:E?b?"auto":"none":void 0,...t.style},onFocusCapture:Et(t.onFocusCapture,P.onFocusCapture),onBlurCapture:Et(t.onBlurCapture,P.onBlurCapture),onPointerDownCapture:Et(t.onPointerDownCapture,A.onPointerDownCapture)})});on.displayName=tn;var rn="DismissableLayerBranch",sn=p.forwardRef((t,e)=>{const o=p.useContext(ce),n=p.useRef(null),r=st(e,n);return p.useEffect(()=>{const i=n.current;if(i)return o.branches.add(i),()=>{o.branches.delete(i)}},[o.branches]),_.jsx(ct.div,{...t,ref:r})});sn.displayName=rn;function cn(t,e=globalThis==null?void 0:globalThis.document){const o=xt(t),n=p.useRef(!1),r=p.useRef(()=>{});return p.useEffect(()=>{const i=c=>{if(c.target&&!n.current){let l=function(){ae(en,o,a,{discrete:!0})};const a={originalEvent:c};c.pointerType==="touch"?(e.removeEventListener("click",r.current),r.current=l,e.addEventListener("click",r.current,{once:!0})):l()}else e.removeEventListener("click",r.current);n.current=!1},s=window.setTimeout(()=>{e.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),e.removeEventListener("pointerdown",i),e.removeEventListener("click",r.current)}},[e,o]),{onPointerDownCapture:()=>n.current=!0}}function an(t,e=globalThis==null?void 0:globalThis.document){const o=xt(t),n=p.useRef(!1);return p.useEffect(()=>{const r=i=>{i.target&&!n.current&&ae(nn,o,{originalEvent:i},{discrete:!1})};return e.addEventListener("focusin",r),()=>e.removeEventListener("focusin",r)},[e,o]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}function Xt(){const t=new CustomEvent(Rt);document.dispatchEvent(t)}function ae(t,e,o,{discrete:n}){const r=o.originalEvent.target,i=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:o});e&&r.addEventListener(t,e,{once:!0}),n?Ge(r,i):r.dispatchEvent(i)}var ot=globalThis!=null&&globalThis.document?p.useLayoutEffect:()=>{};const ln=["top","right","bottom","left"],q=Math.min,$=Math.max,mt=Math.round,ut=Math.floor,z=t=>({x:t,y:t}),fn={left:"right",right:"left",bottom:"top",top:"bottom"},un={start:"end",end:"start"};function Ct(t,e,o){return $(t,q(e,o))}function Y(t,e){return typeof t=="function"?t(e):t}function X(t){return t.split("-")[0]}function tt(t){return t.split("-")[1]}function Nt(t){return t==="x"?"y":"x"}function Lt(t){return t==="y"?"height":"width"}function K(t){return["top","bottom"].includes(X(t))?"y":"x"}function Tt(t){return Nt(K(t))}function dn(t,e,o){o===void 0&&(o=!1);const n=tt(t),r=Tt(t),i=Lt(r);let s=r==="x"?n===(o?"end":"start")?"right":"left":n==="start"?"bottom":"top";return e.reference[i]>e.floating[i]&&(s=ht(s)),[s,ht(s)]}function pn(t){const e=ht(t);return[St(t),e,St(e)]}function St(t){return t.replace(/start|end/g,e=>un[e])}function mn(t,e,o){const n=["left","right"],r=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(t){case"top":case"bottom":return o?e?r:n:e?n:r;case"left":case"right":return e?i:s;default:return[]}}function hn(t,e,o,n){const r=tt(t);let i=mn(X(t),o==="start",n);return r&&(i=i.map(s=>s+"-"+r),e&&(i=i.concat(i.map(St)))),i}function ht(t){return t.replace(/left|right|bottom|top/g,e=>fn[e])}function gn(t){return{top:0,right:0,bottom:0,left:0,...t}}function le(t){return typeof t!="number"?gn(t):{top:t,right:t,bottom:t,left:t}}function gt(t){const{x:e,y:o,width:n,height:r}=t;return{width:n,height:r,top:o,left:e,right:e+n,bottom:o+r,x:e,y:o}}function qt(t,e,o){let{reference:n,floating:r}=t;const i=K(e),s=Tt(e),c=Lt(s),l=X(e),a=i==="y",f=n.x+n.width/2-r.width/2,u=n.y+n.height/2-r.height/2,m=n[c]/2-r[c]/2;let d;switch(l){case"top":d={x:f,y:n.y-r.height};break;case"bottom":d={x:f,y:n.y+n.height};break;case"right":d={x:n.x+n.width,y:u};break;case"left":d={x:n.x-r.width,y:u};break;default:d={x:n.x,y:n.y}}switch(tt(e)){case"start":d[s]-=m*(o&&a?-1:1);break;case"end":d[s]+=m*(o&&a?-1:1);break}return d}const yn=async(t,e,o)=>{const{placement:n="bottom",strategy:r="absolute",middleware:i=[],platform:s}=o,c=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(e));let a=await s.getElementRects({reference:t,floating:e,strategy:r}),{x:f,y:u}=qt(a,n,l),m=n,d={},h=0;for(let g=0;g<c.length;g++){const{name:w,fn:y}=c[g],{x:v,y:E,data:b,reset:A}=await y({x:f,y:u,initialPlacement:n,placement:m,strategy:r,middlewareData:d,rects:a,platform:s,elements:{reference:t,floating:e}});f=v??f,u=E??u,d={...d,[w]:{...d[w],...b}},A&&h<=50&&(h++,typeof A=="object"&&(A.placement&&(m=A.placement),A.rects&&(a=A.rects===!0?await s.getElementRects({reference:t,floating:e,strategy:r}):A.rects),{x:f,y:u}=qt(a,m,l)),g=-1)}return{x:f,y:u,placement:m,strategy:r,middlewareData:d}};async function rt(t,e){var o;e===void 0&&(e={});const{x:n,y:r,platform:i,rects:s,elements:c,strategy:l}=t,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:u="floating",altBoundary:m=!1,padding:d=0}=Y(e,t),h=le(d),w=c[m?u==="floating"?"reference":"floating":u],y=gt(await i.getClippingRect({element:(o=await(i.isElement==null?void 0:i.isElement(w)))==null||o?w:w.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(c.floating)),boundary:a,rootBoundary:f,strategy:l})),v=u==="floating"?{x:n,y:r,width:s.floating.width,height:s.floating.height}:s.reference,E=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c.floating)),b=await(i.isElement==null?void 0:i.isElement(E))?await(i.getScale==null?void 0:i.getScale(E))||{x:1,y:1}:{x:1,y:1},A=gt(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:v,offsetParent:E,strategy:l}):v);return{top:(y.top-A.top+h.top)/b.y,bottom:(A.bottom-y.bottom+h.bottom)/b.y,left:(y.left-A.left+h.left)/b.x,right:(A.right-y.right+h.right)/b.x}}const wn=t=>({name:"arrow",options:t,async fn(e){const{x:o,y:n,placement:r,rects:i,platform:s,elements:c,middlewareData:l}=e,{element:a,padding:f=0}=Y(t,e)||{};if(a==null)return{};const u=le(f),m={x:o,y:n},d=Tt(r),h=Lt(d),g=await s.getDimensions(a),w=d==="y",y=w?"top":"left",v=w?"bottom":"right",E=w?"clientHeight":"clientWidth",b=i.reference[h]+i.reference[d]-m[d]-i.floating[h],A=m[d]-i.reference[d],P=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a));let x=P?P[E]:0;(!x||!await(s.isElement==null?void 0:s.isElement(P)))&&(x=c.floating[E]||i.floating[h]);const R=b/2-A/2,T=x/2-g[h]/2-1,C=q(u[y],T),F=q(u[v],T),W=C,D=x-g[h]-F,S=x/2-g[h]/2+R,B=Ct(W,S,D),N=!l.arrow&&tt(r)!=null&&S!==B&&i.reference[h]/2-(S<W?C:F)-g[h]/2<0,L=N?S<W?S-W:S-D:0;return{[d]:m[d]+L,data:{[d]:B,centerOffset:S-B-L,...N&&{alignmentOffset:L}},reset:N}}}),xn=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var o,n;const{placement:r,middlewareData:i,rects:s,initialPlacement:c,platform:l,elements:a}=e,{mainAxis:f=!0,crossAxis:u=!0,fallbackPlacements:m,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:g=!0,...w}=Y(t,e);if((o=i.arrow)!=null&&o.alignmentOffset)return{};const y=X(r),v=K(c),E=X(c)===c,b=await(l.isRTL==null?void 0:l.isRTL(a.floating)),A=m||(E||!g?[ht(c)]:pn(c)),P=h!=="none";!m&&P&&A.push(...hn(c,g,h,b));const x=[c,...A],R=await rt(e,w),T=[];let C=((n=i.flip)==null?void 0:n.overflows)||[];if(f&&T.push(R[y]),u){const S=dn(r,s,b);T.push(R[S[0]],R[S[1]])}if(C=[...C,{placement:r,overflows:T}],!T.every(S=>S<=0)){var F,W;const S=(((F=i.flip)==null?void 0:F.index)||0)+1,B=x[S];if(B)return{data:{index:S,overflows:C},reset:{placement:B}};let N=(W=C.filter(L=>L.overflows[0]<=0).sort((L,O)=>L.overflows[1]-O.overflows[1])[0])==null?void 0:W.placement;if(!N)switch(d){case"bestFit":{var D;const L=(D=C.filter(O=>{if(P){const M=K(O.placement);return M===v||M==="y"}return!0}).map(O=>[O.placement,O.overflows.filter(M=>M>0).reduce((M,H)=>M+H,0)]).sort((O,M)=>O[1]-M[1])[0])==null?void 0:D[0];L&&(N=L);break}case"initialPlacement":N=c;break}if(r!==N)return{reset:{placement:N}}}return{}}}};function Kt(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function Zt(t){return ln.some(e=>t[e]>=0)}const vn=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){const{rects:o}=e,{strategy:n="referenceHidden",...r}=Y(t,e);switch(n){case"referenceHidden":{const i=await rt(e,{...r,elementContext:"reference"}),s=Kt(i,o.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Zt(s)}}}case"escaped":{const i=await rt(e,{...r,altBoundary:!0}),s=Kt(i,o.floating);return{data:{escapedOffsets:s,escaped:Zt(s)}}}default:return{}}}}};async function bn(t,e){const{placement:o,platform:n,elements:r}=t,i=await(n.isRTL==null?void 0:n.isRTL(r.floating)),s=X(o),c=tt(o),l=K(o)==="y",a=["left","top"].includes(s)?-1:1,f=i&&l?-1:1,u=Y(e,t);let{mainAxis:m,crossAxis:d,alignmentAxis:h}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return c&&typeof h=="number"&&(d=c==="end"?h*-1:h),l?{x:d*f,y:m*a}:{x:m*a,y:d*f}}const An=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var o,n;const{x:r,y:i,placement:s,middlewareData:c}=e,l=await bn(e,t);return s===((o=c.offset)==null?void 0:o.placement)&&(n=c.arrow)!=null&&n.alignmentOffset?{}:{x:r+l.x,y:i+l.y,data:{...l,placement:s}}}}},En=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:o,y:n,placement:r}=e,{mainAxis:i=!0,crossAxis:s=!1,limiter:c={fn:w=>{let{x:y,y:v}=w;return{x:y,y:v}}},...l}=Y(t,e),a={x:o,y:n},f=await rt(e,l),u=K(X(r)),m=Nt(u);let d=a[m],h=a[u];if(i){const w=m==="y"?"top":"left",y=m==="y"?"bottom":"right",v=d+f[w],E=d-f[y];d=Ct(v,d,E)}if(s){const w=u==="y"?"top":"left",y=u==="y"?"bottom":"right",v=h+f[w],E=h-f[y];h=Ct(v,h,E)}const g=c.fn({...e,[m]:d,[u]:h});return{...g,data:{x:g.x-o,y:g.y-n,enabled:{[m]:i,[u]:s}}}}}},Pn=function(t){return t===void 0&&(t={}),{options:t,fn(e){const{x:o,y:n,placement:r,rects:i,middlewareData:s}=e,{offset:c=0,mainAxis:l=!0,crossAxis:a=!0}=Y(t,e),f={x:o,y:n},u=K(r),m=Nt(u);let d=f[m],h=f[u];const g=Y(c,e),w=typeof g=="number"?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(l){const E=m==="y"?"height":"width",b=i.reference[m]-i.floating[E]+w.mainAxis,A=i.reference[m]+i.reference[E]-w.mainAxis;d<b?d=b:d>A&&(d=A)}if(a){var y,v;const E=m==="y"?"width":"height",b=["top","left"].includes(X(r)),A=i.reference[u]-i.floating[E]+(b&&((y=s.offset)==null?void 0:y[u])||0)+(b?0:w.crossAxis),P=i.reference[u]+i.reference[E]+(b?0:((v=s.offset)==null?void 0:v[u])||0)-(b?w.crossAxis:0);h<A?h=A:h>P&&(h=P)}return{[m]:d,[u]:h}}}},On=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){var o,n;const{placement:r,rects:i,platform:s,elements:c}=e,{apply:l=()=>{},...a}=Y(t,e),f=await rt(e,a),u=X(r),m=tt(r),d=K(r)==="y",{width:h,height:g}=i.floating;let w,y;u==="top"||u==="bottom"?(w=u,y=m===(await(s.isRTL==null?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(y=u,w=m==="end"?"top":"bottom");const v=g-f.top-f.bottom,E=h-f.left-f.right,b=q(g-f[w],v),A=q(h-f[y],E),P=!e.middlewareData.shift;let x=b,R=A;if((o=e.middlewareData.shift)!=null&&o.enabled.x&&(R=E),(n=e.middlewareData.shift)!=null&&n.enabled.y&&(x=v),P&&!m){const C=$(f.left,0),F=$(f.right,0),W=$(f.top,0),D=$(f.bottom,0);d?R=h-2*(C!==0||F!==0?C+F:$(f.left,f.right)):x=g-2*(W!==0||D!==0?W+D:$(f.top,f.bottom))}await l({...e,availableWidth:R,availableHeight:x});const T=await s.getDimensions(c.floating);return h!==T.width||g!==T.height?{reset:{rects:!0}}:{}}}};function vt(){return typeof window<"u"}function et(t){return fe(t)?(t.nodeName||"").toLowerCase():"#document"}function k(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function U(t){var e;return(e=(fe(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function fe(t){return vt()?t instanceof Node||t instanceof k(t).Node:!1}function I(t){return vt()?t instanceof Element||t instanceof k(t).Element:!1}function V(t){return vt()?t instanceof HTMLElement||t instanceof k(t).HTMLElement:!1}function Jt(t){return!vt()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof k(t).ShadowRoot}function at(t){const{overflow:e,overflowX:o,overflowY:n,display:r}=j(t);return/auto|scroll|overlay|hidden|clip/.test(e+n+o)&&!["inline","contents"].includes(r)}function Rn(t){return["table","td","th"].includes(et(t))}function bt(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function Mt(t){const e=_t(),o=I(t)?j(t):t;return["transform","translate","scale","rotate","perspective"].some(n=>o[n]?o[n]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!e&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!e&&(o.filter?o.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(n=>(o.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(o.contain||"").includes(n))}function Cn(t){let e=Z(t);for(;V(e)&&!Q(e);){if(Mt(e))return e;if(bt(e))return null;e=Z(e)}return null}function _t(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Q(t){return["html","body","#document"].includes(et(t))}function j(t){return k(t).getComputedStyle(t)}function At(t){return I(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function Z(t){if(et(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Jt(t)&&t.host||U(t);return Jt(e)?e.host:e}function ue(t){const e=Z(t);return Q(e)?t.ownerDocument?t.ownerDocument.body:t.body:V(e)&&at(e)?e:ue(e)}function it(t,e,o){var n;e===void 0&&(e=[]),o===void 0&&(o=!0);const r=ue(t),i=r===((n=t.ownerDocument)==null?void 0:n.body),s=k(r);if(i){const c=Dt(s);return e.concat(s,s.visualViewport||[],at(r)?r:[],c&&o?it(c):[])}return e.concat(r,it(r,[],o))}function Dt(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function de(t){const e=j(t);let o=parseFloat(e.width)||0,n=parseFloat(e.height)||0;const r=V(t),i=r?t.offsetWidth:o,s=r?t.offsetHeight:n,c=mt(o)!==i||mt(n)!==s;return c&&(o=i,n=s),{width:o,height:n,$:c}}function Ft(t){return I(t)?t:t.contextElement}function G(t){const e=Ft(t);if(!V(e))return z(1);const o=e.getBoundingClientRect(),{width:n,height:r,$:i}=de(e);let s=(i?mt(o.width):o.width)/n,c=(i?mt(o.height):o.height)/r;return(!s||!Number.isFinite(s))&&(s=1),(!c||!Number.isFinite(c))&&(c=1),{x:s,y:c}}const Sn=z(0);function pe(t){const e=k(t);return!_t()||!e.visualViewport?Sn:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function Dn(t,e,o){return e===void 0&&(e=!1),!o||e&&o!==k(t)?!1:e}function J(t,e,o,n){e===void 0&&(e=!1),o===void 0&&(o=!1);const r=t.getBoundingClientRect(),i=Ft(t);let s=z(1);e&&(n?I(n)&&(s=G(n)):s=G(t));const c=Dn(i,o,n)?pe(i):z(0);let l=(r.left+c.x)/s.x,a=(r.top+c.y)/s.y,f=r.width/s.x,u=r.height/s.y;if(i){const m=k(i),d=n&&I(n)?k(n):n;let h=m,g=Dt(h);for(;g&&n&&d!==h;){const w=G(g),y=g.getBoundingClientRect(),v=j(g),E=y.left+(g.clientLeft+parseFloat(v.paddingLeft))*w.x,b=y.top+(g.clientTop+parseFloat(v.paddingTop))*w.y;l*=w.x,a*=w.y,f*=w.x,u*=w.y,l+=E,a+=b,h=k(g),g=Dt(h)}}return gt({width:f,height:u,x:l,y:a})}function Wt(t,e){const o=At(t).scrollLeft;return e?e.left+o:J(U(t)).left+o}function me(t,e,o){o===void 0&&(o=!1);const n=t.getBoundingClientRect(),r=n.left+e.scrollLeft-(o?0:Wt(t,n)),i=n.top+e.scrollTop;return{x:r,y:i}}function Nn(t){let{elements:e,rect:o,offsetParent:n,strategy:r}=t;const i=r==="fixed",s=U(n),c=e?bt(e.floating):!1;if(n===s||c&&i)return o;let l={scrollLeft:0,scrollTop:0},a=z(1);const f=z(0),u=V(n);if((u||!u&&!i)&&((et(n)!=="body"||at(s))&&(l=At(n)),V(n))){const d=J(n);a=G(n),f.x=d.x+n.clientLeft,f.y=d.y+n.clientTop}const m=s&&!u&&!i?me(s,l,!0):z(0);return{width:o.width*a.x,height:o.height*a.y,x:o.x*a.x-l.scrollLeft*a.x+f.x+m.x,y:o.y*a.y-l.scrollTop*a.y+f.y+m.y}}function Ln(t){return Array.from(t.getClientRects())}function Tn(t){const e=U(t),o=At(t),n=t.ownerDocument.body,r=$(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),i=$(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight);let s=-o.scrollLeft+Wt(t);const c=-o.scrollTop;return j(n).direction==="rtl"&&(s+=$(e.clientWidth,n.clientWidth)-r),{width:r,height:i,x:s,y:c}}function Mn(t,e){const o=k(t),n=U(t),r=o.visualViewport;let i=n.clientWidth,s=n.clientHeight,c=0,l=0;if(r){i=r.width,s=r.height;const a=_t();(!a||a&&e==="fixed")&&(c=r.offsetLeft,l=r.offsetTop)}return{width:i,height:s,x:c,y:l}}function _n(t,e){const o=J(t,!0,e==="fixed"),n=o.top+t.clientTop,r=o.left+t.clientLeft,i=V(t)?G(t):z(1),s=t.clientWidth*i.x,c=t.clientHeight*i.y,l=r*i.x,a=n*i.y;return{width:s,height:c,x:l,y:a}}function Gt(t,e,o){let n;if(e==="viewport")n=Mn(t,o);else if(e==="document")n=Tn(U(t));else if(I(e))n=_n(e,o);else{const r=pe(t);n={x:e.x-r.x,y:e.y-r.y,width:e.width,height:e.height}}return gt(n)}function he(t,e){const o=Z(t);return o===e||!I(o)||Q(o)?!1:j(o).position==="fixed"||he(o,e)}function Fn(t,e){const o=e.get(t);if(o)return o;let n=it(t,[],!1).filter(c=>I(c)&&et(c)!=="body"),r=null;const i=j(t).position==="fixed";let s=i?Z(t):t;for(;I(s)&&!Q(s);){const c=j(s),l=Mt(s);!l&&c.position==="fixed"&&(r=null),(i?!l&&!r:!l&&c.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||at(s)&&!l&&he(t,s))?n=n.filter(f=>f!==s):r=c,s=Z(s)}return e.set(t,n),n}function Wn(t){let{element:e,boundary:o,rootBoundary:n,strategy:r}=t;const s=[...o==="clippingAncestors"?bt(e)?[]:Fn(e,this._c):[].concat(o),n],c=s[0],l=s.reduce((a,f)=>{const u=Gt(e,f,r);return a.top=$(u.top,a.top),a.right=q(u.right,a.right),a.bottom=q(u.bottom,a.bottom),a.left=$(u.left,a.left),a},Gt(e,c,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function $n(t){const{width:e,height:o}=de(t);return{width:e,height:o}}function kn(t,e,o){const n=V(e),r=U(e),i=o==="fixed",s=J(t,!0,i,e);let c={scrollLeft:0,scrollTop:0};const l=z(0);if(n||!n&&!i)if((et(e)!=="body"||at(r))&&(c=At(e)),n){const m=J(e,!0,i,e);l.x=m.x+e.clientLeft,l.y=m.y+e.clientTop}else r&&(l.x=Wt(r));const a=r&&!n&&!i?me(r,c):z(0),f=s.left+c.scrollLeft-l.x-a.x,u=s.top+c.scrollTop-l.y-a.y;return{x:f,y:u,width:s.width,height:s.height}}function Pt(t){return j(t).position==="static"}function Qt(t,e){if(!V(t)||j(t).position==="fixed")return null;if(e)return e(t);let o=t.offsetParent;return U(t)===o&&(o=o.ownerDocument.body),o}function ge(t,e){const o=k(t);if(bt(t))return o;if(!V(t)){let r=Z(t);for(;r&&!Q(r);){if(I(r)&&!Pt(r))return r;r=Z(r)}return o}let n=Qt(t,e);for(;n&&Rn(n)&&Pt(n);)n=Qt(n,e);return n&&Q(n)&&Pt(n)&&!Mt(n)?o:n||Cn(t)||o}const Bn=async function(t){const e=this.getOffsetParent||ge,o=this.getDimensions,n=await o(t.floating);return{reference:kn(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function In(t){return j(t).direction==="rtl"}const jn={convertOffsetParentRelativeRectToViewportRelativeRect:Nn,getDocumentElement:U,getClippingRect:Wn,getOffsetParent:ge,getElementRects:Bn,getClientRects:Ln,getDimensions:$n,getScale:G,isElement:I,isRTL:In};function ye(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function Hn(t,e){let o=null,n;const r=U(t);function i(){var c;clearTimeout(n),(c=o)==null||c.disconnect(),o=null}function s(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),i();const a=t.getBoundingClientRect(),{left:f,top:u,width:m,height:d}=a;if(c||e(),!m||!d)return;const h=ut(u),g=ut(r.clientWidth-(f+m)),w=ut(r.clientHeight-(u+d)),y=ut(f),E={rootMargin:-h+"px "+-g+"px "+-w+"px "+-y+"px",threshold:$(0,q(1,l))||1};let b=!0;function A(P){const x=P[0].intersectionRatio;if(x!==l){if(!b)return s();x?s(!1,x):n=setTimeout(()=>{s(!1,1e-7)},1e3)}x===1&&!ye(a,t.getBoundingClientRect())&&s(),b=!1}try{o=new IntersectionObserver(A,{...E,root:r.ownerDocument})}catch{o=new IntersectionObserver(A,E)}o.observe(t)}return s(!0),i}function zn(t,e,o,n){n===void 0&&(n={});const{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=n,a=Ft(t),f=r||i?[...a?it(a):[],...it(e)]:[];f.forEach(y=>{r&&y.addEventListener("scroll",o,{passive:!0}),i&&y.addEventListener("resize",o)});const u=a&&c?Hn(a,o):null;let m=-1,d=null;s&&(d=new ResizeObserver(y=>{let[v]=y;v&&v.target===a&&d&&(d.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var E;(E=d)==null||E.observe(e)})),o()}),a&&!l&&d.observe(a),d.observe(e));let h,g=l?J(t):null;l&&w();function w(){const y=J(t);g&&!ye(g,y)&&o(),g=y,h=requestAnimationFrame(w)}return o(),()=>{var y;f.forEach(v=>{r&&v.removeEventListener("scroll",o),i&&v.removeEventListener("resize",o)}),u==null||u(),(y=d)==null||y.disconnect(),d=null,l&&cancelAnimationFrame(h)}}const Vn=An,Un=En,Yn=xn,Xn=On,qn=vn,te=wn,Kn=Pn,Zn=(t,e,o)=>{const n=new Map,r={platform:jn,...o},i={...r.platform,_c:n};return yn(t,e,{...r,platform:i})};var pt=typeof document<"u"?p.useLayoutEffect:p.useEffect;function yt(t,e){if(t===e)return!0;if(typeof t!=typeof e)return!1;if(typeof t=="function"&&t.toString()===e.toString())return!0;let o,n,r;if(t&&e&&typeof t=="object"){if(Array.isArray(t)){if(o=t.length,o!==e.length)return!1;for(n=o;n--!==0;)if(!yt(t[n],e[n]))return!1;return!0}if(r=Object.keys(t),o=r.length,o!==Object.keys(e).length)return!1;for(n=o;n--!==0;)if(!{}.hasOwnProperty.call(e,r[n]))return!1;for(n=o;n--!==0;){const i=r[n];if(!(i==="_owner"&&t.$$typeof)&&!yt(t[i],e[i]))return!1}return!0}return t!==t&&e!==e}function we(t){return typeof window>"u"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function ee(t,e){const o=we(t);return Math.round(e*o)/o}function Ot(t){const e=p.useRef(t);return pt(()=>{e.current=t}),e}function Jn(t){t===void 0&&(t={});const{placement:e="bottom",strategy:o="absolute",middleware:n=[],platform:r,elements:{reference:i,floating:s}={},transform:c=!0,whileElementsMounted:l,open:a}=t,[f,u]=p.useState({x:0,y:0,strategy:o,placement:e,middlewareData:{},isPositioned:!1}),[m,d]=p.useState(n);yt(m,n)||d(n);const[h,g]=p.useState(null),[w,y]=p.useState(null),v=p.useCallback(O=>{O!==P.current&&(P.current=O,g(O))},[]),E=p.useCallback(O=>{O!==x.current&&(x.current=O,y(O))},[]),b=i||h,A=s||w,P=p.useRef(null),x=p.useRef(null),R=p.useRef(f),T=l!=null,C=Ot(l),F=Ot(r),W=Ot(a),D=p.useCallback(()=>{if(!P.current||!x.current)return;const O={placement:e,strategy:o,middleware:m};F.current&&(O.platform=F.current),Zn(P.current,x.current,O).then(M=>{const H={...M,isPositioned:W.current!==!1};S.current&&!yt(R.current,H)&&(R.current=H,ne.flushSync(()=>{u(H)}))})},[m,e,o,F,W]);pt(()=>{a===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,u(O=>({...O,isPositioned:!1})))},[a]);const S=p.useRef(!1);pt(()=>(S.current=!0,()=>{S.current=!1}),[]),pt(()=>{if(b&&(P.current=b),A&&(x.current=A),b&&A){if(C.current)return C.current(b,A,D);D()}},[b,A,D,C,T]);const B=p.useMemo(()=>({reference:P,floating:x,setReference:v,setFloating:E}),[v,E]),N=p.useMemo(()=>({reference:b,floating:A}),[b,A]),L=p.useMemo(()=>{const O={position:o,left:0,top:0};if(!N.floating)return O;const M=ee(N.floating,f.x),H=ee(N.floating,f.y);return c?{...O,transform:"translate("+M+"px, "+H+"px)",...we(N.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:M,top:H}},[o,c,N.floating,f.x,f.y]);return p.useMemo(()=>({...f,update:D,refs:B,elements:N,floatingStyles:L}),[f,D,B,N,L])}const Gn=t=>{function e(o){return{}.hasOwnProperty.call(o,"current")}return{name:"arrow",options:t,fn(o){const{element:n,padding:r}=typeof t=="function"?t(o):t;return n&&e(n)?n.current!=null?te({element:n.current,padding:r}).fn(o):{}:n?te({element:n,padding:r}).fn(o):{}}}},Qn=(t,e)=>({...Vn(t),options:[t,e]}),to=(t,e)=>({...Un(t),options:[t,e]}),eo=(t,e)=>({...Kn(t),options:[t,e]}),no=(t,e)=>({...Yn(t),options:[t,e]}),oo=(t,e)=>({...Xn(t),options:[t,e]}),ro=(t,e)=>({...qn(t),options:[t,e]}),io=(t,e)=>({...Gn(t),options:[t,e]});var so="Arrow",xe=p.forwardRef((t,e)=>{const{children:o,width:n=10,height:r=5,...i}=t;return _.jsx(ct.svg,{...i,ref:e,width:n,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?o:_.jsx("polygon",{points:"0,0 30,0 15,10"})})});xe.displayName=so;var co=xe;function ao(t){const[e,o]=p.useState(void 0);return ot(()=>{if(t){o({width:t.offsetWidth,height:t.offsetHeight});const n=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const i=r[0];let s,c;if("borderBoxSize"in i){const l=i.borderBoxSize,a=Array.isArray(l)?l[0]:l;s=a.inlineSize,c=a.blockSize}else s=t.offsetWidth,c=t.offsetHeight;o({width:s,height:c})});return n.observe(t,{box:"border-box"}),()=>n.unobserve(t)}else o(void 0)},[t]),e}var ve="Popper",[be,bo]=Ve(ve),[Ao,Ae]=be(ve),Ee="PopperAnchor",Pe=p.forwardRef((t,e)=>{const{__scopePopper:o,virtualRef:n,...r}=t,i=Ae(Ee,o),s=p.useRef(null),c=st(e,s);return p.useEffect(()=>{i.onAnchorChange((n==null?void 0:n.current)||s.current)}),n?null:_.jsx(ct.div,{...r,ref:c})});Pe.displayName=Ee;var $t="PopperContent",[lo,fo]=be($t),Oe=p.forwardRef((t,e)=>{var kt,Bt,It,jt,Ht,zt;const{__scopePopper:o,side:n="bottom",sideOffset:r=0,align:i="center",alignOffset:s=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:a=[],collisionPadding:f=0,sticky:u="partial",hideWhenDetached:m=!1,updatePositionStrategy:d="optimized",onPlaced:h,...g}=t,w=Ae($t,o),[y,v]=p.useState(null),E=st(e,nt=>v(nt)),[b,A]=p.useState(null),P=ao(b),x=(P==null?void 0:P.width)??0,R=(P==null?void 0:P.height)??0,T=n+(i!=="center"?"-"+i:""),C=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},F=Array.isArray(a)?a:[a],W=F.length>0,D={padding:C,boundary:F.filter(po),altBoundary:W},{refs:S,floatingStyles:B,placement:N,isPositioned:L,middlewareData:O}=Jn({strategy:"fixed",placement:T,whileElementsMounted:(...nt)=>zn(...nt,{animationFrame:d==="always"}),elements:{reference:w.anchor},middleware:[Qn({mainAxis:r+R,alignmentAxis:s}),l&&to({mainAxis:!0,crossAxis:!1,limiter:u==="partial"?eo():void 0,...D}),l&&no({...D}),oo({...D,apply:({elements:nt,rects:Vt,availableWidth:_e,availableHeight:Fe})=>{const{width:We,height:$e}=Vt.reference,ft=nt.floating.style;ft.setProperty("--radix-popper-available-width",`${_e}px`),ft.setProperty("--radix-popper-available-height",`${Fe}px`),ft.setProperty("--radix-popper-anchor-width",`${We}px`),ft.setProperty("--radix-popper-anchor-height",`${$e}px`)}}),b&&io({element:b,padding:c}),mo({arrowWidth:x,arrowHeight:R}),m&&ro({strategy:"referenceHidden",...D})]}),[M,H]=Se(N),lt=xt(h);ot(()=>{L&&(lt==null||lt())},[L,lt]);const De=(kt=O.arrow)==null?void 0:kt.x,Ne=(Bt=O.arrow)==null?void 0:Bt.y,Le=((It=O.arrow)==null?void 0:It.centerOffset)!==0,[Te,Me]=p.useState();return ot(()=>{y&&Me(window.getComputedStyle(y).zIndex)},[y]),_.jsx("div",{ref:S.setFloating,"data-radix-popper-content-wrapper":"",style:{...B,transform:L?B.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Te,"--radix-popper-transform-origin":[(jt=O.transformOrigin)==null?void 0:jt.x,(Ht=O.transformOrigin)==null?void 0:Ht.y].join(" "),...((zt=O.hide)==null?void 0:zt.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:_.jsx(lo,{scope:o,placedSide:M,onArrowChange:A,arrowX:De,arrowY:Ne,shouldHideArrow:Le,children:_.jsx(ct.div,{"data-side":M,"data-align":H,...g,ref:E,style:{...g.style,animation:L?void 0:"none"}})})})});Oe.displayName=$t;var Re="PopperArrow",uo={top:"bottom",right:"left",bottom:"top",left:"right"},Ce=p.forwardRef(function(e,o){const{__scopePopper:n,...r}=e,i=fo(Re,n),s=uo[i.placedSide];return _.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:_.jsx(co,{...r,ref:o,style:{...r.style,display:"block"}})})});Ce.displayName=Re;function po(t){return t!==null}var mo=t=>({name:"transformOrigin",options:t,fn(e){var w,y,v;const{placement:o,rects:n,middlewareData:r}=e,s=((w=r.arrow)==null?void 0:w.centerOffset)!==0,c=s?0:t.arrowWidth,l=s?0:t.arrowHeight,[a,f]=Se(o),u={start:"0%",center:"50%",end:"100%"}[f],m=(((y=r.arrow)==null?void 0:y.x)??0)+c/2,d=(((v=r.arrow)==null?void 0:v.y)??0)+l/2;let h="",g="";return a==="bottom"?(h=s?u:`${m}px`,g=`${-l}px`):a==="top"?(h=s?u:`${m}px`,g=`${n.floating.height+l}px`):a==="right"?(h=`${-l}px`,g=s?u:`${d}px`):a==="left"&&(h=`${n.floating.width+l}px`,g=s?u:`${d}px`),{data:{x:h,y:g}}}});function Se(t){const[e,o="center"]=t.split("-");return[e,o]}var Eo=Pe,Po=Oe,Oo=Ce;function ho(t,e){return p.useReducer((o,n)=>e[o][n]??o,t)}var go=t=>{const{present:e,children:o}=t,n=yo(e),r=typeof o=="function"?o({present:n.isPresent}):p.Children.only(o),i=st(n.ref,wo(r));return typeof o=="function"||n.isPresent?p.cloneElement(r,{ref:i}):null};go.displayName="Presence";function yo(t){const[e,o]=p.useState(),n=p.useRef({}),r=p.useRef(t),i=p.useRef("none"),s=t?"mounted":"unmounted",[c,l]=ho(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return p.useEffect(()=>{const a=dt(n.current);i.current=c==="mounted"?a:"none"},[c]),ot(()=>{const a=n.current,f=r.current;if(f!==t){const m=i.current,d=dt(a);t?l("MOUNT"):d==="none"||(a==null?void 0:a.display)==="none"?l("UNMOUNT"):l(f&&m!==d?"ANIMATION_OUT":"UNMOUNT"),r.current=t}},[t,l]),ot(()=>{if(e){let a;const f=e.ownerDocument.defaultView??window,u=d=>{const g=dt(n.current).includes(d.animationName);if(d.target===e&&g&&(l("ANIMATION_END"),!r.current)){const w=e.style.animationFillMode;e.style.animationFillMode="forwards",a=f.setTimeout(()=>{e.style.animationFillMode==="forwards"&&(e.style.animationFillMode=w)})}},m=d=>{d.target===e&&(i.current=dt(n.current))};return e.addEventListener("animationstart",m),e.addEventListener("animationcancel",u),e.addEventListener("animationend",u),()=>{f.clearTimeout(a),e.removeEventListener("animationstart",m),e.removeEventListener("animationcancel",u),e.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[e,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:p.useCallback(a=>{a&&(n.current=getComputedStyle(a)),o(a)},[])}}function dt(t){return(t==null?void 0:t.animationName)||"none"}function wo(t){var n,r;let e=(n=Object.getOwnPropertyDescriptor(t.props,"ref"))==null?void 0:n.get,o=e&&"isReactWarning"in e&&e.isReactWarning;return o?t.ref:(e=(r=Object.getOwnPropertyDescriptor(t,"ref"))==null?void 0:r.get,o=e&&"isReactWarning"in e&&e.isReactWarning,o?t.props.ref:t.props.ref||t.ref)}export{Eo as A,Po as C,on as D,ct as P,bo as a,Et as b,Ve as c,go as d,Oo as e,vo as f,_ as j,st as u};
