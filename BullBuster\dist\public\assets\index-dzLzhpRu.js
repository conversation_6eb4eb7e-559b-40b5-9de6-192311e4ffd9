var $r=e=>{throw TypeError(e)};var Js=(e,t,s)=>t.has(e)||$r("Cannot "+s);var p=(e,t,s)=>(Js(e,t,"read from private field"),s?s.call(e):t.get(e)),E=(e,t,s)=>t.has(e)?$r("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),C=(e,t,s,n)=>(Js(e,t,"write to private field"),n?n.call(e,s):t.set(e,s),s),D=(e,t,s)=>(Js(e,t,"access private method"),s);var xs=(e,t,s,n)=>({set _(r){C(e,t,r,s)},get _(){return p(e,t,n)}});import{j as d,V as $o,R as Qo,A as Yo,C as Xo,T as Zo,D as Jo,P as Oc,c as Lc,a as ea,u as ta,b as Ic,d as Fc,e as mt,f as Bc,g as Uc,h as _c,i as zc,k as Hc,l as Kc}from"./ui-7t35Orsm.js";import{a as Wc,r as m,c as Gc}from"./vendor-CX2mysxk.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))n(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function s(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(r){if(r.ep)return;r.ep=!0;const o=s(r);fetch(r.href,o)}})();var sa,Qr=Wc;sa=Qr.createRoot,Qr.hydrateRoot;function qc(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var s,n,r,o,i=[],a="",l=e.split("/");for(l[0]||l.shift();r=l.shift();)s=r[0],s==="*"?(i.push(s),a+=r[1]==="?"?"(?:/(.*))?":"/(.*)"):s===":"?(n=r.indexOf("?",1),o=r.indexOf(".",1),i.push(r.substring(1,~n?n:~o?o:r.length)),a+=~n&&!~o?"(?:/([^/]+?))?":"/([^/]+?)",~o&&(a+=(~n?"?":"")+"\\"+r.substring(o))):a+="/"+r;return{keys:i,pattern:new RegExp("^"+a+(t?"(?=$|/)":"/?$"),"i")}}var na={exports:{}},ra={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lt=m;function $c(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qc=typeof Object.is=="function"?Object.is:$c,Yc=Lt.useState,Xc=Lt.useEffect,Zc=Lt.useLayoutEffect,Jc=Lt.useDebugValue;function eu(e,t){var s=t(),n=Yc({inst:{value:s,getSnapshot:t}}),r=n[0].inst,o=n[1];return Zc(function(){r.value=s,r.getSnapshot=t,en(r)&&o({inst:r})},[e,s,t]),Xc(function(){return en(r)&&o({inst:r}),e(function(){en(r)&&o({inst:r})})},[e]),Jc(s),s}function en(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!Qc(e,s)}catch{return!0}}function tu(e,t){return t()}var su=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?tu:eu;ra.useSyncExternalStore=Lt.useSyncExternalStore!==void 0?Lt.useSyncExternalStore:su;na.exports=ra;var nu=na.exports;const ru=Gc.useInsertionEffect,iu=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ou=iu?m.useLayoutEffect:m.useEffect,au=ru||ou,ia=e=>{const t=m.useRef([e,(...s)=>t[0](...s)]).current;return au(()=>{t[0]=e}),t[1]},lu="popstate",nr="pushState",rr="replaceState",cu="hashchange",Yr=[lu,nr,rr,cu],uu=e=>{for(const t of Yr)addEventListener(t,e);return()=>{for(const t of Yr)removeEventListener(t,e)}},oa=(e,t)=>nu.useSyncExternalStore(uu,e,t),du=()=>location.search,hu=({ssrSearch:e=""}={})=>oa(du,()=>e),Xr=()=>location.pathname,fu=({ssrPath:e}={})=>oa(Xr,e?()=>e:Xr),pu=(e,{replace:t=!1,state:s=null}={})=>history[t?rr:nr](s,"",e),mu=(e={})=>[fu(e),pu],Zr=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Zr]>"u"){for(const e of[nr,rr]){const t=history[e];history[e]=function(){const s=t.apply(this,arguments),n=new Event(e);return n.arguments=arguments,dispatchEvent(n),s}}Object.defineProperty(window,Zr,{value:!0})}const yu=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",aa=(e="")=>e==="/"?"":e,gu=(e,t)=>e[0]==="~"?e.slice(1):aa(t)+e,xu=(e="",t)=>yu(Jr(aa(e)),Jr(t)),Jr=e=>{try{return decodeURI(e)}catch{return e}},la={hook:mu,searchHook:hu,parser:qc,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},ca=m.createContext(la),_s=()=>m.useContext(ca),ua={},da=m.createContext(ua),vu=()=>m.useContext(da),ir=e=>{const[t,s]=e.hook(e);return[xu(e.base,t),ia((n,r)=>s(gu(n,e.base),r))]},ha=(e,t,s,n)=>{const{pattern:r,keys:o}=t instanceof RegExp?{keys:!1,pattern:t}:e(t||"*",n),i=r.exec(s)||[],[a,...l]=i;return a!==void 0?[!0,(()=>{const c=o!==!1?Object.fromEntries(o.map((h,f)=>[h,l[f]])):i.groups;let u={...l};return c&&Object.assign(u,c),u})(),...n?[a]:[]]:[!1,null]},bu=({children:e,...t})=>{var u,h;const s=_s(),n=t.hook?la:s;let r=n;const[o,i]=((u=t.ssrPath)==null?void 0:u.split("?"))??[];i&&(t.ssrSearch=i,t.ssrPath=o),t.hrefs=t.hrefs??((h=t.hook)==null?void 0:h.hrefs);let a=m.useRef({}),l=a.current,c=l;for(let f in n){const y=f==="base"?n[f]+(t[f]||""):t[f]||n[f];l===c&&y!==c[f]&&(a.current=c={...c}),c[f]=y,y!==n[f]&&(r=c)}return m.createElement(ca.Provider,{value:r,children:e})},ei=({children:e,component:t},s)=>t?m.createElement(t,{params:s}):typeof e=="function"?e(s):e,wu=e=>{let t=m.useRef(ua),s=t.current;for(const n in e)e[n]!==s[n]&&(s=e);return Object.keys(e).length===0&&(s=e),t.current=s},ti=({path:e,nest:t,match:s,...n})=>{const r=_s(),[o]=ir(r),[i,a,l]=s??ha(r.parser,e,o,t),c=wu({...vu(),...a});if(!i)return null;const u=l?m.createElement(bu,{base:l},ei(n,c)):ei(n,c);return m.createElement(da.Provider,{value:c,children:u})};m.forwardRef((e,t)=>{const s=_s(),[n,r]=ir(s),{to:o="",href:i=o,onClick:a,asChild:l,children:c,className:u,replace:h,state:f,...y}=e,x=ia(v=>{v.ctrlKey||v.metaKey||v.altKey||v.shiftKey||v.button!==0||(a==null||a(v),v.defaultPrevented||(v.preventDefault(),r(i,e)))}),g=s.hrefs(i[0]==="~"?i.slice(1):s.base+i,s);return l&&m.isValidElement(c)?m.cloneElement(c,{onClick:x,href:g}):m.createElement("a",{...y,onClick:x,href:g,className:u!=null&&u.call?u(n===i):u,children:c,ref:t})});const fa=e=>Array.isArray(e)?e.flatMap(t=>fa(t&&t.type===m.Fragment?t.props.children:t)):[e],Tu=({children:e,location:t})=>{const s=_s(),[n]=ir(s);for(const r of fa(e)){let o=0;if(m.isValidElement(r)&&(o=ha(s.parser,r.props.path,t||n,r.props.nest))[0])return m.cloneElement(r,{match:o})}return null};var Ut=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},ut=typeof window>"u"||"Deno"in globalThis;function de(){}function Pu(e,t){return typeof e=="function"?e(t):e}function Tn(e){return typeof e=="number"&&e>=0&&e!==1/0}function pa(e,t){return Math.max(e+(t||0)-Date.now(),0)}function wt(e,t){return typeof e=="function"?e(t):e}function pe(e,t){return typeof e=="function"?e(t):e}function si(e,t){const{type:s="all",exact:n,fetchStatus:r,predicate:o,queryKey:i,stale:a}=e;if(i){if(n){if(t.queryHash!==or(i,t.options))return!1}else if(!rs(t.queryKey,i))return!1}if(s!=="all"){const l=t.isActive();if(s==="active"&&!l||s==="inactive"&&l)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||r&&r!==t.state.fetchStatus||o&&!o(t))}function ni(e,t){const{exact:s,status:n,predicate:r,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(s){if(dt(t.options.mutationKey)!==dt(o))return!1}else if(!rs(t.options.mutationKey,o))return!1}return!(n&&t.state.status!==n||r&&!r(t))}function or(e,t){return((t==null?void 0:t.queryKeyHashFn)||dt)(e)}function dt(e){return JSON.stringify(e,(t,s)=>Pn(s)?Object.keys(s).sort().reduce((n,r)=>(n[r]=s[r],n),{}):s)}function rs(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(s=>!rs(e[s],t[s])):!1}function ma(e,t){if(e===t)return e;const s=ri(e)&&ri(t);if(s||Pn(e)&&Pn(t)){const n=s?e:Object.keys(e),r=n.length,o=s?t:Object.keys(t),i=o.length,a=s?[]:{};let l=0;for(let c=0;c<i;c++){const u=s?c:o[c];(!s&&n.includes(u)||s)&&e[u]===void 0&&t[u]===void 0?(a[u]=void 0,l++):(a[u]=ma(e[u],t[u]),a[u]===e[u]&&e[u]!==void 0&&l++)}return r===i&&l===r?e:a}return t}function Ns(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function ri(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Pn(e){if(!ii(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!ii(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function ii(e){return Object.prototype.toString.call(e)==="[object Object]"}function Su(e){return new Promise(t=>{setTimeout(t,e)})}function Sn(e,t,s){return typeof s.structuralSharing=="function"?s.structuralSharing(e,t):s.structuralSharing!==!1?ma(e,t):t}function Cu(e,t,s=0){const n=[...e,t];return s&&n.length>s?n.slice(1):n}function ju(e,t,s=0){const n=[t,...e];return s&&n.length>s?n.slice(0,-1):n}var ar=Symbol();function ya(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===ar?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var st,Fe,Pt,Fo,Au=(Fo=class extends Ut{constructor(){super();E(this,st);E(this,Fe);E(this,Pt);C(this,Pt,t=>{if(!ut&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){p(this,Fe)||this.setEventListener(p(this,Pt))}onUnsubscribe(){var t;this.hasListeners()||((t=p(this,Fe))==null||t.call(this),C(this,Fe,void 0))}setEventListener(t){var s;C(this,Pt,t),(s=p(this,Fe))==null||s.call(this),C(this,Fe,t(n=>{typeof n=="boolean"?this.setFocused(n):this.onFocus()}))}setFocused(t){p(this,st)!==t&&(C(this,st,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){var t;return typeof p(this,st)=="boolean"?p(this,st):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},st=new WeakMap,Fe=new WeakMap,Pt=new WeakMap,Fo),lr=new Au,St,Be,Ct,Bo,Eu=(Bo=class extends Ut{constructor(){super();E(this,St,!0);E(this,Be);E(this,Ct);C(this,Ct,t=>{if(!ut&&window.addEventListener){const s=()=>t(!0),n=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",n)}}})}onSubscribe(){p(this,Be)||this.setEventListener(p(this,Ct))}onUnsubscribe(){var t;this.hasListeners()||((t=p(this,Be))==null||t.call(this),C(this,Be,void 0))}setEventListener(t){var s;C(this,Ct,t),(s=p(this,Be))==null||s.call(this),C(this,Be,t(this.setOnline.bind(this)))}setOnline(t){p(this,St)!==t&&(C(this,St,t),this.listeners.forEach(n=>{n(t)}))}isOnline(){return p(this,St)}},St=new WeakMap,Be=new WeakMap,Ct=new WeakMap,Bo),Ds=new Eu;function Cn(){let e,t;const s=new Promise((r,o)=>{e=r,t=o});s.status="pending",s.catch(()=>{});function n(r){Object.assign(s,r),delete s.resolve,delete s.reject}return s.resolve=r=>{n({status:"fulfilled",value:r}),e(r)},s.reject=r=>{n({status:"rejected",reason:r}),t(r)},s}function Ru(e){return Math.min(1e3*2**e,3e4)}function ga(e){return(e??"online")==="online"?Ds.isOnline():!0}var xa=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function tn(e){return e instanceof xa}function va(e){let t=!1,s=0,n=!1,r;const o=Cn(),i=g=>{var v;n||(f(new xa(g)),(v=e.abort)==null||v.call(e))},a=()=>{t=!0},l=()=>{t=!1},c=()=>lr.isFocused()&&(e.networkMode==="always"||Ds.isOnline())&&e.canRun(),u=()=>ga(e.networkMode)&&e.canRun(),h=g=>{var v;n||(n=!0,(v=e.onSuccess)==null||v.call(e,g),r==null||r(),o.resolve(g))},f=g=>{var v;n||(n=!0,(v=e.onError)==null||v.call(e,g),r==null||r(),o.reject(g))},y=()=>new Promise(g=>{var v;r=b=>{(n||c())&&g(b)},(v=e.onPause)==null||v.call(e)}).then(()=>{var g;r=void 0,n||(g=e.onContinue)==null||g.call(e)}),x=()=>{if(n)return;let g;const v=s===0?e.initialPromise:void 0;try{g=v??e.fn()}catch(b){g=Promise.reject(b)}Promise.resolve(g).then(h).catch(b=>{var k;if(n)return;const w=e.retry??(ut?0:3),T=e.retryDelay??Ru,j=typeof T=="function"?T(s,b):T,S=w===!0||typeof w=="number"&&s<w||typeof w=="function"&&w(s,b);if(t||!S){f(b);return}s++,(k=e.onFail)==null||k.call(e,s,b),Su(j).then(()=>c()?void 0:y()).then(()=>{t?f(b):x()})})};return{promise:o,cancel:i,continue:()=>(r==null||r(),o),cancelRetry:a,continueRetry:l,canStart:u,start:()=>(u()?x():y().then(x),o)}}function ku(){let e=[],t=0,s=a=>{a()},n=a=>{a()},r=a=>setTimeout(a,0);const o=a=>{t?e.push(a):r(()=>{s(a)})},i=()=>{const a=e;e=[],a.length&&r(()=>{n(()=>{a.forEach(l=>{s(l)})})})};return{batch:a=>{let l;t++;try{l=a()}finally{t--,t||i()}return l},batchCalls:a=>(...l)=>{o(()=>{a(...l)})},schedule:o,setNotifyFunction:a=>{s=a},setBatchNotifyFunction:a=>{n=a},setScheduler:a=>{r=a}}}var G=ku(),nt,Uo,ba=(Uo=class{constructor(){E(this,nt)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Tn(this.gcTime)&&C(this,nt,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(ut?1/0:5*60*1e3))}clearGcTimeout(){p(this,nt)&&(clearTimeout(p(this,nt)),C(this,nt,void 0))}},nt=new WeakMap,Uo),jt,At,ue,X,ls,rt,fe,je,_o,Mu=(_o=class extends ba{constructor(t){super();E(this,fe);E(this,jt);E(this,At);E(this,ue);E(this,X);E(this,ls);E(this,rt);C(this,rt,!1),C(this,ls,t.defaultOptions),this.setOptions(t.options),this.observers=[],C(this,ue,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,C(this,jt,Nu(this.options)),this.state=t.state??p(this,jt),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=p(this,X))==null?void 0:t.promise}setOptions(t){this.options={...p(this,ls),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&p(this,ue).remove(this)}setData(t,s){const n=Sn(this.state.data,t,this.options);return D(this,fe,je).call(this,{data:n,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),n}setState(t,s){D(this,fe,je).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){var n,r;const s=(n=p(this,X))==null?void 0:n.promise;return(r=p(this,X))==null||r.cancel(t),s?s.then(de).catch(de):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(p(this,jt))}isActive(){return this.observers.some(t=>pe(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ar||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!pa(this.state.dataUpdatedAt,t)}onFocus(){var s;const t=this.observers.find(n=>n.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(s=p(this,X))==null||s.continue()}onOnline(){var s;const t=this.observers.find(n=>n.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(s=p(this,X))==null||s.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),p(this,ue).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(p(this,X)&&(p(this,rt)?p(this,X).cancel({revert:!0}):p(this,X).cancelRetry()),this.scheduleGc()),p(this,ue).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||D(this,fe,je).call(this,{type:"invalidate"})}fetch(t,s){var l,c,u;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(p(this,X))return p(this,X).continueRetry(),p(this,X).promise}if(t&&this.setOptions(t),!this.options.queryFn){const h=this.observers.find(f=>f.options.queryFn);h&&this.setOptions(h.options)}const n=new AbortController,r=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(C(this,rt,!0),n.signal)})},o=()=>{const h=ya(this.options,s),f={queryKey:this.queryKey,meta:this.meta};return r(f),C(this,rt,!1),this.options.persister?this.options.persister(h,f,this):h(f)},i={fetchOptions:s,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:o};r(i),(l=this.options.behavior)==null||l.onFetch(i,this),C(this,At,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=i.fetchOptions)==null?void 0:c.meta))&&D(this,fe,je).call(this,{type:"fetch",meta:(u=i.fetchOptions)==null?void 0:u.meta});const a=h=>{var f,y,x,g;tn(h)&&h.silent||D(this,fe,je).call(this,{type:"error",error:h}),tn(h)||((y=(f=p(this,ue).config).onError)==null||y.call(f,h,this),(g=(x=p(this,ue).config).onSettled)==null||g.call(x,this.state.data,h,this)),this.scheduleGc()};return C(this,X,va({initialPromise:s==null?void 0:s.initialPromise,fn:i.fetchFn,abort:n.abort.bind(n),onSuccess:h=>{var f,y,x,g;if(h===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(v){a(v);return}(y=(f=p(this,ue).config).onSuccess)==null||y.call(f,h,this),(g=(x=p(this,ue).config).onSettled)==null||g.call(x,h,this.state.error,this),this.scheduleGc()},onError:a,onFail:(h,f)=>{D(this,fe,je).call(this,{type:"failed",failureCount:h,error:f})},onPause:()=>{D(this,fe,je).call(this,{type:"pause"})},onContinue:()=>{D(this,fe,je).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),p(this,X).start()}},jt=new WeakMap,At=new WeakMap,ue=new WeakMap,X=new WeakMap,ls=new WeakMap,rt=new WeakMap,fe=new WeakSet,je=function(t){const s=n=>{switch(t.type){case"failed":return{...n,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...n,fetchStatus:"paused"};case"continue":return{...n,fetchStatus:"fetching"};case"fetch":return{...n,...wa(n.data,this.options),fetchMeta:t.meta??null};case"success":return{...n,data:t.data,dataUpdateCount:n.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=t.error;return tn(r)&&r.revert&&p(this,At)?{...p(this,At),fetchStatus:"idle"}:{...n,error:r,errorUpdateCount:n.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:n.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...n,isInvalidated:!0};case"setState":return{...n,...t.state}}};this.state=s(this.state),G.batch(()=>{this.observers.forEach(n=>{n.onQueryUpdate()}),p(this,ue).notify({query:this,type:"updated",action:t})})},_o);function wa(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:ga(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function Nu(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,n=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var ye,zo,Du=(zo=class extends Ut{constructor(t={}){super();E(this,ye);this.config=t,C(this,ye,new Map)}build(t,s,n){const r=s.queryKey,o=s.queryHash??or(r,s);let i=this.get(o);return i||(i=new Mu({cache:this,queryKey:r,queryHash:o,options:t.defaultQueryOptions(s),state:n,defaultOptions:t.getQueryDefaults(r)}),this.add(i)),i}add(t){p(this,ye).has(t.queryHash)||(p(this,ye).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=p(this,ye).get(t.queryHash);s&&(t.destroy(),s===t&&p(this,ye).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){G.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return p(this,ye).get(t)}getAll(){return[...p(this,ye).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(n=>si(s,n))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(n=>si(t,n)):s}notify(t){G.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){G.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){G.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},ye=new WeakMap,zo),ge,ee,it,xe,Oe,Ho,Vu=(Ho=class extends ba{constructor(t){super();E(this,xe);E(this,ge);E(this,ee);E(this,it);this.mutationId=t.mutationId,C(this,ee,t.mutationCache),C(this,ge,[]),this.state=t.state||Ta(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){p(this,ge).includes(t)||(p(this,ge).push(t),this.clearGcTimeout(),p(this,ee).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){C(this,ge,p(this,ge).filter(s=>s!==t)),this.scheduleGc(),p(this,ee).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){p(this,ge).length||(this.state.status==="pending"?this.scheduleGc():p(this,ee).remove(this))}continue(){var t;return((t=p(this,it))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var r,o,i,a,l,c,u,h,f,y,x,g,v,b,w,T,j,S,k,V;C(this,it,va({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(A,M)=>{D(this,xe,Oe).call(this,{type:"failed",failureCount:A,error:M})},onPause:()=>{D(this,xe,Oe).call(this,{type:"pause"})},onContinue:()=>{D(this,xe,Oe).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>p(this,ee).canRun(this)}));const s=this.state.status==="pending",n=!p(this,it).canStart();try{if(!s){D(this,xe,Oe).call(this,{type:"pending",variables:t,isPaused:n}),await((o=(r=p(this,ee).config).onMutate)==null?void 0:o.call(r,t,this));const M=await((a=(i=this.options).onMutate)==null?void 0:a.call(i,t));M!==this.state.context&&D(this,xe,Oe).call(this,{type:"pending",context:M,variables:t,isPaused:n})}const A=await p(this,it).start();return await((c=(l=p(this,ee).config).onSuccess)==null?void 0:c.call(l,A,t,this.state.context,this)),await((h=(u=this.options).onSuccess)==null?void 0:h.call(u,A,t,this.state.context)),await((y=(f=p(this,ee).config).onSettled)==null?void 0:y.call(f,A,null,this.state.variables,this.state.context,this)),await((g=(x=this.options).onSettled)==null?void 0:g.call(x,A,null,t,this.state.context)),D(this,xe,Oe).call(this,{type:"success",data:A}),A}catch(A){try{throw await((b=(v=p(this,ee).config).onError)==null?void 0:b.call(v,A,t,this.state.context,this)),await((T=(w=this.options).onError)==null?void 0:T.call(w,A,t,this.state.context)),await((S=(j=p(this,ee).config).onSettled)==null?void 0:S.call(j,void 0,A,this.state.variables,this.state.context,this)),await((V=(k=this.options).onSettled)==null?void 0:V.call(k,void 0,A,t,this.state.context)),A}finally{D(this,xe,Oe).call(this,{type:"error",error:A})}}finally{p(this,ee).runNext(this)}}},ge=new WeakMap,ee=new WeakMap,it=new WeakMap,xe=new WeakSet,Oe=function(t){const s=n=>{switch(t.type){case"failed":return{...n,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...n,isPaused:!0};case"continue":return{...n,isPaused:!1};case"pending":return{...n,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...n,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...n,data:void 0,error:t.error,failureCount:n.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),G.batch(()=>{p(this,ge).forEach(n=>{n.onMutationUpdate(t)}),p(this,ee).notify({mutation:this,type:"updated",action:t})})},Ho);function Ta(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var ie,cs,Ko,Ou=(Ko=class extends Ut{constructor(t={}){super();E(this,ie);E(this,cs);this.config=t,C(this,ie,new Map),C(this,cs,Date.now())}build(t,s,n){const r=new Vu({mutationCache:this,mutationId:++xs(this,cs)._,options:t.defaultMutationOptions(s),state:n});return this.add(r),r}add(t){const s=vs(t),n=p(this,ie).get(s)??[];n.push(t),p(this,ie).set(s,n),this.notify({type:"added",mutation:t})}remove(t){var n;const s=vs(t);if(p(this,ie).has(s)){const r=(n=p(this,ie).get(s))==null?void 0:n.filter(o=>o!==t);r&&(r.length===0?p(this,ie).delete(s):p(this,ie).set(s,r))}this.notify({type:"removed",mutation:t})}canRun(t){var n;const s=(n=p(this,ie).get(vs(t)))==null?void 0:n.find(r=>r.state.status==="pending");return!s||s===t}runNext(t){var n;const s=(n=p(this,ie).get(vs(t)))==null?void 0:n.find(r=>r!==t&&r.state.isPaused);return(s==null?void 0:s.continue())??Promise.resolve()}clear(){G.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...p(this,ie).values()].flat()}find(t){const s={exact:!0,...t};return this.getAll().find(n=>ni(s,n))}findAll(t={}){return this.getAll().filter(s=>ni(t,s))}notify(t){G.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return G.batch(()=>Promise.all(t.map(s=>s.continue().catch(de))))}},ie=new WeakMap,cs=new WeakMap,Ko);function vs(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function oi(e){return{onFetch:(t,s)=>{var u,h,f,y,x;const n=t.options,r=(f=(h=(u=t.fetchOptions)==null?void 0:u.meta)==null?void 0:h.fetchMore)==null?void 0:f.direction,o=((y=t.state.data)==null?void 0:y.pages)||[],i=((x=t.state.data)==null?void 0:x.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const c=async()=>{let g=!1;const v=T=>{Object.defineProperty(T,"signal",{enumerable:!0,get:()=>(t.signal.aborted?g=!0:t.signal.addEventListener("abort",()=>{g=!0}),t.signal)})},b=ya(t.options,t.fetchOptions),w=async(T,j,S)=>{if(g)return Promise.reject();if(j==null&&T.pages.length)return Promise.resolve(T);const k={queryKey:t.queryKey,pageParam:j,direction:S?"backward":"forward",meta:t.options.meta};v(k);const V=await b(k),{maxPages:A}=t.options,M=S?ju:Cu;return{pages:M(T.pages,V,A),pageParams:M(T.pageParams,j,A)}};if(r&&o.length){const T=r==="backward",j=T?Lu:ai,S={pages:o,pageParams:i},k=j(n,S);a=await w(S,k,T)}else{const T=e??o.length;do{const j=l===0?i[0]??n.initialPageParam:ai(n,a);if(l>0&&j==null)break;a=await w(a,j),l++}while(l<T)}return a};t.options.persister?t.fetchFn=()=>{var g,v;return(v=(g=t.options).persister)==null?void 0:v.call(g,c,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s)}:t.fetchFn=c}}}function ai(e,{pages:t,pageParams:s}){const n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,s[n],s):void 0}function Lu(e,{pages:t,pageParams:s}){var n;return t.length>0?(n=e.getPreviousPageParam)==null?void 0:n.call(e,t[0],t,s[0],s):void 0}var H,Ue,_e,Et,Rt,ze,kt,Mt,Wo,Iu=(Wo=class{constructor(e={}){E(this,H);E(this,Ue);E(this,_e);E(this,Et);E(this,Rt);E(this,ze);E(this,kt);E(this,Mt);C(this,H,e.queryCache||new Du),C(this,Ue,e.mutationCache||new Ou),C(this,_e,e.defaultOptions||{}),C(this,Et,new Map),C(this,Rt,new Map),C(this,ze,0)}mount(){xs(this,ze)._++,p(this,ze)===1&&(C(this,kt,lr.subscribe(async e=>{e&&(await this.resumePausedMutations(),p(this,H).onFocus())})),C(this,Mt,Ds.subscribe(async e=>{e&&(await this.resumePausedMutations(),p(this,H).onOnline())})))}unmount(){var e,t;xs(this,ze)._--,p(this,ze)===0&&((e=p(this,kt))==null||e.call(this),C(this,kt,void 0),(t=p(this,Mt))==null||t.call(this),C(this,Mt,void 0))}isFetching(e){return p(this,H).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return p(this,Ue).findAll({...e,status:"pending"}).length}getQueryData(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=p(this,H).get(t.queryHash))==null?void 0:s.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const s=this.defaultQueryOptions(e),n=p(this,H).build(this,s);return e.revalidateIfStale&&n.isStaleByTime(wt(s.staleTime,n))&&this.prefetchQuery(s),Promise.resolve(t)}}getQueriesData(e){return p(this,H).findAll(e).map(({queryKey:t,state:s})=>{const n=s.data;return[t,n]})}setQueryData(e,t,s){const n=this.defaultQueryOptions({queryKey:e}),r=p(this,H).get(n.queryHash),o=r==null?void 0:r.state.data,i=Pu(t,o);if(i!==void 0)return p(this,H).build(this,n).setData(i,{...s,manual:!0})}setQueriesData(e,t,s){return G.batch(()=>p(this,H).findAll(e).map(({queryKey:n})=>[n,this.setQueryData(n,t,s)]))}getQueryState(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=p(this,H).get(t.queryHash))==null?void 0:s.state}removeQueries(e){const t=p(this,H);G.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=p(this,H),n={type:"active",...e};return G.batch(()=>(s.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries(n,t)))}cancelQueries(e={},t={}){const s={revert:!0,...t},n=G.batch(()=>p(this,H).findAll(e).map(r=>r.cancel(s)));return Promise.all(n).then(de).catch(de)}invalidateQueries(e={},t={}){return G.batch(()=>{if(p(this,H).findAll(e).forEach(n=>{n.invalidate()}),e.refetchType==="none")return Promise.resolve();const s={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(s,t)})}refetchQueries(e={},t){const s={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},n=G.batch(()=>p(this,H).findAll(e).filter(r=>!r.isDisabled()).map(r=>{let o=r.fetch(void 0,s);return s.throwOnError||(o=o.catch(de)),r.state.fetchStatus==="paused"?Promise.resolve():o}));return Promise.all(n).then(de)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=p(this,H).build(this,t);return s.isStaleByTime(wt(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(de).catch(de)}fetchInfiniteQuery(e){return e.behavior=oi(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(de).catch(de)}ensureInfiniteQueryData(e){return e.behavior=oi(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Ds.isOnline()?p(this,Ue).resumePausedMutations():Promise.resolve()}getQueryCache(){return p(this,H)}getMutationCache(){return p(this,Ue)}getDefaultOptions(){return p(this,_e)}setDefaultOptions(e){C(this,_e,e)}setQueryDefaults(e,t){p(this,Et).set(dt(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...p(this,Et).values()];let s={};return t.forEach(n=>{rs(e,n.queryKey)&&(s={...s,...n.defaultOptions})}),s}setMutationDefaults(e,t){p(this,Rt).set(dt(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...p(this,Rt).values()];let s={};return t.forEach(n=>{rs(e,n.mutationKey)&&(s={...s,...n.defaultOptions})}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...p(this,_e).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=or(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===ar&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...p(this,_e).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){p(this,H).clear(),p(this,Ue).clear()}},H=new WeakMap,Ue=new WeakMap,_e=new WeakMap,Et=new WeakMap,Rt=new WeakMap,ze=new WeakMap,kt=new WeakMap,Mt=new WeakMap,Wo),se,O,us,te,ot,Nt,He,ve,ds,Dt,Vt,at,lt,Ke,Ot,F,Qt,jn,An,En,Rn,kn,Mn,Nn,Pa,Go,Fu=(Go=class extends Ut{constructor(t,s){super();E(this,F);E(this,se);E(this,O);E(this,us);E(this,te);E(this,ot);E(this,Nt);E(this,He);E(this,ve);E(this,ds);E(this,Dt);E(this,Vt);E(this,at);E(this,lt);E(this,Ke);E(this,Ot,new Set);this.options=s,C(this,se,t),C(this,ve,null),C(this,He,Cn()),this.options.experimental_prefetchInRender||p(this,He).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(p(this,O).addObserver(this),li(p(this,O),this.options)?D(this,F,Qt).call(this):this.updateResult(),D(this,F,Rn).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Dn(p(this,O),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Dn(p(this,O),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,D(this,F,kn).call(this),D(this,F,Mn).call(this),p(this,O).removeObserver(this)}setOptions(t,s){const n=this.options,r=p(this,O);if(this.options=p(this,se).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof pe(this.options.enabled,p(this,O))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");D(this,F,Nn).call(this),p(this,O).setOptions(this.options),n._defaulted&&!Ns(this.options,n)&&p(this,se).getQueryCache().notify({type:"observerOptionsUpdated",query:p(this,O),observer:this});const o=this.hasListeners();o&&ci(p(this,O),r,this.options,n)&&D(this,F,Qt).call(this),this.updateResult(s),o&&(p(this,O)!==r||pe(this.options.enabled,p(this,O))!==pe(n.enabled,p(this,O))||wt(this.options.staleTime,p(this,O))!==wt(n.staleTime,p(this,O)))&&D(this,F,jn).call(this);const i=D(this,F,An).call(this);o&&(p(this,O)!==r||pe(this.options.enabled,p(this,O))!==pe(n.enabled,p(this,O))||i!==p(this,Ke))&&D(this,F,En).call(this,i)}getOptimisticResult(t){const s=p(this,se).getQueryCache().build(p(this,se),t),n=this.createResult(s,t);return Uu(this,n)&&(C(this,te,n),C(this,Nt,this.options),C(this,ot,p(this,O).state)),n}getCurrentResult(){return p(this,te)}trackResult(t,s){const n={};return Object.keys(t).forEach(r=>{Object.defineProperty(n,r,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(r),s==null||s(r),t[r])})}),n}trackProp(t){p(this,Ot).add(t)}getCurrentQuery(){return p(this,O)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=p(this,se).defaultQueryOptions(t),n=p(this,se).getQueryCache().build(p(this,se),s);return n.fetch().then(()=>this.createResult(n,s))}fetch(t){return D(this,F,Qt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),p(this,te)))}createResult(t,s){var A;const n=p(this,O),r=this.options,o=p(this,te),i=p(this,ot),a=p(this,Nt),c=t!==n?t.state:p(this,us),{state:u}=t;let h={...u},f=!1,y;if(s._optimisticResults){const M=this.hasListeners(),U=!M&&li(t,s),Y=M&&ci(t,n,s,r);(U||Y)&&(h={...h,...wa(u.data,t.options)}),s._optimisticResults==="isRestoring"&&(h.fetchStatus="idle")}let{error:x,errorUpdatedAt:g,status:v}=h;if(s.select&&h.data!==void 0)if(o&&h.data===(i==null?void 0:i.data)&&s.select===p(this,ds))y=p(this,Dt);else try{C(this,ds,s.select),y=s.select(h.data),y=Sn(o==null?void 0:o.data,y,s),C(this,Dt,y),C(this,ve,null)}catch(M){C(this,ve,M)}else y=h.data;if(s.placeholderData!==void 0&&y===void 0&&v==="pending"){let M;if(o!=null&&o.isPlaceholderData&&s.placeholderData===(a==null?void 0:a.placeholderData))M=o.data;else if(M=typeof s.placeholderData=="function"?s.placeholderData((A=p(this,Vt))==null?void 0:A.state.data,p(this,Vt)):s.placeholderData,s.select&&M!==void 0)try{M=s.select(M),C(this,ve,null)}catch(U){C(this,ve,U)}M!==void 0&&(v="success",y=Sn(o==null?void 0:o.data,M,s),f=!0)}p(this,ve)&&(x=p(this,ve),y=p(this,Dt),g=Date.now(),v="error");const b=h.fetchStatus==="fetching",w=v==="pending",T=v==="error",j=w&&b,S=y!==void 0,V={status:v,fetchStatus:h.fetchStatus,isPending:w,isSuccess:v==="success",isError:T,isInitialLoading:j,isLoading:j,data:y,dataUpdatedAt:h.dataUpdatedAt,error:x,errorUpdatedAt:g,failureCount:h.fetchFailureCount,failureReason:h.fetchFailureReason,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>c.dataUpdateCount||h.errorUpdateCount>c.errorUpdateCount,isFetching:b,isRefetching:b&&!w,isLoadingError:T&&!S,isPaused:h.fetchStatus==="paused",isPlaceholderData:f,isRefetchError:T&&S,isStale:cr(t,s),refetch:this.refetch,promise:p(this,He)};if(this.options.experimental_prefetchInRender){const M=I=>{V.status==="error"?I.reject(V.error):V.data!==void 0&&I.resolve(V.data)},U=()=>{const I=C(this,He,V.promise=Cn());M(I)},Y=p(this,He);switch(Y.status){case"pending":t.queryHash===n.queryHash&&M(Y);break;case"fulfilled":(V.status==="error"||V.data!==Y.value)&&U();break;case"rejected":(V.status!=="error"||V.error!==Y.reason)&&U();break}}return V}updateResult(t){const s=p(this,te),n=this.createResult(p(this,O),this.options);if(C(this,ot,p(this,O).state),C(this,Nt,this.options),p(this,ot).data!==void 0&&C(this,Vt,p(this,O)),Ns(n,s))return;C(this,te,n);const r={},o=()=>{if(!s)return!0;const{notifyOnChangeProps:i}=this.options,a=typeof i=="function"?i():i;if(a==="all"||!a&&!p(this,Ot).size)return!0;const l=new Set(a??p(this,Ot));return this.options.throwOnError&&l.add("error"),Object.keys(p(this,te)).some(c=>{const u=c;return p(this,te)[u]!==s[u]&&l.has(u)})};(t==null?void 0:t.listeners)!==!1&&o()&&(r.listeners=!0),D(this,F,Pa).call(this,{...r,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&D(this,F,Rn).call(this)}},se=new WeakMap,O=new WeakMap,us=new WeakMap,te=new WeakMap,ot=new WeakMap,Nt=new WeakMap,He=new WeakMap,ve=new WeakMap,ds=new WeakMap,Dt=new WeakMap,Vt=new WeakMap,at=new WeakMap,lt=new WeakMap,Ke=new WeakMap,Ot=new WeakMap,F=new WeakSet,Qt=function(t){D(this,F,Nn).call(this);let s=p(this,O).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(de)),s},jn=function(){D(this,F,kn).call(this);const t=wt(this.options.staleTime,p(this,O));if(ut||p(this,te).isStale||!Tn(t))return;const n=pa(p(this,te).dataUpdatedAt,t)+1;C(this,at,setTimeout(()=>{p(this,te).isStale||this.updateResult()},n))},An=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(p(this,O)):this.options.refetchInterval)??!1},En=function(t){D(this,F,Mn).call(this),C(this,Ke,t),!(ut||pe(this.options.enabled,p(this,O))===!1||!Tn(p(this,Ke))||p(this,Ke)===0)&&C(this,lt,setInterval(()=>{(this.options.refetchIntervalInBackground||lr.isFocused())&&D(this,F,Qt).call(this)},p(this,Ke)))},Rn=function(){D(this,F,jn).call(this),D(this,F,En).call(this,D(this,F,An).call(this))},kn=function(){p(this,at)&&(clearTimeout(p(this,at)),C(this,at,void 0))},Mn=function(){p(this,lt)&&(clearInterval(p(this,lt)),C(this,lt,void 0))},Nn=function(){const t=p(this,se).getQueryCache().build(p(this,se),this.options);if(t===p(this,O))return;const s=p(this,O);C(this,O,t),C(this,us,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},Pa=function(t){G.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(p(this,te))}),p(this,se).getQueryCache().notify({query:p(this,O),type:"observerResultsUpdated"})})},Go);function Bu(e,t){return pe(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function li(e,t){return Bu(e,t)||e.state.data!==void 0&&Dn(e,t,t.refetchOnMount)}function Dn(e,t,s){if(pe(t.enabled,e)!==!1){const n=typeof s=="function"?s(e):s;return n==="always"||n!==!1&&cr(e,t)}return!1}function ci(e,t,s,n){return(e!==t||pe(n.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&cr(e,s)}function cr(e,t){return pe(t.enabled,e)!==!1&&e.isStaleByTime(wt(t.staleTime,e))}function Uu(e,t){return!Ns(e.getCurrentResult(),t)}var We,Ge,ne,Ae,Me,Ss,Vn,qo,_u=(qo=class extends Ut{constructor(s,n){super();E(this,Me);E(this,We);E(this,Ge);E(this,ne);E(this,Ae);C(this,We,s),this.setOptions(n),this.bindMethods(),D(this,Me,Ss).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(s){var r;const n=this.options;this.options=p(this,We).defaultMutationOptions(s),Ns(this.options,n)||p(this,We).getMutationCache().notify({type:"observerOptionsUpdated",mutation:p(this,ne),observer:this}),n!=null&&n.mutationKey&&this.options.mutationKey&&dt(n.mutationKey)!==dt(this.options.mutationKey)?this.reset():((r=p(this,ne))==null?void 0:r.state.status)==="pending"&&p(this,ne).setOptions(this.options)}onUnsubscribe(){var s;this.hasListeners()||(s=p(this,ne))==null||s.removeObserver(this)}onMutationUpdate(s){D(this,Me,Ss).call(this),D(this,Me,Vn).call(this,s)}getCurrentResult(){return p(this,Ge)}reset(){var s;(s=p(this,ne))==null||s.removeObserver(this),C(this,ne,void 0),D(this,Me,Ss).call(this),D(this,Me,Vn).call(this)}mutate(s,n){var r;return C(this,Ae,n),(r=p(this,ne))==null||r.removeObserver(this),C(this,ne,p(this,We).getMutationCache().build(p(this,We),this.options)),p(this,ne).addObserver(this),p(this,ne).execute(s)}},We=new WeakMap,Ge=new WeakMap,ne=new WeakMap,Ae=new WeakMap,Me=new WeakSet,Ss=function(){var n;const s=((n=p(this,ne))==null?void 0:n.state)??Ta();C(this,Ge,{...s,isPending:s.status==="pending",isSuccess:s.status==="success",isError:s.status==="error",isIdle:s.status==="idle",mutate:this.mutate,reset:this.reset})},Vn=function(s){G.batch(()=>{var n,r,o,i,a,l,c,u;if(p(this,Ae)&&this.hasListeners()){const h=p(this,Ge).variables,f=p(this,Ge).context;(s==null?void 0:s.type)==="success"?((r=(n=p(this,Ae)).onSuccess)==null||r.call(n,s.data,h,f),(i=(o=p(this,Ae)).onSettled)==null||i.call(o,s.data,null,h,f)):(s==null?void 0:s.type)==="error"&&((l=(a=p(this,Ae)).onError)==null||l.call(a,s.error,h,f),(u=(c=p(this,Ae)).onSettled)==null||u.call(c,void 0,s.error,h,f))}this.listeners.forEach(h=>{h(p(this,Ge))})})},qo),Sa=m.createContext(void 0),ur=e=>{const t=m.useContext(Sa);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},zu=({client:e,children:t})=>(m.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),d.jsx(Sa.Provider,{value:e,children:t})),Ca=m.createContext(!1),Hu=()=>m.useContext(Ca);Ca.Provider;function Ku(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var Wu=m.createContext(Ku()),Gu=()=>m.useContext(Wu);function ja(e,t){return typeof e=="function"?e(...t):!!e}function On(){}var qu=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},$u=e=>{m.useEffect(()=>{e.clearReset()},[e])},Qu=({result:e,errorResetBoundary:t,throwOnError:s,query:n})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&ja(s,[e.error,n]),Yu=e=>{e.suspense&&(e.staleTime===void 0&&(e.staleTime=1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},Xu=(e,t)=>e.isLoading&&e.isFetching&&!t,Zu=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,ui=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function Ju(e,t,s){var u,h,f,y,x;const n=ur(),r=Hu(),o=Gu(),i=n.defaultQueryOptions(e);(h=(u=n.getDefaultOptions().queries)==null?void 0:u._experimental_beforeQuery)==null||h.call(u,i),i._optimisticResults=r?"isRestoring":"optimistic",Yu(i),qu(i,o),$u(o);const a=!n.getQueryCache().get(i.queryHash),[l]=m.useState(()=>new t(n,i)),c=l.getOptimisticResult(i);if(m.useSyncExternalStore(m.useCallback(g=>{const v=r?On:l.subscribe(G.batchCalls(g));return l.updateResult(),v},[l,r]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),m.useEffect(()=>{l.setOptions(i,{listeners:!1})},[i,l]),Zu(i,c))throw ui(i,l,o);if(Qu({result:c,errorResetBoundary:o,throwOnError:i.throwOnError,query:n.getQueryCache().get(i.queryHash)}))throw c.error;if((y=(f=n.getDefaultOptions().queries)==null?void 0:f._experimental_afterQuery)==null||y.call(f,i,c),i.experimental_prefetchInRender&&!ut&&Xu(c,r)){const g=a?ui(i,l,o):(x=n.getQueryCache().get(i.queryHash))==null?void 0:x.promise;g==null||g.catch(On).finally(()=>{l.updateResult()})}return i.notifyOnChangeProps?c:l.trackResult(c)}function Aa(e,t){return Ju(e,Fu)}function ed(e,t){const s=ur(),[n]=m.useState(()=>new _u(s,e));m.useEffect(()=>{n.setOptions(e)},[n,e]);const r=m.useSyncExternalStore(m.useCallback(i=>n.subscribe(G.batchCalls(i)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),o=m.useCallback((i,a)=>{n.mutate(i,a).catch(On)},[n]);if(r.error&&ja(n.options.throwOnError,[r.error]))throw r.error;return{...r,mutate:o,mutateAsync:r.mutate}}async function Ea(e){if(!e.ok){const t=await e.text()||e.statusText;throw new Error(`${e.status}: ${t}`)}}async function td(e,t,s){const n=await fetch(t,{method:e,headers:s?{"Content-Type":"application/json"}:{},body:s?JSON.stringify(s):void 0,credentials:"include"});return await Ea(n),n}const sd=({on401:e})=>async({queryKey:t})=>{const s=await fetch(t.join("/"),{credentials:"include"});return e==="returnNull"&&s.status===401?null:(await Ea(s),await s.json())},nd=new Iu({defaultOptions:{queries:{queryFn:sd({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}}),rd=1,id=1e6;let sn=0;function od(){return sn=(sn+1)%Number.MAX_SAFE_INTEGER,sn.toString()}const nn=new Map,di=e=>{if(nn.has(e))return;const t=setTimeout(()=>{nn.delete(e),Zt({type:"REMOVE_TOAST",toastId:e})},id);nn.set(e,t)},ad=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,rd)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(s=>s.id===t.toast.id?{...s,...t.toast}:s)};case"DISMISS_TOAST":{const{toastId:s}=t;return s?di(s):e.toasts.forEach(n=>{di(n.id)}),{...e,toasts:e.toasts.map(n=>n.id===s||s===void 0?{...n,open:!1}:n)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(s=>s.id!==t.toastId)}}},Cs=[];let js={toasts:[]};function Zt(e){js=ad(js,e),Cs.forEach(t=>{t(js)})}function ld({...e}){const t=od(),s=r=>Zt({type:"UPDATE_TOAST",toast:{...r,id:t}}),n=()=>Zt({type:"DISMISS_TOAST",toastId:t});return Zt({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:r=>{r||n()}}}),{id:t,dismiss:n,update:s}}function Ra(){const[e,t]=m.useState(js);return m.useEffect(()=>(Cs.push(t),()=>{const s=Cs.indexOf(t);s>-1&&Cs.splice(s,1)}),[e]),{...e,toast:ld,dismiss:s=>Zt({type:"DISMISS_TOAST",toastId:s})}}function ka(e){var t,s,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(s=ka(e[t]))&&(n&&(n+=" "),n+=s)}else for(s in e)e[s]&&(n&&(n+=" "),n+=s);return n}function Ma(){for(var e,t,s=0,n="",r=arguments.length;s<r;s++)(e=arguments[s])&&(t=ka(e))&&(n&&(n+=" "),n+=t);return n}const hi=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,fi=Ma,cd=(e,t)=>s=>{var n;if((t==null?void 0:t.variants)==null)return fi(e,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:r,defaultVariants:o}=t,i=Object.keys(r).map(c=>{const u=s==null?void 0:s[c],h=o==null?void 0:o[c];if(u===null)return null;const f=hi(u)||hi(h);return r[c][f]}),a=s&&Object.entries(s).reduce((c,u)=>{let[h,f]=u;return f===void 0||(c[h]=f),c},{}),l=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((c,u)=>{let{class:h,className:f,...y}=u;return Object.entries(y).every(x=>{let[g,v]=x;return Array.isArray(v)?v.includes({...o,...a}[g]):{...o,...a}[g]===v})?[...c,h,f]:c},[]);return fi(e,i,l,s==null?void 0:s.class,s==null?void 0:s.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ud=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Na=(...e)=>e.filter((t,s,n)=>!!t&&n.indexOf(t)===s).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var dd={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hd=m.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:r="",children:o,iconNode:i,...a},l)=>m.createElement("svg",{ref:l,...dd,width:t,height:t,stroke:e,strokeWidth:n?Number(s)*24/Number(t):s,className:Na("lucide",r),...a},[...i.map(([c,u])=>m.createElement(c,u)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=(e,t)=>{const s=m.forwardRef(({className:n,...r},o)=>m.createElement(hd,{ref:o,iconNode:t,className:Na(`lucide-${ud(e)}`,n),...r}));return s.displayName=`${e}`,s};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Da=z("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fd=z("Bike",[["circle",{cx:"18.5",cy:"17.5",r:"3.5",key:"15x4ox"}],["circle",{cx:"5.5",cy:"17.5",r:"3.5",key:"1noe27"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["path",{d:"M12 17.5V14l-3-3 4-3 2 3h2",key:"1npguv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pd=z("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const md=z("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yd=z("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Va=z("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gd=z("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xd=z("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vd=z("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bd=z("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oa=z("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wd=z("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const La=z("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Td=z("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pd=z("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sd=z("Sandwich",[["path",{d:"m2.37 11.223 8.372-6.777a2 2 0 0 1 2.516 0l8.371 6.777",key:"f1wd0e"}],["path",{d:"M21 15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5.25",key:"1pfu07"}],["path",{d:"M3 15a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h9",key:"1oq9qw"}],["path",{d:"m6.67 15 6.13 4.6a2 2 0 0 0 2.8-.4l3.15-4.2",key:"1fnwu5"}],["rect",{width:"20",height:"4",x:"2",y:"11",rx:"1",key:"itshg"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ln=z("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cd=z("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jd=z("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ad=z("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ed=z("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ia=z("Utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fa=z("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),dr="-",Rd=e=>{const t=Md(e),{conflictingClassGroups:s,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:i=>{const a=i.split(dr);return a[0]===""&&a.length!==1&&a.shift(),Ba(a,t)||kd(i)},getConflictingClassGroupIds:(i,a)=>{const l=s[i]||[];return a&&n[i]?[...l,...n[i]]:l}}},Ba=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const s=e[0],n=t.nextPart.get(s),r=n?Ba(e.slice(1),n):void 0;if(r)return r;if(t.validators.length===0)return;const o=e.join(dr);return(i=t.validators.find(({validator:a})=>a(o)))==null?void 0:i.classGroupId},pi=/^\[(.+)\]$/,kd=e=>{if(pi.test(e)){const t=pi.exec(e)[1],s=t==null?void 0:t.substring(0,t.indexOf(":"));if(s)return"arbitrary.."+s}},Md=e=>{const{theme:t,prefix:s}=e,n={nextPart:new Map,validators:[]};return Dd(Object.entries(e.classGroups),s).forEach(([o,i])=>{In(i,n,o,t)}),n},In=(e,t,s,n)=>{e.forEach(r=>{if(typeof r=="string"){const o=r===""?t:mi(t,r);o.classGroupId=s;return}if(typeof r=="function"){if(Nd(r)){In(r(n),t,s,n);return}t.validators.push({validator:r,classGroupId:s});return}Object.entries(r).forEach(([o,i])=>{In(i,mi(t,o),s,n)})})},mi=(e,t)=>{let s=e;return t.split(dr).forEach(n=>{s.nextPart.has(n)||s.nextPart.set(n,{nextPart:new Map,validators:[]}),s=s.nextPart.get(n)}),s},Nd=e=>e.isThemeGetter,Dd=(e,t)=>t?e.map(([s,n])=>{const r=n.map(o=>typeof o=="string"?t+o:typeof o=="object"?Object.fromEntries(Object.entries(o).map(([i,a])=>[t+i,a])):o);return[s,r]}):e,Vd=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,s=new Map,n=new Map;const r=(o,i)=>{s.set(o,i),t++,t>e&&(t=0,n=s,s=new Map)};return{get(o){let i=s.get(o);if(i!==void 0)return i;if((i=n.get(o))!==void 0)return r(o,i),i},set(o,i){s.has(o)?s.set(o,i):r(o,i)}}},Ua="!",Od=e=>{const{separator:t,experimentalParseClassName:s}=e,n=t.length===1,r=t[0],o=t.length,i=a=>{const l=[];let c=0,u=0,h;for(let v=0;v<a.length;v++){let b=a[v];if(c===0){if(b===r&&(n||a.slice(v,v+o)===t)){l.push(a.slice(u,v)),u=v+o;continue}if(b==="/"){h=v;continue}}b==="["?c++:b==="]"&&c--}const f=l.length===0?a:a.substring(u),y=f.startsWith(Ua),x=y?f.substring(1):f,g=h&&h>u?h-u:void 0;return{modifiers:l,hasImportantModifier:y,baseClassName:x,maybePostfixModifierPosition:g}};return s?a=>s({className:a,parseClassName:i}):i},Ld=e=>{if(e.length<=1)return e;const t=[];let s=[];return e.forEach(n=>{n[0]==="["?(t.push(...s.sort(),n),s=[]):s.push(n)}),t.push(...s.sort()),t},Id=e=>({cache:Vd(e.cacheSize),parseClassName:Od(e),...Rd(e)}),Fd=/\s+/,Bd=(e,t)=>{const{parseClassName:s,getClassGroupId:n,getConflictingClassGroupIds:r}=t,o=[],i=e.trim().split(Fd);let a="";for(let l=i.length-1;l>=0;l-=1){const c=i[l],{modifiers:u,hasImportantModifier:h,baseClassName:f,maybePostfixModifierPosition:y}=s(c);let x=!!y,g=n(x?f.substring(0,y):f);if(!g){if(!x){a=c+(a.length>0?" "+a:a);continue}if(g=n(f),!g){a=c+(a.length>0?" "+a:a);continue}x=!1}const v=Ld(u).join(":"),b=h?v+Ua:v,w=b+g;if(o.includes(w))continue;o.push(w);const T=r(g,x);for(let j=0;j<T.length;++j){const S=T[j];o.push(b+S)}a=c+(a.length>0?" "+a:a)}return a};function Ud(){let e=0,t,s,n="";for(;e<arguments.length;)(t=arguments[e++])&&(s=_a(t))&&(n&&(n+=" "),n+=s);return n}const _a=e=>{if(typeof e=="string")return e;let t,s="";for(let n=0;n<e.length;n++)e[n]&&(t=_a(e[n]))&&(s&&(s+=" "),s+=t);return s};function _d(e,...t){let s,n,r,o=i;function i(l){const c=t.reduce((u,h)=>h(u),e());return s=Id(c),n=s.cache.get,r=s.cache.set,o=a,a(l)}function a(l){const c=n(l);if(c)return c;const u=Bd(l,s);return r(l,u),u}return function(){return o(Ud.apply(null,arguments))}}const B=e=>{const t=s=>s[e]||[];return t.isThemeGetter=!0,t},za=/^\[(?:([a-z-]+):)?(.+)\]$/i,zd=/^\d+\/\d+$/,Hd=new Set(["px","full","screen"]),Kd=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Wd=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Gd=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,qd=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,$d=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ce=e=>Tt(e)||Hd.has(e)||zd.test(e),De=e=>_t(e,"length",sh),Tt=e=>!!e&&!Number.isNaN(Number(e)),rn=e=>_t(e,"number",Tt),Wt=e=>!!e&&Number.isInteger(Number(e)),Qd=e=>e.endsWith("%")&&Tt(e.slice(0,-1)),N=e=>za.test(e),Ve=e=>Kd.test(e),Yd=new Set(["length","size","percentage"]),Xd=e=>_t(e,Yd,Ha),Zd=e=>_t(e,"position",Ha),Jd=new Set(["image","url"]),eh=e=>_t(e,Jd,rh),th=e=>_t(e,"",nh),Gt=()=>!0,_t=(e,t,s)=>{const n=za.exec(e);return n?n[1]?typeof t=="string"?n[1]===t:t.has(n[1]):s(n[2]):!1},sh=e=>Wd.test(e)&&!Gd.test(e),Ha=()=>!1,nh=e=>qd.test(e),rh=e=>$d.test(e),ih=()=>{const e=B("colors"),t=B("spacing"),s=B("blur"),n=B("brightness"),r=B("borderColor"),o=B("borderRadius"),i=B("borderSpacing"),a=B("borderWidth"),l=B("contrast"),c=B("grayscale"),u=B("hueRotate"),h=B("invert"),f=B("gap"),y=B("gradientColorStops"),x=B("gradientColorStopPositions"),g=B("inset"),v=B("margin"),b=B("opacity"),w=B("padding"),T=B("saturate"),j=B("scale"),S=B("sepia"),k=B("skew"),V=B("space"),A=B("translate"),M=()=>["auto","contain","none"],U=()=>["auto","hidden","clip","visible","scroll"],Y=()=>["auto",N,t],I=()=>[N,t],gs=()=>["",Ce,De],Xe=()=>["auto",Tt,N],Zs=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Kt=()=>["solid","dashed","dotted","double","none"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],re=()=>["start","end","center","between","around","evenly","stretch"],Pe=()=>["","0",N],pt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Se=()=>[Tt,N];return{cacheSize:500,separator:":",theme:{colors:[Gt],spacing:[Ce,De],blur:["none","",Ve,N],brightness:Se(),borderColor:[e],borderRadius:["none","","full",Ve,N],borderSpacing:I(),borderWidth:gs(),contrast:Se(),grayscale:Pe(),hueRotate:Se(),invert:Pe(),gap:I(),gradientColorStops:[e],gradientColorStopPositions:[Qd,De],inset:Y(),margin:Y(),opacity:Se(),padding:I(),saturate:Se(),scale:Se(),sepia:Pe(),skew:Se(),space:I(),translate:I()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[Ve]}],"break-after":[{"break-after":pt()}],"break-before":[{"break-before":pt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Zs(),N]}],overflow:[{overflow:U()}],"overflow-x":[{"overflow-x":U()}],"overflow-y":[{"overflow-y":U()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Wt,N]}],basis:[{basis:Y()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:Pe()}],shrink:[{shrink:Pe()}],order:[{order:["first","last","none",Wt,N]}],"grid-cols":[{"grid-cols":[Gt]}],"col-start-end":[{col:["auto",{span:["full",Wt,N]},N]}],"col-start":[{"col-start":Xe()}],"col-end":[{"col-end":Xe()}],"grid-rows":[{"grid-rows":[Gt]}],"row-start-end":[{row:["auto",{span:[Wt,N]},N]}],"row-start":[{"row-start":Xe()}],"row-end":[{"row-end":Xe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...re()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...re(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...re(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[w]}],px:[{px:[w]}],py:[{py:[w]}],ps:[{ps:[w]}],pe:[{pe:[w]}],pt:[{pt:[w]}],pr:[{pr:[w]}],pb:[{pb:[w]}],pl:[{pl:[w]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[V]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[V]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",N,t]}],"min-w":[{"min-w":[N,t,"min","max","fit"]}],"max-w":[{"max-w":[N,t,"none","full","min","max","fit","prose",{screen:[Ve]},Ve]}],h:[{h:[N,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[N,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ve,De]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",rn]}],"font-family":[{font:[Gt]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",Tt,rn]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ce,N]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Kt(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ce,De]}],"underline-offset":[{"underline-offset":["auto",Ce,N]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Zs(),Zd]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Xd]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},eh]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:[...Kt(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:Kt()}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["",...Kt()]}],"outline-offset":[{"outline-offset":[Ce,N]}],"outline-w":[{outline:[Ce,De]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:gs()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[Ce,De]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ve,th]}],"shadow-color":[{shadow:[Gt]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":[...q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none"]}],blur:[{blur:[s]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Ve,N]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[h]}],saturate:[{saturate:[T]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[s]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[T]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:Se()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:Se()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[j]}],"scale-x":[{"scale-x":[j]}],"scale-y":[{"scale-y":[j]}],rotate:[{rotate:[Wt,N]}],"translate-x":[{"translate-x":[A]}],"translate-y":[{"translate-y":[A]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Ce,De,rn]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},oh=_d(ih);function ae(...e){return oh(Ma(e))}const ah=Oc,Ka=m.forwardRef(({className:e,...t},s)=>d.jsx($o,{ref:s,className:ae("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Ka.displayName=$o.displayName;const lh=cd("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Wa=m.forwardRef(({className:e,variant:t,...s},n)=>d.jsx(Qo,{ref:n,className:ae(lh({variant:t}),e),...s}));Wa.displayName=Qo.displayName;const ch=m.forwardRef(({className:e,...t},s)=>d.jsx(Yo,{ref:s,className:ae("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));ch.displayName=Yo.displayName;const Ga=m.forwardRef(({className:e,...t},s)=>d.jsx(Xo,{ref:s,className:ae("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:d.jsx(Fa,{className:"h-4 w-4"})}));Ga.displayName=Xo.displayName;const qa=m.forwardRef(({className:e,...t},s)=>d.jsx(Zo,{ref:s,className:ae("text-sm font-semibold",e),...t}));qa.displayName=Zo.displayName;const $a=m.forwardRef(({className:e,...t},s)=>d.jsx(Jo,{ref:s,className:ae("text-sm opacity-90",e),...t}));$a.displayName=Jo.displayName;function uh(){const{toasts:e}=Ra();return d.jsxs(ah,{children:[e.map(function({id:t,title:s,description:n,action:r,...o}){return d.jsxs(Wa,{...o,children:[d.jsxs("div",{className:"grid gap-1",children:[s&&d.jsx(qa,{children:s}),n&&d.jsx($a,{children:n})]}),r,d.jsx(Ga,{})]},t)}),d.jsx(Ka,{})]})}var[zs,Jg]=Lc("Tooltip",[ea]),hr=ea(),Qa="TooltipProvider",dh=700,yi="tooltip.open",[hh,Ya]=zs(Qa),Xa=e=>{const{__scopeTooltip:t,delayDuration:s=dh,skipDelayDuration:n=300,disableHoverableContent:r=!1,children:o}=e,i=m.useRef(!0),a=m.useRef(!1),l=m.useRef(0);return m.useEffect(()=>{const c=l.current;return()=>window.clearTimeout(c)},[]),d.jsx(hh,{scope:t,isOpenDelayedRef:i,delayDuration:s,onOpen:m.useCallback(()=>{window.clearTimeout(l.current),i.current=!1},[]),onClose:m.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.current=!0,n)},[n]),isPointerInTransitRef:a,onPointerInTransitChange:m.useCallback(c=>{a.current=c},[]),disableHoverableContent:r,children:o})};Xa.displayName=Qa;var Za="Tooltip",[e0,Hs]=zs(Za),Fn="TooltipTrigger",fh=m.forwardRef((e,t)=>{const{__scopeTooltip:s,...n}=e,r=Hs(Fn,s),o=Ya(Fn,s),i=hr(s),a=m.useRef(null),l=ta(t,a,r.onTriggerChange),c=m.useRef(!1),u=m.useRef(!1),h=m.useCallback(()=>c.current=!1,[]);return m.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),d.jsx(Ic,{asChild:!0,...i,children:d.jsx(Fc.button,{"aria-describedby":r.open?r.contentId:void 0,"data-state":r.stateAttribute,...n,ref:l,onPointerMove:mt(e.onPointerMove,f=>{f.pointerType!=="touch"&&!u.current&&!o.isPointerInTransitRef.current&&(r.onTriggerEnter(),u.current=!0)}),onPointerLeave:mt(e.onPointerLeave,()=>{r.onTriggerLeave(),u.current=!1}),onPointerDown:mt(e.onPointerDown,()=>{r.open&&r.onClose(),c.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:mt(e.onFocus,()=>{c.current||r.onOpen()}),onBlur:mt(e.onBlur,r.onClose),onClick:mt(e.onClick,r.onClose)})})});fh.displayName=Fn;var ph="TooltipPortal",[t0,mh]=zs(ph,{forceMount:void 0}),It="TooltipContent",Ja=m.forwardRef((e,t)=>{const s=mh(It,e.__scopeTooltip),{forceMount:n=s.forceMount,side:r="top",...o}=e,i=Hs(It,e.__scopeTooltip);return d.jsx(Bc,{present:n||i.open,children:i.disableHoverableContent?d.jsx(el,{side:r,...o,ref:t}):d.jsx(yh,{side:r,...o,ref:t})})}),yh=m.forwardRef((e,t)=>{const s=Hs(It,e.__scopeTooltip),n=Ya(It,e.__scopeTooltip),r=m.useRef(null),o=ta(t,r),[i,a]=m.useState(null),{trigger:l,onClose:c}=s,u=r.current,{onPointerInTransitChange:h}=n,f=m.useCallback(()=>{a(null),h(!1)},[h]),y=m.useCallback((x,g)=>{const v=x.currentTarget,b={x:x.clientX,y:x.clientY},w=wh(b,v.getBoundingClientRect()),T=Th(b,w),j=Ph(g.getBoundingClientRect()),S=Ch([...T,...j]);a(S),h(!0)},[h]);return m.useEffect(()=>()=>f(),[f]),m.useEffect(()=>{if(l&&u){const x=v=>y(v,u),g=v=>y(v,l);return l.addEventListener("pointerleave",x),u.addEventListener("pointerleave",g),()=>{l.removeEventListener("pointerleave",x),u.removeEventListener("pointerleave",g)}}},[l,u,y,f]),m.useEffect(()=>{if(i){const x=g=>{const v=g.target,b={x:g.clientX,y:g.clientY},w=(l==null?void 0:l.contains(v))||(u==null?void 0:u.contains(v)),T=!Sh(b,i);w?f():T&&(f(),c())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[l,u,i,c,f]),d.jsx(el,{...e,ref:o})}),[gh,xh]=zs(Za,{isInside:!1}),vh=Kc("TooltipContent"),el=m.forwardRef((e,t)=>{const{__scopeTooltip:s,children:n,"aria-label":r,onEscapeKeyDown:o,onPointerDownOutside:i,...a}=e,l=Hs(It,s),c=hr(s),{onClose:u}=l;return m.useEffect(()=>(document.addEventListener(yi,u),()=>document.removeEventListener(yi,u)),[u]),m.useEffect(()=>{if(l.trigger){const h=f=>{const y=f.target;y!=null&&y.contains(l.trigger)&&u()};return window.addEventListener("scroll",h,{capture:!0}),()=>window.removeEventListener("scroll",h,{capture:!0})}},[l.trigger,u]),d.jsx(Uc,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:h=>h.preventDefault(),onDismiss:u,children:d.jsxs(_c,{"data-state":l.stateAttribute,...c,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[d.jsx(vh,{children:n}),d.jsx(gh,{scope:s,isInside:!0,children:d.jsx(zc,{id:l.contentId,role:"tooltip",children:r||n})})]})})});Ja.displayName=It;var tl="TooltipArrow",bh=m.forwardRef((e,t)=>{const{__scopeTooltip:s,...n}=e,r=hr(s);return xh(tl,s).isInside?null:d.jsx(Hc,{...r,...n,ref:t})});bh.displayName=tl;function wh(e,t){const s=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),r=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(s,n,r,o)){case o:return"left";case r:return"right";case s:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function Th(e,t,s=5){const n=[];switch(t){case"top":n.push({x:e.x-s,y:e.y+s},{x:e.x+s,y:e.y+s});break;case"bottom":n.push({x:e.x-s,y:e.y-s},{x:e.x+s,y:e.y-s});break;case"left":n.push({x:e.x+s,y:e.y-s},{x:e.x+s,y:e.y+s});break;case"right":n.push({x:e.x-s,y:e.y-s},{x:e.x-s,y:e.y+s});break}return n}function Ph(e){const{top:t,right:s,bottom:n,left:r}=e;return[{x:r,y:t},{x:s,y:t},{x:s,y:n},{x:r,y:n}]}function Sh(e,t){const{x:s,y:n}=e;let r=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const a=t[o].x,l=t[o].y,c=t[i].x,u=t[i].y;l>n!=u>n&&s<(c-a)*(n-l)/(u-l)+a&&(r=!r)}return r}function Ch(e){const t=e.slice();return t.sort((s,n)=>s.x<n.x?-1:s.x>n.x?1:s.y<n.y?-1:s.y>n.y?1:0),jh(t)}function jh(e){if(e.length<=1)return e.slice();const t=[];for(let n=0;n<e.length;n++){const r=e[n];for(;t.length>=2;){const o=t[t.length-1],i=t[t.length-2];if((o.x-i.x)*(r.y-i.y)>=(o.y-i.y)*(r.x-i.x))t.pop();else break}t.push(r)}t.pop();const s=[];for(let n=e.length-1;n>=0;n--){const r=e[n];for(;s.length>=2;){const o=s[s.length-1],i=s[s.length-2];if((o.x-i.x)*(r.y-i.y)>=(o.y-i.y)*(r.x-i.x))s.pop();else break}s.push(r)}return s.pop(),t.length===1&&s.length===1&&t[0].x===s[0].x&&t[0].y===s[0].y?t:t.concat(s)}var Ah=Xa,sl=Ja;const Eh=Ah,Rh=m.forwardRef(({className:e,sideOffset:t=4,...s},n)=>d.jsx(sl,{ref:n,sideOffset:t,className:ae("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",e),...s}));Rh.displayName=sl.displayName;const nl=m.forwardRef(({className:e,...t},s)=>d.jsx("div",{ref:s,className:ae("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));nl.displayName="Card";const kh=m.forwardRef(({className:e,...t},s)=>d.jsx("div",{ref:s,className:ae("flex flex-col space-y-1.5 p-6",e),...t}));kh.displayName="CardHeader";const Mh=m.forwardRef(({className:e,...t},s)=>d.jsx("div",{ref:s,className:ae("text-2xl font-semibold leading-none tracking-tight",e),...t}));Mh.displayName="CardTitle";const Nh=m.forwardRef(({className:e,...t},s)=>d.jsx("div",{ref:s,className:ae("text-sm text-muted-foreground",e),...t}));Nh.displayName="CardDescription";const rl=m.forwardRef(({className:e,...t},s)=>d.jsx("div",{ref:s,className:ae("p-6 pt-0",e),...t}));rl.displayName="CardContent";const Dh=m.forwardRef(({className:e,...t},s)=>d.jsx("div",{ref:s,className:ae("flex items-center p-6 pt-0",e),...t}));Dh.displayName="CardFooter";function Vh(){return d.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:d.jsx(nl,{className:"w-full max-w-md mx-4",children:d.jsxs(rl,{className:"pt-6",children:[d.jsxs("div",{className:"flex mb-4 gap-2",children:[d.jsx(yd,{className:"h-8 w-8 text-red-500"}),d.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),d.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}function Oh(e){if(typeof Proxy>"u")return e;const t=new Map,s=(...n)=>e(...n);return new Proxy(s,{get:(n,r)=>r==="create"?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}function Ks(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Bn=e=>Array.isArray(e);function il(e,t){if(!Array.isArray(t))return!1;const s=t.length;if(s!==e.length)return!1;for(let n=0;n<s;n++)if(t[n]!==e[n])return!1;return!0}function is(e){return typeof e=="string"||Array.isArray(e)}function gi(e){const t=[{},{}];return e==null||e.values.forEach((s,n)=>{t[0][n]=s.get(),t[1][n]=s.getVelocity()}),t}function fr(e,t,s,n){if(typeof t=="function"){const[r,o]=gi(n);t=t(s!==void 0?s:e.custom,r,o)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[r,o]=gi(n);t=t(s!==void 0?s:e.custom,r,o)}return t}function Ws(e,t,s){const n=e.getProps();return fr(n,t,s!==void 0?s:n.custom,e)}const pr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],mr=["initial",...pr],hs=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ft=new Set(hs),Re=e=>e*1e3,ke=e=>e/1e3,Lh={type:"spring",stiffness:500,damping:25,restSpeed:10},Ih=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Fh={type:"keyframes",duration:.8},Bh={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Uh=(e,{keyframes:t})=>t.length>2?Fh:ft.has(e)?e.startsWith("scale")?Ih(t[1]):Lh:Bh;function yr(e,t){return e?e[t]||e.default||e:void 0}const _h={skipAnimations:!1,useManualTiming:!1},zh=e=>e!==null;function Gs(e,{repeat:t,repeatType:s="loop"},n){const r=e.filter(zh),o=t&&s!=="loop"&&t%2===1?0:r.length-1;return!o||n===void 0?r[o]:n}const Q=e=>e;let Hh=Q,Un=Q;function Kh(e){let t=new Set,s=new Set,n=!1,r=!1;const o=new WeakSet;let i={delta:0,timestamp:0,isProcessing:!1};function a(c){o.has(c)&&(l.schedule(c),e()),c(i)}const l={schedule:(c,u=!1,h=!1)=>{const y=h&&n?t:s;return u&&o.add(c),y.has(c)||y.add(c),c},cancel:c=>{s.delete(c),o.delete(c)},process:c=>{if(i=c,n){r=!0;return}n=!0,[t,s]=[s,t],s.clear(),t.forEach(a),n=!1,r&&(r=!1,l.process(c))}};return l}const bs=["read","resolveKeyframes","update","preRender","render","postRender"],Wh=40;function ol(e,t){let s=!1,n=!0;const r={delta:0,timestamp:0,isProcessing:!1},o=()=>s=!0,i=bs.reduce((b,w)=>(b[w]=Kh(o),b),{}),{read:a,resolveKeyframes:l,update:c,preRender:u,render:h,postRender:f}=i,y=()=>{const b=performance.now();s=!1,r.delta=n?1e3/60:Math.max(Math.min(b-r.timestamp,Wh),1),r.timestamp=b,r.isProcessing=!0,a.process(r),l.process(r),c.process(r),u.process(r),h.process(r),f.process(r),r.isProcessing=!1,s&&t&&(n=!1,e(y))},x=()=>{s=!0,n=!0,r.isProcessing||e(y)};return{schedule:bs.reduce((b,w)=>{const T=i[w];return b[w]=(j,S=!1,k=!1)=>(s||x(),T.schedule(j,S,k)),b},{}),cancel:b=>{for(let w=0;w<bs.length;w++)i[bs[w]].cancel(b)},state:r,steps:i}}const{schedule:L,cancel:me,state:$,steps:on}=ol(typeof requestAnimationFrame<"u"?requestAnimationFrame:Q,!0),al=(e,t,s)=>(((1-3*s+3*t)*e+(3*s-6*t))*e+3*t)*e,Gh=1e-7,qh=12;function $h(e,t,s,n,r){let o,i,a=0;do i=t+(s-t)/2,o=al(i,n,r)-e,o>0?s=i:t=i;while(Math.abs(o)>Gh&&++a<qh);return i}function fs(e,t,s,n){if(e===t&&s===n)return Q;const r=o=>$h(o,0,1,e,s);return o=>o===0||o===1?o:al(r(o),t,n)}const ll=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,cl=e=>t=>1-e(1-t),ul=fs(.33,1.53,.69,.99),gr=cl(ul),dl=ll(gr),hl=e=>(e*=2)<1?.5*gr(e):.5*(2-Math.pow(2,-10*(e-1))),xr=e=>1-Math.sin(Math.acos(e)),fl=cl(xr),pl=ll(xr),ml=e=>/^0[^.\s]+$/u.test(e);function Qh(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||ml(e):!0}const yl=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),gl=e=>t=>typeof t=="string"&&t.startsWith(e),xl=gl("--"),Yh=gl("var(--"),vr=e=>Yh(e)?Xh.test(e.split("/*")[0].trim()):!1,Xh=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Zh=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Jh(e){const t=Zh.exec(e);if(!t)return[,];const[,s,n,r]=t;return[`--${s??n}`,r]}function vl(e,t,s=1){const[n,r]=Jh(e);if(!n)return;const o=window.getComputedStyle(t).getPropertyValue(n);if(o){const i=o.trim();return yl(i)?parseFloat(i):i}return vr(r)?vl(r,t,s+1):r}const Ne=(e,t,s)=>s>t?t:s<e?e:s,zt={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},os={...zt,transform:e=>Ne(0,1,e)},ws={...zt,default:1},ps=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Le=ps("deg"),we=ps("%"),R=ps("px"),ef=ps("vh"),tf=ps("vw"),xi={...we,parse:e=>we.parse(e)/100,transform:e=>we.transform(e*100)},sf=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),vi=e=>e===zt||e===R,bi=(e,t)=>parseFloat(e.split(", ")[t]),wi=(e,t)=>(s,{transform:n})=>{if(n==="none"||!n)return 0;const r=n.match(/^matrix3d\((.+)\)$/u);if(r)return bi(r[1],t);{const o=n.match(/^matrix\((.+)\)$/u);return o?bi(o[1],e):0}},nf=new Set(["x","y","z"]),rf=hs.filter(e=>!nf.has(e));function of(e){const t=[];return rf.forEach(s=>{const n=e.getValue(s);n!==void 0&&(t.push([s,n.get()]),n.set(s.startsWith("scale")?1:0))}),t}const Ft={width:({x:e},{paddingLeft:t="0",paddingRight:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),height:({y:e},{paddingTop:t="0",paddingBottom:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:wi(4,13),y:wi(5,14)};Ft.translateX=Ft.x;Ft.translateY=Ft.y;const bl=e=>t=>t.test(e),af={test:e=>e==="auto",parse:e=>e},wl=[zt,R,we,Le,tf,ef,af],Ti=e=>wl.find(bl(e)),ct=new Set;let _n=!1,zn=!1;function Tl(){if(zn){const e=Array.from(ct).filter(n=>n.needsMeasurement),t=new Set(e.map(n=>n.element)),s=new Map;t.forEach(n=>{const r=of(n);r.length&&(s.set(n,r),n.render())}),e.forEach(n=>n.measureInitialState()),t.forEach(n=>{n.render();const r=s.get(n);r&&r.forEach(([o,i])=>{var a;(a=n.getValue(o))===null||a===void 0||a.set(i)})}),e.forEach(n=>n.measureEndState()),e.forEach(n=>{n.suspendedScrollY!==void 0&&window.scrollTo(0,n.suspendedScrollY)})}zn=!1,_n=!1,ct.forEach(e=>e.complete()),ct.clear()}function Pl(){ct.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(zn=!0)})}function lf(){Pl(),Tl()}class br{constructor(t,s,n,r,o,i=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=s,this.name=n,this.motionValue=r,this.element=o,this.isAsync=i}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ct.add(this),_n||(_n=!0,L.read(Pl),L.resolveKeyframes(Tl))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:s,element:n,motionValue:r}=this;for(let o=0;o<t.length;o++)if(t[o]===null)if(o===0){const i=r==null?void 0:r.get(),a=t[t.length-1];if(i!==void 0)t[0]=i;else if(n&&s){const l=n.readValue(s,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),r&&i===void 0&&r.set(t[0])}else t[o]=t[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ct.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ct.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Jt=e=>Math.round(e*1e5)/1e5,wr=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function cf(e){return e==null}const uf=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Tr=(e,t)=>s=>!!(typeof s=="string"&&uf.test(s)&&s.startsWith(e)||t&&!cf(s)&&Object.prototype.hasOwnProperty.call(s,t)),Sl=(e,t,s)=>n=>{if(typeof n!="string")return n;const[r,o,i,a]=n.match(wr);return{[e]:parseFloat(r),[t]:parseFloat(o),[s]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},df=e=>Ne(0,255,e),an={...zt,transform:e=>Math.round(df(e))},tt={test:Tr("rgb","red"),parse:Sl("red","green","blue"),transform:({red:e,green:t,blue:s,alpha:n=1})=>"rgba("+an.transform(e)+", "+an.transform(t)+", "+an.transform(s)+", "+Jt(os.transform(n))+")"};function hf(e){let t="",s="",n="",r="";return e.length>5?(t=e.substring(1,3),s=e.substring(3,5),n=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),s=e.substring(2,3),n=e.substring(3,4),r=e.substring(4,5),t+=t,s+=s,n+=n,r+=r),{red:parseInt(t,16),green:parseInt(s,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}}const Hn={test:Tr("#"),parse:hf,transform:tt.transform},yt={test:Tr("hsl","hue"),parse:Sl("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:s,alpha:n=1})=>"hsla("+Math.round(e)+", "+we.transform(Jt(t))+", "+we.transform(Jt(s))+", "+Jt(os.transform(n))+")"},Z={test:e=>tt.test(e)||Hn.test(e)||yt.test(e),parse:e=>tt.test(e)?tt.parse(e):yt.test(e)?yt.parse(e):Hn.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?tt.transform(e):yt.transform(e)},ff=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function pf(e){var t,s;return isNaN(e)&&typeof e=="string"&&(((t=e.match(wr))===null||t===void 0?void 0:t.length)||0)+(((s=e.match(ff))===null||s===void 0?void 0:s.length)||0)>0}const Cl="number",jl="color",mf="var",yf="var(",Pi="${}",gf=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function as(e){const t=e.toString(),s=[],n={color:[],number:[],var:[]},r=[];let o=0;const a=t.replace(gf,l=>(Z.test(l)?(n.color.push(o),r.push(jl),s.push(Z.parse(l))):l.startsWith(yf)?(n.var.push(o),r.push(mf),s.push(l)):(n.number.push(o),r.push(Cl),s.push(parseFloat(l))),++o,Pi)).split(Pi);return{values:s,split:a,indexes:n,types:r}}function Al(e){return as(e).values}function El(e){const{split:t,types:s}=as(e),n=t.length;return r=>{let o="";for(let i=0;i<n;i++)if(o+=t[i],r[i]!==void 0){const a=s[i];a===Cl?o+=Jt(r[i]):a===jl?o+=Z.transform(r[i]):o+=r[i]}return o}}const xf=e=>typeof e=="number"?0:e;function vf(e){const t=Al(e);return El(e)(t.map(xf))}const Qe={test:pf,parse:Al,createTransformer:El,getAnimatableNone:vf},bf=new Set(["brightness","contrast","saturate","opacity"]);function wf(e){const[t,s]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[n]=s.match(wr)||[];if(!n)return e;const r=s.replace(n,"");let o=bf.has(t)?1:0;return n!==s&&(o*=100),t+"("+o+r+")"}const Tf=/\b([a-z-]*)\(.*?\)/gu,Kn={...Qe,getAnimatableNone:e=>{const t=e.match(Tf);return t?t.map(wf).join(" "):e}},Pf={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R},Sf={rotate:Le,rotateX:Le,rotateY:Le,rotateZ:Le,scale:ws,scaleX:ws,scaleY:ws,scaleZ:ws,skew:Le,skewX:Le,skewY:Le,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:os,originX:xi,originY:xi,originZ:R},Si={...zt,transform:Math.round},Pr={...Pf,...Sf,zIndex:Si,size:R,fillOpacity:os,strokeOpacity:os,numOctaves:Si},Cf={...Pr,color:Z,backgroundColor:Z,outlineColor:Z,fill:Z,stroke:Z,borderColor:Z,borderTopColor:Z,borderRightColor:Z,borderBottomColor:Z,borderLeftColor:Z,filter:Kn,WebkitFilter:Kn},Sr=e=>Cf[e];function Rl(e,t){let s=Sr(e);return s!==Kn&&(s=Qe),s.getAnimatableNone?s.getAnimatableNone(t):void 0}const jf=new Set(["auto","none","0"]);function Af(e,t,s){let n=0,r;for(;n<e.length&&!r;){const o=e[n];typeof o=="string"&&!jf.has(o)&&as(o).values.length&&(r=e[n]),n++}if(r&&s)for(const o of t)e[o]=Rl(s,r)}class kl extends br{constructor(t,s,n,r,o){super(t,s,n,r,o,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:s,name:n}=this;if(!s||!s.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let c=t[l];if(typeof c=="string"&&(c=c.trim(),vr(c))){const u=vl(c,s.current);u!==void 0&&(t[l]=u),l===t.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!sf.has(n)||t.length!==2)return;const[r,o]=t,i=Ti(r),a=Ti(o);if(i!==a)if(vi(i)&&vi(a))for(let l=0;l<t.length;l++){const c=t[l];typeof c=="string"&&(t[l]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:s}=this,n=[];for(let r=0;r<t.length;r++)Qh(t[r])&&n.push(r);n.length&&Af(t,n,s)}measureInitialState(){const{element:t,unresolvedKeyframes:s,name:n}=this;if(!t||!t.current)return;n==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ft[n](t.measureViewportBox(),window.getComputedStyle(t.current)),s[0]=this.measuredOrigin;const r=s[s.length-1];r!==void 0&&t.getValue(n,r).jump(r,!1)}measureEndState(){var t;const{element:s,name:n,unresolvedKeyframes:r}=this;if(!s||!s.current)return;const o=s.getValue(n);o&&o.jump(this.measuredOrigin,!1);const i=r.length-1,a=r[i];r[i]=Ft[n](s.measureViewportBox(),window.getComputedStyle(s.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,c])=>{s.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function Cr(e){return typeof e=="function"}let As;function Ef(){As=void 0}const Te={now:()=>(As===void 0&&Te.set($.isProcessing||_h.useManualTiming?$.timestamp:performance.now()),As),set:e=>{As=e,queueMicrotask(Ef)}},Ci=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Qe.test(e)||e==="0")&&!e.startsWith("url("));function Rf(e){const t=e[0];if(e.length===1)return!0;for(let s=0;s<e.length;s++)if(e[s]!==t)return!0}function kf(e,t,s,n){const r=e[0];if(r===null)return!1;if(t==="display"||t==="visibility")return!0;const o=e[e.length-1],i=Ci(r,t),a=Ci(o,t);return!i||!a?!1:Rf(e)||(s==="spring"||Cr(s))&&n}const Mf=40;class Ml{constructor({autoplay:t=!0,delay:s=0,type:n="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:i="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Te.now(),this.options={autoplay:t,delay:s,type:n,repeat:r,repeatDelay:o,repeatType:i,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Mf?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&lf(),this._resolved}onKeyframesResolved(t,s){this.resolvedAt=Te.now(),this.hasAttemptedResolve=!0;const{name:n,type:r,velocity:o,delay:i,onComplete:a,onUpdate:l,isGenerator:c}=this.options;if(!c&&!kf(t,n,r,o))if(i)this.options.duration=0;else{l==null||l(Gs(t,this.options,s)),a==null||a(),this.resolveFinishedPromise();return}const u=this.initPlayback(t,s);u!==!1&&(this._resolved={keyframes:t,finalKeyframe:s,...u},this.onPostResolved())}onPostResolved(){}then(t,s){return this.currentFinishedPromise.then(t,s)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const ht=(e,t,s)=>{const n=t-e;return n===0?1:(s-e)/n},Nl=(e,t,s=10)=>{let n="";const r=Math.max(Math.round(t/s),2);for(let o=0;o<r;o++)n+=e(ht(0,r-1,o))+", ";return`linear(${n.substring(0,n.length-2)})`};function jr(e,t){return t?e*(1e3/t):0}const Nf=5;function Dl(e,t,s){const n=Math.max(t-Nf,0);return jr(s-e(n),t-n)}const K={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ln=.001;function Df({duration:e=K.duration,bounce:t=K.bounce,velocity:s=K.velocity,mass:n=K.mass}){let r,o,i=1-t;i=Ne(K.minDamping,K.maxDamping,i),e=Ne(K.minDuration,K.maxDuration,ke(e)),i<1?(r=c=>{const u=c*i,h=u*e,f=u-s,y=Wn(c,i),x=Math.exp(-h);return ln-f/y*x},o=c=>{const h=c*i*e,f=h*s+s,y=Math.pow(i,2)*Math.pow(c,2)*e,x=Math.exp(-h),g=Wn(Math.pow(c,2),i);return(-r(c)+ln>0?-1:1)*((f-y)*x)/g}):(r=c=>{const u=Math.exp(-c*e),h=(c-s)*e+1;return-ln+u*h},o=c=>{const u=Math.exp(-c*e),h=(s-c)*(e*e);return u*h});const a=5/e,l=Of(r,o,a);if(e=Re(e),isNaN(l))return{stiffness:K.stiffness,damping:K.damping,duration:e};{const c=Math.pow(l,2)*n;return{stiffness:c,damping:i*2*Math.sqrt(n*c),duration:e}}}const Vf=12;function Of(e,t,s){let n=s;for(let r=1;r<Vf;r++)n=n-e(n)/t(n);return n}function Wn(e,t){return e*Math.sqrt(1-t*t)}const Gn=2e4;function Vl(e){let t=0;const s=50;let n=e.next(t);for(;!n.done&&t<Gn;)t+=s,n=e.next(t);return t>=Gn?1/0:t}const Lf=["duration","bounce"],If=["stiffness","damping","mass"];function ji(e,t){return t.some(s=>e[s]!==void 0)}function Ff(e){let t={velocity:K.velocity,stiffness:K.stiffness,damping:K.damping,mass:K.mass,isResolvedFromDuration:!1,...e};if(!ji(e,If)&&ji(e,Lf))if(e.visualDuration){const s=e.visualDuration,n=2*Math.PI/(s*1.2),r=n*n,o=2*Ne(.05,1,1-e.bounce)*Math.sqrt(r);t={...t,mass:K.mass,stiffness:r,damping:o}}else{const s=Df(e);t={...t,...s,mass:K.mass},t.isResolvedFromDuration=!0}return t}function Ol(e=K.visualDuration,t=K.bounce){const s=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:n,restDelta:r}=s;const o=s.keyframes[0],i=s.keyframes[s.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:h,velocity:f,isResolvedFromDuration:y}=Ff({...s,velocity:-ke(s.velocity||0)}),x=f||0,g=c/(2*Math.sqrt(l*u)),v=i-o,b=ke(Math.sqrt(l/u)),w=Math.abs(v)<5;n||(n=w?K.restSpeed.granular:K.restSpeed.default),r||(r=w?K.restDelta.granular:K.restDelta.default);let T;if(g<1){const S=Wn(b,g);T=k=>{const V=Math.exp(-g*b*k);return i-V*((x+g*b*v)/S*Math.sin(S*k)+v*Math.cos(S*k))}}else if(g===1)T=S=>i-Math.exp(-b*S)*(v+(x+b*v)*S);else{const S=b*Math.sqrt(g*g-1);T=k=>{const V=Math.exp(-g*b*k),A=Math.min(S*k,300);return i-V*((x+g*b*v)*Math.sinh(A)+S*v*Math.cosh(A))/S}}const j={calculatedDuration:y&&h||null,next:S=>{const k=T(S);if(y)a.done=S>=h;else{let V=0;g<1&&(V=S===0?Re(x):Dl(T,S,k));const A=Math.abs(V)<=n,M=Math.abs(i-k)<=r;a.done=A&&M}return a.value=a.done?i:k,a},toString:()=>{const S=Math.min(Vl(j),Gn),k=Nl(V=>j.next(S*V).value,S,30);return S+"ms "+k}};return j}function Ai({keyframes:e,velocity:t=0,power:s=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:o=500,modifyTarget:i,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=e[0],f={done:!1,value:h},y=A=>a!==void 0&&A<a||l!==void 0&&A>l,x=A=>a===void 0?l:l===void 0||Math.abs(a-A)<Math.abs(l-A)?a:l;let g=s*t;const v=h+g,b=i===void 0?v:i(v);b!==v&&(g=b-h);const w=A=>-g*Math.exp(-A/n),T=A=>b+w(A),j=A=>{const M=w(A),U=T(A);f.done=Math.abs(M)<=c,f.value=f.done?b:U};let S,k;const V=A=>{y(f.value)&&(S=A,k=Ol({keyframes:[f.value,x(f.value)],velocity:Dl(T,A,f.value),damping:r,stiffness:o,restDelta:c,restSpeed:u}))};return V(0),{calculatedDuration:null,next:A=>{let M=!1;return!k&&S===void 0&&(M=!0,j(A),V(A)),S!==void 0&&A>=S?k.next(A-S):(!M&&j(A),f)}}}const Bf=fs(.42,0,1,1),Uf=fs(0,0,.58,1),Ll=fs(.42,0,.58,1),_f=e=>Array.isArray(e)&&typeof e[0]!="number",Ar=e=>Array.isArray(e)&&typeof e[0]=="number",Ei={linear:Q,easeIn:Bf,easeInOut:Ll,easeOut:Uf,circIn:xr,circInOut:pl,circOut:fl,backIn:gr,backInOut:dl,backOut:ul,anticipate:hl},Ri=e=>{if(Ar(e)){Un(e.length===4);const[t,s,n,r]=e;return fs(t,s,n,r)}else if(typeof e=="string")return Un(Ei[e]!==void 0),Ei[e];return e},zf=(e,t)=>s=>t(e(s)),qe=(...e)=>e.reduce(zf),_=(e,t,s)=>e+(t-e)*s;function cn(e,t,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?e+(t-e)*6*s:s<1/2?t:s<2/3?e+(t-e)*(2/3-s)*6:e}function Hf({hue:e,saturation:t,lightness:s,alpha:n}){e/=360,t/=100,s/=100;let r=0,o=0,i=0;if(!t)r=o=i=s;else{const a=s<.5?s*(1+t):s+t-s*t,l=2*s-a;r=cn(l,a,e+1/3),o=cn(l,a,e),i=cn(l,a,e-1/3)}return{red:Math.round(r*255),green:Math.round(o*255),blue:Math.round(i*255),alpha:n}}function Vs(e,t){return s=>s>0?t:e}const un=(e,t,s)=>{const n=e*e,r=s*(t*t-n)+n;return r<0?0:Math.sqrt(r)},Kf=[Hn,tt,yt],Wf=e=>Kf.find(t=>t.test(e));function ki(e){const t=Wf(e);if(!t)return!1;let s=t.parse(e);return t===yt&&(s=Hf(s)),s}const Mi=(e,t)=>{const s=ki(e),n=ki(t);if(!s||!n)return Vs(e,t);const r={...s};return o=>(r.red=un(s.red,n.red,o),r.green=un(s.green,n.green,o),r.blue=un(s.blue,n.blue,o),r.alpha=_(s.alpha,n.alpha,o),tt.transform(r))},qn=new Set(["none","hidden"]);function Gf(e,t){return qn.has(e)?s=>s<=0?e:t:s=>s>=1?t:e}function qf(e,t){return s=>_(e,t,s)}function Er(e){return typeof e=="number"?qf:typeof e=="string"?vr(e)?Vs:Z.test(e)?Mi:Yf:Array.isArray(e)?Il:typeof e=="object"?Z.test(e)?Mi:$f:Vs}function Il(e,t){const s=[...e],n=s.length,r=e.map((o,i)=>Er(o)(o,t[i]));return o=>{for(let i=0;i<n;i++)s[i]=r[i](o);return s}}function $f(e,t){const s={...e,...t},n={};for(const r in s)e[r]!==void 0&&t[r]!==void 0&&(n[r]=Er(e[r])(e[r],t[r]));return r=>{for(const o in n)s[o]=n[o](r);return s}}function Qf(e,t){var s;const n=[],r={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){const i=t.types[o],a=e.indexes[i][r[i]],l=(s=e.values[a])!==null&&s!==void 0?s:0;n[o]=l,r[i]++}return n}const Yf=(e,t)=>{const s=Qe.createTransformer(t),n=as(e),r=as(t);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?qn.has(e)&&!r.values.length||qn.has(t)&&!n.values.length?Gf(e,t):qe(Il(Qf(n,r),r.values),s):Vs(e,t)};function Fl(e,t,s){return typeof e=="number"&&typeof t=="number"&&typeof s=="number"?_(e,t,s):Er(e)(e,t)}function Xf(e,t,s){const n=[],r=s||Fl,o=e.length-1;for(let i=0;i<o;i++){let a=r(e[i],e[i+1]);if(t){const l=Array.isArray(t)?t[i]||Q:t;a=qe(l,a)}n.push(a)}return n}function Rr(e,t,{clamp:s=!0,ease:n,mixer:r}={}){const o=e.length;if(Un(o===t.length),o===1)return()=>t[0];if(o===2&&e[0]===e[1])return()=>t[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const i=Xf(t,n,r),a=i.length,l=c=>{let u=0;if(a>1)for(;u<e.length-2&&!(c<e[u+1]);u++);const h=ht(e[u],e[u+1],c);return i[u](h)};return s?c=>l(Ne(e[0],e[o-1],c)):l}function Zf(e,t){const s=e[e.length-1];for(let n=1;n<=t;n++){const r=ht(0,t,n);e.push(_(s,1,r))}}function Bl(e){const t=[0];return Zf(t,e.length-1),t}function Jf(e,t){return e.map(s=>s*t)}function ep(e,t){return e.map(()=>t||Ll).splice(0,e.length-1)}function Os({duration:e=300,keyframes:t,times:s,ease:n="easeInOut"}){const r=_f(n)?n.map(Ri):Ri(n),o={done:!1,value:t[0]},i=Jf(s&&s.length===t.length?s:Bl(t),e),a=Rr(i,t,{ease:Array.isArray(r)?r:ep(t,r)});return{calculatedDuration:e,next:l=>(o.value=a(l),o.done=l>=e,o)}}const tp=e=>{const t=({timestamp:s})=>e(s);return{start:()=>L.update(t,!0),stop:()=>me(t),now:()=>$.isProcessing?$.timestamp:Te.now()}},sp={decay:Ai,inertia:Ai,tween:Os,keyframes:Os,spring:Ol},np=e=>e/100;class kr extends Ml{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:s,motionValue:n,element:r,keyframes:o}=this.options,i=(r==null?void 0:r.KeyframeResolver)||br,a=(l,c)=>this.onKeyframesResolved(l,c);this.resolver=new i(o,a,s,n,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:i=0}=this.options,a=Cr(s)?s:sp[s]||Os;let l,c;a!==Os&&typeof t[0]!="number"&&(l=qe(np,Fl(t[0],t[1])),t=[0,100]);const u=a({...this.options,keyframes:t});o==="mirror"&&(c=a({...this.options,keyframes:[...t].reverse(),velocity:-i})),u.calculatedDuration===null&&(u.calculatedDuration=Vl(u));const{calculatedDuration:h}=u,f=h+r,y=f*(n+1)-r;return{generator:u,mirroredGenerator:c,mapPercentToKeyframes:l,calculatedDuration:h,resolvedDuration:f,totalDuration:y}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,s=!1){const{resolved:n}=this;if(!n){const{keyframes:A}=this.options;return{done:!0,value:A[A.length-1]}}const{finalKeyframe:r,generator:o,mirroredGenerator:i,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:c,totalDuration:u,resolvedDuration:h}=n;if(this.startTime===null)return o.next(0);const{delay:f,repeat:y,repeatType:x,repeatDelay:g,onUpdate:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),s?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const b=this.currentTime-f*(this.speed>=0?1:-1),w=this.speed>=0?b<0:b>u;this.currentTime=Math.max(b,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let T=this.currentTime,j=o;if(y){const A=Math.min(this.currentTime,u)/h;let M=Math.floor(A),U=A%1;!U&&A>=1&&(U=1),U===1&&M--,M=Math.min(M,y+1),!!(M%2)&&(x==="reverse"?(U=1-U,g&&(U-=g/h)):x==="mirror"&&(j=i)),T=Ne(0,1,U)*h}const S=w?{done:!1,value:l[0]}:j.next(T);a&&(S.value=a(S.value));let{done:k}=S;!w&&c!==null&&(k=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const V=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&k);return V&&r!==void 0&&(S.value=Gs(l,this.options,r)),v&&v(S.value),V&&this.finish(),S}get duration(){const{resolved:t}=this;return t?ke(t.calculatedDuration):0}get time(){return ke(this.currentTime)}set time(t){t=Re(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const s=this.playbackSpeed!==t;this.playbackSpeed=t,s&&(this.time=ke(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=tp,onPlay:s,startTime:n}=this.options;this.driver||(this.driver=t(o=>this.tick(o))),s&&s();const r=this.driver.now();this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=r):this.startTime=n??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const rp=new Set(["opacity","clipPath","filter","transform"]);function Mr(e){let t;return()=>(t===void 0&&(t=e()),t)}const ip={linearEasing:void 0};function op(e,t){const s=Mr(e);return()=>{var n;return(n=ip[t])!==null&&n!==void 0?n:s()}}const Ls=op(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing");function Ul(e){return!!(typeof e=="function"&&Ls()||!e||typeof e=="string"&&(e in $n||Ls())||Ar(e)||Array.isArray(e)&&e.every(Ul))}const Yt=([e,t,s,n])=>`cubic-bezier(${e}, ${t}, ${s}, ${n})`,$n={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Yt([0,.65,.55,1]),circOut:Yt([.55,0,1,.45]),backIn:Yt([.31,.01,.66,-.59]),backOut:Yt([.33,1.53,.69,.99])};function _l(e,t){if(e)return typeof e=="function"&&Ls()?Nl(e,t):Ar(e)?Yt(e):Array.isArray(e)?e.map(s=>_l(s,t)||$n.easeOut):$n[e]}function ap(e,t,s,{delay:n=0,duration:r=300,repeat:o=0,repeatType:i="loop",ease:a="easeInOut",times:l}={}){const c={[t]:s};l&&(c.offset=l);const u=_l(a,r);return Array.isArray(u)&&(c.easing=u),e.animate(c,{delay:n,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:o+1,direction:i==="reverse"?"alternate":"normal"})}function Ni(e,t){e.timeline=t,e.onfinish=null}const lp=Mr(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Is=10,cp=2e4;function up(e){return Cr(e.type)||e.type==="spring"||!Ul(e.ease)}function dp(e,t){const s=new kr({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let n={done:!1,value:e[0]};const r=[];let o=0;for(;!n.done&&o<cp;)n=s.sample(o),r.push(n.value),o+=Is;return{times:void 0,keyframes:r,duration:o-Is,ease:"linear"}}const zl={anticipate:hl,backInOut:dl,circInOut:pl};function hp(e){return e in zl}class Di extends Ml{constructor(t){super(t);const{name:s,motionValue:n,element:r,keyframes:o}=this.options;this.resolver=new kl(o,(i,a)=>this.onKeyframesResolved(i,a),s,n,r),this.resolver.scheduleResolve()}initPlayback(t,s){var n;let{duration:r=300,times:o,ease:i,type:a,motionValue:l,name:c,startTime:u}=this.options;if(!(!((n=l.owner)===null||n===void 0)&&n.current))return!1;if(typeof i=="string"&&Ls()&&hp(i)&&(i=zl[i]),up(this.options)){const{onComplete:f,onUpdate:y,motionValue:x,element:g,...v}=this.options,b=dp(t,v);t=b.keyframes,t.length===1&&(t[1]=t[0]),r=b.duration,o=b.times,i=b.ease,a="keyframes"}const h=ap(l.owner.current,c,t,{...this.options,duration:r,times:o,ease:i});return h.startTime=u??this.calcStartTime(),this.pendingTimeline?(Ni(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{const{onComplete:f}=this.options;l.set(Gs(t,this.options,s)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:r,times:o,type:a,ease:i,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:s}=t;return ke(s)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:s}=t;return ke(s.currentTime||0)}set time(t){const{resolved:s}=this;if(!s)return;const{animation:n}=s;n.currentTime=Re(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:s}=t;return s.playbackRate}set speed(t){const{resolved:s}=this;if(!s)return;const{animation:n}=s;n.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:s}=t;return s.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:s}=t;return s.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:s}=this;if(!s)return Q;const{animation:n}=s;Ni(n,t)}return Q}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:s}=t;s.playState==="finished"&&this.updateFinishedPromise(),s.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:s}=t;s.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:s,keyframes:n,duration:r,type:o,ease:i,times:a}=t;if(s.playState==="idle"||s.playState==="finished")return;if(this.time){const{motionValue:c,onUpdate:u,onComplete:h,element:f,...y}=this.options,x=new kr({...y,keyframes:n,duration:r,type:o,ease:i,times:a,isGenerator:!0}),g=Re(this.time);c.setWithVelocity(x.sample(g-Is).value,x.sample(g).value,Is)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:s,name:n,repeatDelay:r,repeatType:o,damping:i,type:a}=t;return lp()&&n&&rp.has(n)&&s&&s.owner&&s.owner.current instanceof HTMLElement&&!s.owner.getProps().onUpdate&&!r&&o!=="mirror"&&i!==0&&a!=="inertia"}}const Hl=Mr(()=>window.ScrollTimeline!==void 0);class fp{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}then(t,s){return Promise.all(this.animations).then(t).catch(s)}getAll(t){return this.animations[0][t]}setAll(t,s){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=s}attachTimeline(t,s){const n=this.animations.map(r=>Hl()&&r.attachTimeline?r.attachTimeline(t):s(r));return()=>{n.forEach((r,o)=>{r&&r(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let s=0;s<this.animations.length;s++)t=Math.max(t,this.animations[s].duration);return t}runAll(t){this.animations.forEach(s=>s[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function pp({when:e,delay:t,delayChildren:s,staggerChildren:n,staggerDirection:r,repeat:o,repeatType:i,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const Nr=(e,t,s,n={},r,o)=>i=>{const a=yr(n,e)||{},l=a.delay||n.delay||0;let{elapsed:c=0}=n;c=c-Re(l);let u={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-c,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{i(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:r};pp(a)||(u={...u,...Uh(e,u)}),u.duration&&(u.duration=Re(u.duration)),u.repeatDelay&&(u.repeatDelay=Re(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),h&&!o&&t.get()!==void 0){const f=Gs(u.keyframes,a);if(f!==void 0)return L.update(()=>{u.onUpdate(f),u.onComplete()}),new fp([])}return!o&&Di.supports(u)?new Di(u):new kr(u)},mp=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),yp=e=>Bn(e)?e[e.length-1]||0:e;function Dr(e,t){e.indexOf(t)===-1&&e.push(t)}function Vr(e,t){const s=e.indexOf(t);s>-1&&e.splice(s,1)}class Or{constructor(){this.subscriptions=[]}add(t){return Dr(this.subscriptions,t),()=>Vr(this.subscriptions,t)}notify(t,s,n){const r=this.subscriptions.length;if(r)if(r===1)this.subscriptions[0](t,s,n);else for(let o=0;o<r;o++){const i=this.subscriptions[o];i&&i(t,s,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Vi=30,gp=e=>!isNaN(parseFloat(e)),es={current:void 0};class xp{constructor(t,s={}){this.version="11.13.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(n,r=!0)=>{const o=Te.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(n),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),r&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=s.owner}setCurrent(t){this.current=t,this.updatedAt=Te.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=gp(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,s){this.events[t]||(this.events[t]=new Or);const n=this.events[t].add(s);return t==="change"?()=>{n(),L.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,s){this.passiveEffect=t,this.stopPassiveEffect=s}set(t,s=!0){!s||!this.passiveEffect?this.updateAndNotify(t,s):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,s,n){this.set(s),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,s=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return es.current&&es.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=Te.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Vi)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Vi);return jr(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(t){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=t(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function be(e,t){return new xp(e,t)}function vp(e,t,s){e.hasValue(t)?e.getValue(t).set(s):e.addValue(t,be(s))}function bp(e,t){const s=Ws(e,t);let{transitionEnd:n={},transition:r={},...o}=s||{};o={...o,...n};for(const i in o){const a=yp(o[i]);vp(e,i,a)}}const Lr=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),wp="framerAppearId",Kl="data-"+Lr(wp);function Wl(e){return e.props[Kl]}const J=e=>!!(e&&e.getVelocity);function Tp(e){return!!(J(e)&&e.add)}function Qn(e,t){const s=e.getValue("willChange");if(Tp(s))return s.add(t)}function Pp({protectedKeys:e,needsAnimating:t},s){const n=e.hasOwnProperty(s)&&t[s]!==!0;return t[s]=!1,n}function Gl(e,t,{delay:s=0,transitionOverride:n,type:r}={}){var o;let{transition:i=e.getDefaultTransition(),transitionEnd:a,...l}=t;n&&(i=n);const c=[],u=r&&e.animationState&&e.animationState.getState()[r];for(const h in l){const f=e.getValue(h,(o=e.latestValues[h])!==null&&o!==void 0?o:null),y=l[h];if(y===void 0||u&&Pp(u,h))continue;const x={delay:s,...yr(i||{},h)};let g=!1;if(window.MotionHandoffAnimation){const b=Wl(e);if(b){const w=window.MotionHandoffAnimation(b,h,L);w!==null&&(x.startTime=w,g=!0)}}Qn(e,h),f.start(Nr(h,f,y,e.shouldReduceMotion&&ft.has(h)?{type:!1}:x,e,g));const v=f.animation;v&&c.push(v)}return a&&Promise.all(c).then(()=>{L.update(()=>{a&&bp(e,a)})}),c}function Yn(e,t,s={}){var n;const r=Ws(e,t,s.type==="exit"?(n=e.presenceContext)===null||n===void 0?void 0:n.custom:void 0);let{transition:o=e.getDefaultTransition()||{}}=r||{};s.transitionOverride&&(o=s.transitionOverride);const i=r?()=>Promise.all(Gl(e,r,s)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:f}=o;return Sp(e,t,u+c,h,f,s)}:()=>Promise.resolve(),{when:l}=o;if(l){const[c,u]=l==="beforeChildren"?[i,a]:[a,i];return c().then(()=>u())}else return Promise.all([i(),a(s.delay)])}function Sp(e,t,s=0,n=0,r=1,o){const i=[],a=(e.variantChildren.size-1)*n,l=r===1?(c=0)=>c*n:(c=0)=>a-c*n;return Array.from(e.variantChildren).sort(Cp).forEach((c,u)=>{c.notify("AnimationStart",t),i.push(Yn(c,t,{...o,delay:s+l(u)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(i)}function Cp(e,t){return e.sortNodePosition(t)}function jp(e,t,s={}){e.notify("AnimationStart",t);let n;if(Array.isArray(t)){const r=t.map(o=>Yn(e,o,s));n=Promise.all(r)}else if(typeof t=="string")n=Yn(e,t,s);else{const r=typeof t=="function"?Ws(e,t,s.custom):t;n=Promise.all(Gl(e,r,s))}return n.then(()=>{e.notify("AnimationComplete",t)})}const Ap=mr.length;function ql(e){if(!e)return;if(!e.isControllingVariants){const s=e.parent?ql(e.parent)||{}:{};return e.props.initial!==void 0&&(s.initial=e.props.initial),s}const t={};for(let s=0;s<Ap;s++){const n=mr[s],r=e.props[n];(is(r)||r===!1)&&(t[n]=r)}return t}const Ep=[...pr].reverse(),Rp=pr.length;function kp(e){return t=>Promise.all(t.map(({animation:s,options:n})=>jp(e,s,n)))}function Mp(e){let t=kp(e),s=Oi(),n=!0;const r=l=>(c,u)=>{var h;const f=Ws(e,u,l==="exit"?(h=e.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:y,transitionEnd:x,...g}=f;c={...c,...g,...x}}return c};function o(l){t=l(e)}function i(l){const{props:c}=e,u=ql(e.parent)||{},h=[],f=new Set;let y={},x=1/0;for(let v=0;v<Rp;v++){const b=Ep[v],w=s[b],T=c[b]!==void 0?c[b]:u[b],j=is(T),S=b===l?w.isActive:null;S===!1&&(x=v);let k=T===u[b]&&T!==c[b]&&j;if(k&&n&&e.manuallyAnimateOnMount&&(k=!1),w.protectedKeys={...y},!w.isActive&&S===null||!T&&!w.prevProp||Ks(T)||typeof T=="boolean")continue;const V=Np(w.prevProp,T);let A=V||b===l&&w.isActive&&!k&&j||v>x&&j,M=!1;const U=Array.isArray(T)?T:[T];let Y=U.reduce(r(b),{});S===!1&&(Y={});const{prevResolvedValues:I={}}=w,gs={...I,...Y},Xe=q=>{A=!0,f.has(q)&&(M=!0,f.delete(q)),w.needsAnimating[q]=!0;const re=e.getValue(q);re&&(re.liveStyle=!1)};for(const q in gs){const re=Y[q],Pe=I[q];if(y.hasOwnProperty(q))continue;let pt=!1;Bn(re)&&Bn(Pe)?pt=!il(re,Pe):pt=re!==Pe,pt?re!=null?Xe(q):f.add(q):re!==void 0&&f.has(q)?Xe(q):w.protectedKeys[q]=!0}w.prevProp=T,w.prevResolvedValues=Y,w.isActive&&(y={...y,...Y}),n&&e.blockInitialAnimation&&(A=!1),A&&(!(k&&V)||M)&&h.push(...U.map(q=>({animation:q,options:{type:b}})))}if(f.size){const v={};f.forEach(b=>{const w=e.getBaseTarget(b),T=e.getValue(b);T&&(T.liveStyle=!0),v[b]=w??null}),h.push({animation:v})}let g=!!h.length;return n&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(g=!1),n=!1,g?t(h):Promise.resolve()}function a(l,c){var u;if(s[l].isActive===c)return Promise.resolve();(u=e.variantChildren)===null||u===void 0||u.forEach(f=>{var y;return(y=f.animationState)===null||y===void 0?void 0:y.setActive(l,c)}),s[l].isActive=c;const h=i(l);for(const f in s)s[f].protectedKeys={};return h}return{animateChanges:i,setActive:a,setAnimateFunction:o,getState:()=>s,reset:()=>{s=Oi(),n=!0}}}function Np(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!il(t,e):!1}function Ze(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Oi(){return{animate:Ze(!0),whileInView:Ze(),whileHover:Ze(),whileTap:Ze(),whileDrag:Ze(),whileFocus:Ze(),exit:Ze()}}class Ye{constructor(t){this.isMounted=!1,this.node=t}update(){}}class Dp extends Ye{constructor(t){super(t),t.animationState||(t.animationState=Mp(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Ks(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:s}=this.node.prevProps||{};t!==s&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let Vp=0;class Op extends Ye{constructor(){super(...arguments),this.id=Vp++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:s}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const r=this.node.animationState.setActive("exit",!t);s&&!t&&r.then(()=>s(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Lp={animation:{Feature:Dp},exit:{Feature:Op}};function Ir(e,t,s){var n;if(e instanceof Element)return[e];if(typeof e=="string"){let r=document;const o=(n=void 0)!==null&&n!==void 0?n:r.querySelectorAll(e);return o?Array.from(o):[]}return Array.from(e)}const he={x:!1,y:!1};function $l(){return he.x||he.y}function Li(e){return t=>{t.pointerType==="touch"||$l()||e(t)}}function Ip(e,t,s={}){const n=new AbortController,r={passive:!0,...s,signal:n.signal},o=Li(i=>{const{target:a}=i,l=t(i);if(!l||!a)return;const c=Li(u=>{l(u),a.removeEventListener("pointerleave",c)});a.addEventListener("pointerleave",c,r)});return Ir(e).forEach(i=>{i.addEventListener("pointerenter",o,r)}),()=>n.abort()}function Fp(e){return e==="x"||e==="y"?he[e]?null:(he[e]=!0,()=>{he[e]=!1}):he.x||he.y?null:(he.x=he.y=!0,()=>{he.x=he.y=!1})}const Ql=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function ms(e){return{point:{x:e.pageX,y:e.pageY}}}const Bp=e=>t=>Ql(t)&&e(t,ms(t));function Ee(e,t,s,n={passive:!0}){return e.addEventListener(t,s,n),()=>e.removeEventListener(t,s)}function $e(e,t,s,n){return Ee(e,t,Bp(s),n)}const Ii=(e,t)=>Math.abs(e-t);function Up(e,t){const s=Ii(e.x,t.x),n=Ii(e.y,t.y);return Math.sqrt(s**2+n**2)}class Yl{constructor(t,s,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=hn(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,y=Up(h.offset,{x:0,y:0})>=3;if(!f&&!y)return;const{point:x}=h,{timestamp:g}=$;this.history.push({...x,timestamp:g});const{onStart:v,onMove:b}=this.handlers;f||(v&&v(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),b&&b(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=dn(f,this.transformPagePoint),L.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:y,onSessionEnd:x,resumeAnimation:g}=this.handlers;if(this.dragSnapToOrigin&&g&&g(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=hn(h.type==="pointercancel"?this.lastMoveEventInfo:dn(f,this.transformPagePoint),this.history);this.startEvent&&y&&y(h,v),x&&x(h,v)},!Ql(t))return;this.dragSnapToOrigin=o,this.handlers=s,this.transformPagePoint=n,this.contextWindow=r||window;const i=ms(t),a=dn(i,this.transformPagePoint),{point:l}=a,{timestamp:c}=$;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=s;u&&u(t,hn(a,this.history)),this.removeListeners=qe($e(this.contextWindow,"pointermove",this.handlePointerMove),$e(this.contextWindow,"pointerup",this.handlePointerUp),$e(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),me(this.updatePoint)}}function dn(e,t){return t?{point:t(e.point)}:e}function Fi(e,t){return{x:e.x-t.x,y:e.y-t.y}}function hn({point:e},t){return{point:e,delta:Fi(e,Xl(t)),offset:Fi(e,_p(t)),velocity:zp(t,.1)}}function _p(e){return e[0]}function Xl(e){return e[e.length-1]}function zp(e,t){if(e.length<2)return{x:0,y:0};let s=e.length-1,n=null;const r=Xl(e);for(;s>=0&&(n=e[s],!(r.timestamp-n.timestamp>Re(t)));)s--;if(!n)return{x:0,y:0};const o=ke(r.timestamp-n.timestamp);if(o===0)return{x:0,y:0};const i={x:(r.x-n.x)/o,y:(r.y-n.y)/o};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function gt(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const Zl=1e-4,Hp=1-Zl,Kp=1+Zl,Jl=.01,Wp=0-Jl,Gp=0+Jl;function oe(e){return e.max-e.min}function qp(e,t,s){return Math.abs(e-t)<=s}function Bi(e,t,s,n=.5){e.origin=n,e.originPoint=_(t.min,t.max,e.origin),e.scale=oe(s)/oe(t),e.translate=_(s.min,s.max,e.origin)-e.originPoint,(e.scale>=Hp&&e.scale<=Kp||isNaN(e.scale))&&(e.scale=1),(e.translate>=Wp&&e.translate<=Gp||isNaN(e.translate))&&(e.translate=0)}function ts(e,t,s,n){Bi(e.x,t.x,s.x,n?n.originX:void 0),Bi(e.y,t.y,s.y,n?n.originY:void 0)}function Ui(e,t,s){e.min=s.min+t.min,e.max=e.min+oe(t)}function $p(e,t,s){Ui(e.x,t.x,s.x),Ui(e.y,t.y,s.y)}function _i(e,t,s){e.min=t.min-s.min,e.max=e.min+oe(t)}function ss(e,t,s){_i(e.x,t.x,s.x),_i(e.y,t.y,s.y)}function Qp(e,{min:t,max:s},n){return t!==void 0&&e<t?e=n?_(t,e,n.min):Math.max(e,t):s!==void 0&&e>s&&(e=n?_(s,e,n.max):Math.min(e,s)),e}function zi(e,t,s){return{min:t!==void 0?e.min+t:void 0,max:s!==void 0?e.max+s-(e.max-e.min):void 0}}function Yp(e,{top:t,left:s,bottom:n,right:r}){return{x:zi(e.x,s,r),y:zi(e.y,t,n)}}function Hi(e,t){let s=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([s,n]=[n,s]),{min:s,max:n}}function Xp(e,t){return{x:Hi(e.x,t.x),y:Hi(e.y,t.y)}}function Zp(e,t){let s=.5;const n=oe(e),r=oe(t);return r>n?s=ht(t.min,t.max-n,e.min):n>r&&(s=ht(e.min,e.max-r,t.min)),Ne(0,1,s)}function Jp(e,t){const s={};return t.min!==void 0&&(s.min=t.min-e.min),t.max!==void 0&&(s.max=t.max-e.min),s}const Xn=.35;function em(e=Xn){return e===!1?e=0:e===!0&&(e=Xn),{x:Ki(e,"left","right"),y:Ki(e,"top","bottom")}}function Ki(e,t,s){return{min:Wi(e,t),max:Wi(e,s)}}function Wi(e,t){return typeof e=="number"?e:e[t]||0}const Gi=()=>({translate:0,scale:1,origin:0,originPoint:0}),xt=()=>({x:Gi(),y:Gi()}),qi=()=>({min:0,max:0}),W=()=>({x:qi(),y:qi()});function ce(e){return[e("x"),e("y")]}function ec({top:e,left:t,right:s,bottom:n}){return{x:{min:t,max:s},y:{min:e,max:n}}}function tm({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function sm(e,t){if(!t)return e;const s=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:s.y,left:s.x,bottom:n.y,right:n.x}}function fn(e){return e===void 0||e===1}function Zn({scale:e,scaleX:t,scaleY:s}){return!fn(e)||!fn(t)||!fn(s)}function Je(e){return Zn(e)||tc(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function tc(e){return $i(e.x)||$i(e.y)}function $i(e){return e&&e!=="0%"}function Fs(e,t,s){const n=e-s,r=t*n;return s+r}function Qi(e,t,s,n,r){return r!==void 0&&(e=Fs(e,r,n)),Fs(e,s,n)+t}function Jn(e,t=0,s=1,n,r){e.min=Qi(e.min,t,s,n,r),e.max=Qi(e.max,t,s,n,r)}function sc(e,{x:t,y:s}){Jn(e.x,t.translate,t.scale,t.originPoint),Jn(e.y,s.translate,s.scale,s.originPoint)}const Yi=.999999999999,Xi=1.0000000000001;function nm(e,t,s,n=!1){const r=s.length;if(!r)return;t.x=t.y=1;let o,i;for(let a=0;a<r;a++){o=s[a],i=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(n&&o.options.layoutScroll&&o.scroll&&o!==o.root&&bt(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,sc(e,i)),n&&Je(o.latestValues)&&bt(e,o.latestValues))}t.x<Xi&&t.x>Yi&&(t.x=1),t.y<Xi&&t.y>Yi&&(t.y=1)}function vt(e,t){e.min=e.min+t,e.max=e.max+t}function Zi(e,t,s,n,r=.5){const o=_(e.min,e.max,r);Jn(e,t,s,o,n)}function bt(e,t){Zi(e.x,t.x,t.scaleX,t.scale,t.originX),Zi(e.y,t.y,t.scaleY,t.scale,t.originY)}function nc(e,t){return ec(sm(e.getBoundingClientRect(),t))}function rm(e,t,s){const n=nc(e,s),{scroll:r}=t;return r&&(vt(n.x,r.offset.x),vt(n.y,r.offset.y)),n}const rc=({current:e})=>e?e.ownerDocument.defaultView:null,im=new WeakMap;class om{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=W(),this.visualElement=t}start(t,{snapToCursor:s=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&n.isPresent===!1)return;const r=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(ms(u).point)},o=(u,h)=>{const{drag:f,dragPropagation:y,onDragStart:x}=this.getProps();if(f&&!y&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Fp(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ce(v=>{let b=this.getAxisMotionValue(v).get()||0;if(we.test(b)){const{projection:w}=this.visualElement;if(w&&w.layout){const T=w.layout.layoutBox[v];T&&(b=oe(T)*(parseFloat(b)/100))}}this.originPoint[v]=b}),x&&L.postRender(()=>x(u,h)),Qn(this.visualElement,"transform");const{animationState:g}=this.visualElement;g&&g.setActive("whileDrag",!0)},i=(u,h)=>{const{dragPropagation:f,dragDirectionLock:y,onDirectionLock:x,onDrag:g}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:v}=h;if(y&&this.currentDirection===null){this.currentDirection=am(v),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",h.point,v),this.updateAxis("y",h.point,v),this.visualElement.render(),g&&g(u,h)},a=(u,h)=>this.stop(u,h),l=()=>ce(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new Yl(t,{onSessionStart:r,onStart:o,onMove:i,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:rc(this.visualElement)})}stop(t,s){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=s;this.startAnimation(r);const{onDragEnd:o}=this.getProps();o&&L.postRender(()=>o(t,s))}cancel(){this.isDragging=!1;const{projection:t,animationState:s}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(t,s,n){const{drag:r}=this.getProps();if(!n||!Ts(t,r,this.currentDirection))return;const o=this.getAxisMotionValue(t);let i=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(i=Qp(i,this.constraints[t],this.elastic[t])),o.set(i)}resolveConstraints(){var t;const{dragConstraints:s,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;s&&gt(s)?this.constraints||(this.constraints=this.resolveRefConstraints()):s&&r?this.constraints=Yp(r.layoutBox,s):this.constraints=!1,this.elastic=em(n),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&ce(i=>{this.constraints!==!1&&this.getAxisMotionValue(i)&&(this.constraints[i]=Jp(r.layoutBox[i],this.constraints[i]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:s}=this.getProps();if(!t||!gt(t))return!1;const n=t.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const o=rm(n,r.root,this.visualElement.getTransformPagePoint());let i=Xp(r.layout.layoutBox,o);if(s){const a=s(tm(i));this.hasMutatedConstraints=!!a,a&&(i=ec(a))}return i}startAnimation(t){const{drag:s,dragMomentum:n,dragElastic:r,dragTransition:o,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=ce(u=>{if(!Ts(u,s,this.currentDirection))return;let h=l&&l[u]||{};i&&(h={min:0,max:0});const f=r?200:1e6,y=r?40:1e7,x={type:"inertia",velocity:n?t[u]:0,bounceStiffness:f,bounceDamping:y,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,x)});return Promise.all(c).then(a)}startAxisValueAnimation(t,s){const n=this.getAxisMotionValue(t);return Qn(this.visualElement,t),n.start(Nr(t,n,0,s,this.visualElement,!1))}stopAnimation(){ce(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ce(t=>{var s;return(s=this.getAxisMotionValue(t).animation)===null||s===void 0?void 0:s.pause()})}getAnimationState(t){var s;return(s=this.getAxisMotionValue(t).animation)===null||s===void 0?void 0:s.state}getAxisMotionValue(t){const s=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),r=n[s];return r||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){ce(s=>{const{drag:n}=this.getProps();if(!Ts(s,n,this.currentDirection))return;const{projection:r}=this.visualElement,o=this.getAxisMotionValue(s);if(r&&r.layout){const{min:i,max:a}=r.layout.layoutBox[s];o.set(t[s]-_(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:s}=this.getProps(),{projection:n}=this.visualElement;if(!gt(s)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};ce(i=>{const a=this.getAxisMotionValue(i);if(a&&this.constraints!==!1){const l=a.get();r[i]=Zp({min:l,max:l},this.constraints[i])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),ce(i=>{if(!Ts(i,t,null))return;const a=this.getAxisMotionValue(i),{min:l,max:c}=this.constraints[i];a.set(_(l,c,r[i]))})}addListeners(){if(!this.visualElement.current)return;im.set(this.visualElement,this);const t=this.visualElement.current,s=$e(t,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),n=()=>{const{dragConstraints:l}=this.getProps();gt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,o=r.addEventListener("measure",n);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),L.read(n);const i=Ee(window,"resize",()=>this.scalePositionWithinConstraints()),a=r.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(ce(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{i(),s(),o(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:o=!1,dragElastic:i=Xn,dragMomentum:a=!0}=t;return{...t,drag:s,dragDirectionLock:n,dragPropagation:r,dragConstraints:o,dragElastic:i,dragMomentum:a}}}function Ts(e,t,s){return(t===!0||t===e)&&(s===null||s===e)}function am(e,t=10){let s=null;return Math.abs(e.y)>t?s="y":Math.abs(e.x)>t&&(s="x"),s}class lm extends Ye{constructor(t){super(t),this.removeGroupControls=Q,this.removeListeners=Q,this.controls=new om(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Q}unmount(){this.removeGroupControls(),this.removeListeners()}}const Ji=e=>(t,s)=>{e&&L.postRender(()=>e(t,s))};class cm extends Ye{constructor(){super(...arguments),this.removePointerDownListener=Q}onPointerDown(t){this.session=new Yl(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rc(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:s,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:Ji(t),onStart:Ji(s),onMove:n,onEnd:(o,i)=>{delete this.session,r&&L.postRender(()=>r(o,i))}}}mount(){this.removePointerDownListener=$e(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const qs=m.createContext(null);function um(){const e=m.useContext(qs);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:s,register:n}=e,r=m.useId();m.useEffect(()=>n(r),[]);const o=m.useCallback(()=>s&&s(r),[r,s]);return!t&&s?[!1,o]:[!0]}const Fr=m.createContext({}),ic=m.createContext({}),Es={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function eo(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const qt={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(R.test(e))e=parseFloat(e);else return e;const s=eo(e,t.target.x),n=eo(e,t.target.y);return`${s}% ${n}%`}},dm={correct:(e,{treeScale:t,projectionDelta:s})=>{const n=e,r=Qe.parse(e);if(r.length>5)return n;const o=Qe.createTransformer(e),i=typeof r[0]!="number"?1:0,a=s.x.scale*t.x,l=s.y.scale*t.y;r[0+i]/=a,r[1+i]/=l;const c=_(a,l,.5);return typeof r[2+i]=="number"&&(r[2+i]/=c),typeof r[3+i]=="number"&&(r[3+i]/=c),o(r)}},Bs={};function hm(e){Object.assign(Bs,e)}const{schedule:Br,cancel:s0}=ol(queueMicrotask,!1);class fm extends m.Component{componentDidMount(){const{visualElement:t,layoutGroup:s,switchLayoutGroup:n,layoutId:r}=this.props,{projection:o}=t;hm(pm),o&&(s.group&&s.group.add(o),n&&n.register&&r&&n.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Es.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:s,visualElement:n,drag:r,isPresent:o}=this.props,i=n.projection;return i&&(i.isPresent=o,r||t.layoutDependency!==s||s===void 0?i.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?i.promote():i.relegate()||L.postRender(()=>{const a=i.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Br.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:s,switchLayoutGroup:n}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function oc(e){const[t,s]=um(),n=m.useContext(Fr);return d.jsx(fm,{...e,layoutGroup:n,switchLayoutGroup:m.useContext(ic),isPresent:t,safeToRemove:s})}const pm={borderRadius:{...qt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:qt,borderTopRightRadius:qt,borderBottomLeftRadius:qt,borderBottomRightRadius:qt,boxShadow:dm},ac=["TopLeft","TopRight","BottomLeft","BottomRight"],mm=ac.length,to=e=>typeof e=="string"?parseFloat(e):e,so=e=>typeof e=="number"||R.test(e);function ym(e,t,s,n,r,o){r?(e.opacity=_(0,s.opacity!==void 0?s.opacity:1,gm(n)),e.opacityExit=_(t.opacity!==void 0?t.opacity:1,0,xm(n))):o&&(e.opacity=_(t.opacity!==void 0?t.opacity:1,s.opacity!==void 0?s.opacity:1,n));for(let i=0;i<mm;i++){const a=`border${ac[i]}Radius`;let l=no(t,a),c=no(s,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||so(l)===so(c)?(e[a]=Math.max(_(to(l),to(c),n),0),(we.test(c)||we.test(l))&&(e[a]+="%")):e[a]=c}(t.rotate||s.rotate)&&(e.rotate=_(t.rotate||0,s.rotate||0,n))}function no(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const gm=lc(0,.5,fl),xm=lc(.5,.95,Q);function lc(e,t,s){return n=>n<e?0:n>t?1:s(ht(e,t,n))}function ro(e,t){e.min=t.min,e.max=t.max}function le(e,t){ro(e.x,t.x),ro(e.y,t.y)}function io(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function oo(e,t,s,n,r){return e-=t,e=Fs(e,1/s,n),r!==void 0&&(e=Fs(e,1/r,n)),e}function vm(e,t=0,s=1,n=.5,r,o=e,i=e){if(we.test(t)&&(t=parseFloat(t),t=_(i.min,i.max,t/100)-i.min),typeof t!="number")return;let a=_(o.min,o.max,n);e===o&&(a-=t),e.min=oo(e.min,t,s,a,r),e.max=oo(e.max,t,s,a,r)}function ao(e,t,[s,n,r],o,i){vm(e,t[s],t[n],t[r],t.scale,o,i)}const bm=["x","scaleX","originX"],wm=["y","scaleY","originY"];function lo(e,t,s,n){ao(e.x,t,bm,s?s.x:void 0,n?n.x:void 0),ao(e.y,t,wm,s?s.y:void 0,n?n.y:void 0)}function co(e){return e.translate===0&&e.scale===1}function cc(e){return co(e.x)&&co(e.y)}function uo(e,t){return e.min===t.min&&e.max===t.max}function Tm(e,t){return uo(e.x,t.x)&&uo(e.y,t.y)}function ho(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function uc(e,t){return ho(e.x,t.x)&&ho(e.y,t.y)}function fo(e){return oe(e.x)/oe(e.y)}function po(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Pm{constructor(){this.members=[]}add(t){Dr(this.members,t),t.scheduleRender()}remove(t){if(Vr(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(t){const s=this.members.findIndex(r=>t===r);if(s===0)return!1;let n;for(let r=s;r>=0;r--){const o=this.members[r];if(o.isPresent!==!1){n=o;break}}return n?(this.promote(n),!0):!1}promote(t,s){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,s&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:r}=t.options;r===!1&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:s,resumingFrom:n}=t;s.onExitComplete&&s.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Sm(e,t,s){let n="";const r=e.x.translate/t.x,o=e.y.translate/t.y,i=(s==null?void 0:s.z)||0;if((r||o||i)&&(n=`translate3d(${r}px, ${o}px, ${i}px) `),(t.x!==1||t.y!==1)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),s){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:f,skewX:y,skewY:x}=s;c&&(n=`perspective(${c}px) ${n}`),u&&(n+=`rotate(${u}deg) `),h&&(n+=`rotateX(${h}deg) `),f&&(n+=`rotateY(${f}deg) `),y&&(n+=`skewX(${y}deg) `),x&&(n+=`skewY(${x}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(n+=`scale(${a}, ${l})`),n||"none"}const Cm=(e,t)=>e.depth-t.depth;class jm{constructor(){this.children=[],this.isDirty=!1}add(t){Dr(this.children,t),this.isDirty=!0}remove(t){Vr(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Cm),this.isDirty=!1,this.children.forEach(t)}}function Rs(e){const t=J(e)?e.get():e;return mp(t)?t.toValue():t}function Am(e,t){const s=Te.now(),n=({timestamp:r})=>{const o=r-s;o>=t&&(me(n),e(o-t))};return L.read(n,!0),()=>me(n)}function Em(e){return e instanceof SVGElement&&e.tagName!=="svg"}function Rm(e,t,s){const n=J(e)?e:be(e);return n.start(Nr("",n,t,s)),n.animation}const et={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Xt=typeof window<"u"&&window.MotionDebug!==void 0,pn=["","X","Y","Z"],km={visibility:"hidden"},mo=1e3;let Mm=0;function mn(e,t,s,n){const{latestValues:r}=t;r[e]&&(s[e]=r[e],t.setStaticValue(e,0),n&&(n[e]=0))}function dc(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const s=Wl(t);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:r,layoutId:o}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",L,!(r||o))}const{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&dc(n)}function hc({attachResizeListener:e,defaultParent:t,measureScroll:s,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(i={},a=t==null?void 0:t()){this.id=Mm++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Xt&&(et.totalNodes=et.resolvedTargetDeltas=et.recalculatedProjection=0),this.nodes.forEach(Vm),this.nodes.forEach(Bm),this.nodes.forEach(Um),this.nodes.forEach(Om),Xt&&window.MotionDebug.record(et)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new jm)}addEventListener(i,a){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new Or),this.eventHandlers.get(i).add(a)}notifyListeners(i,...a){const l=this.eventHandlers.get(i);l&&l.notify(...a)}hasListeners(i){return this.eventHandlers.has(i)}mount(i,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Em(i),this.instance=i;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),e){let h;const f=()=>this.root.updateBlockedByResize=!1;e(i,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Am(f,250),Es.hasAnimatedSinceResize&&(Es.hasAnimatedSinceResize=!1,this.nodes.forEach(go))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:y,layout:x})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const g=this.options.transition||u.getDefaultTransition()||Wm,{onLayoutAnimationStart:v,onLayoutAnimationComplete:b}=u.getProps(),w=!this.targetLayout||!uc(this.targetLayout,x)||y,T=!f&&y;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||T||f&&(w||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,T);const j={...yr(g,"layout"),onPlay:v,onComplete:b};(u.shouldReduceMotion||this.options.layoutRoot)&&(j.delay=0,j.type=!1),this.startAnimation(j)}else f||go(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=x})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,me(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(_m),this.animationId++)}getTransformTemplate(){const{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&dc(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(yo);return}this.isUpdating||this.nodes.forEach(Im),this.isUpdating=!1,this.nodes.forEach(Fm),this.nodes.forEach(Nm),this.nodes.forEach(Dm),this.clearAllSnapshots();const a=Te.now();$.delta=Ne(0,1e3/60,a-$.timestamp),$.timestamp=a,$.isProcessing=!0,on.update.process($),on.preRender.process($),on.render.process($),$.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Br.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Lm),this.sharedNodes.forEach(zm)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,L.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){L.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=W(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(a=!1),a){const l=n(this.instance);this.scroll={animationId:this.root.animationId,phase:i,isRoot:l,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!r)return;const i=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!cc(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;i&&(a||Je(this.latestValues)||u)&&(r(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return i&&(l=this.removeTransform(l)),Gm(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var i;const{visualElement:a}=this.options;if(!a)return W();const l=a.measureViewportBox();if(!(((i=this.scroll)===null||i===void 0?void 0:i.wasRoot)||this.path.some(qm))){const{scroll:u}=this.root;u&&(vt(l.x,u.offset.x),vt(l.y,u.offset.y))}return l}removeElementScroll(i){var a;const l=W();if(le(l,i),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:f}=u;u!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&le(l,i),vt(l.x,h.offset.x),vt(l.y,h.offset.y))}return l}applyTransform(i,a=!1){const l=W();le(l,i);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&bt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),Je(u.latestValues)&&bt(l,u.latestValues)}return Je(this.latestValues)&&bt(l,this.latestValues),l}removeTransform(i){const a=W();le(a,i);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!Je(c.latestValues))continue;Zn(c.latestValues)&&c.updateSnapshot();const u=W(),h=c.measurePageBox();le(u,h),lo(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return Je(this.latestValues)&&lo(a,this.latestValues),a}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==$.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(i||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=$.timestamp,!this.targetDelta&&!this.relativeTarget){const y=this.getClosestProjectingParent();y&&y.layout&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=W(),this.relativeTargetOrigin=W(),ss(this.relativeTargetOrigin,this.layout.layoutBox,y.layout.layoutBox),le(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=W(),this.targetWithTransforms=W()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),$p(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):le(this.target,this.layout.layoutBox),sc(this.target,this.targetDelta)):le(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const y=this.getClosestProjectingParent();y&&!!y.resumingFrom==!!this.resumingFrom&&!y.options.layoutScroll&&y.target&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=W(),this.relativeTargetOrigin=W(),ss(this.relativeTargetOrigin,this.target,y.target),le(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Xt&&et.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Zn(this.parent.latestValues)||tc(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var i;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((i=this.parent)===null||i===void 0)&&i.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===$.timestamp&&(c=!1),c)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;le(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,y=this.treeScale.y;nm(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=W());const{target:x}=a;if(!x){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(io(this.prevProjectionDelta.x,this.projectionDelta.x),io(this.prevProjectionDelta.y,this.projectionDelta.y)),ts(this.projectionDelta,this.layoutCorrected,x,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==y||!po(this.projectionDelta.x,this.prevProjectionDelta.x)||!po(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",x)),Xt&&et.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),i){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=xt(),this.projectionDelta=xt(),this.projectionDeltaWithTransform=xt()}setAnimationOrigin(i,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=xt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=W(),y=l?l.source:void 0,x=this.layout?this.layout.source:void 0,g=y!==x,v=this.getStack(),b=!v||v.members.length<=1,w=!!(g&&!b&&this.options.crossfade===!0&&!this.path.some(Km));this.animationProgress=0;let T;this.mixTargetDelta=j=>{const S=j/1e3;xo(h.x,i.x,S),xo(h.y,i.y,S),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ss(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Hm(this.relativeTarget,this.relativeTargetOrigin,f,S),T&&Tm(this.relativeTarget,T)&&(this.isProjectionDirty=!1),T||(T=W()),le(T,this.relativeTarget)),g&&(this.animationValues=u,ym(u,c,this.latestValues,S,w,b)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(me(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=L.update(()=>{Es.hasAnimatedSinceResize=!0,this.currentAnimation=Rm(0,mo,{...i,onUpdate:a=>{this.mixTargetDelta(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(mo),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=i;if(!(!a||!l||!c)){if(this!==i&&this.layout&&c&&fc(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||W();const h=oe(this.layout.layoutBox.x);l.x.min=i.target.x.min,l.x.max=l.x.min+h;const f=oe(this.layout.layoutBox.y);l.y.min=i.target.y.min,l.y.max=l.y.min+f}le(a,l),bt(a,u),ts(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(i,a){this.sharedNodes.has(i)||this.sharedNodes.set(i,new Pm),this.sharedNodes.get(i).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){var i;const{layoutId:a}=this.options;return a?((i=this.getStack())===null||i===void 0?void 0:i.lead)||this:this}getPrevLead(){var i;const{layoutId:a}=this.options;return a?(i=this.getStack())===null||i===void 0?void 0:i.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),i&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetSkewAndRotation(){const{visualElement:i}=this.options;if(!i)return;let a=!1;const{latestValues:l}=i;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&mn("z",i,c,this.animationValues);for(let u=0;u<pn.length;u++)mn(`rotate${pn[u]}`,i,c,this.animationValues),mn(`skew${pn[u]}`,i,c,this.animationValues);i.render();for(const u in c)i.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);i.scheduleRender()}getProjectionStyles(i){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return km;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Rs(i==null?void 0:i.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const g={};return this.options.layoutId&&(g.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,g.pointerEvents=Rs(i==null?void 0:i.pointerEvents)||""),this.hasProjected&&!Je(this.latestValues)&&(g.transform=u?u({},""):"none",this.hasProjected=!1),g}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),c.transform=Sm(this.projectionDeltaWithTransform,this.treeScale,f),u&&(c.transform=u(f,c.transform));const{x:y,y:x}=this.projectionDelta;c.transformOrigin=`${y.origin*100}% ${x.origin*100}% 0`,h.animationValues?c.opacity=h===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const g in Bs){if(f[g]===void 0)continue;const{correct:v,applyTo:b}=Bs[g],w=c.transform==="none"?f[g]:v(f[g],h);if(b){const T=b.length;for(let j=0;j<T;j++)c[b[j]]=w}else c[g]=w}return this.options.layoutId&&(c.pointerEvents=h===this?Rs(i==null?void 0:i.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>{var a;return(a=i.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(yo),this.root.sharedNodes.clear()}}}function Nm(e){e.updateLayout()}function Dm(e){var t;const s=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&s&&e.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:r}=e.layout,{animationType:o}=e.options,i=s.source!==e.layout.source;o==="size"?ce(h=>{const f=i?s.measuredBox[h]:s.layoutBox[h],y=oe(f);f.min=n[h].min,f.max=f.min+y}):fc(o,s.layoutBox,n)&&ce(h=>{const f=i?s.measuredBox[h]:s.layoutBox[h],y=oe(n[h]);f.max=f.min+y,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+y)});const a=xt();ts(a,n,s.layoutBox);const l=xt();i?ts(l,e.applyTransform(r,!0),s.measuredBox):ts(l,n,s.layoutBox);const c=!cc(a);let u=!1;if(!e.resumeFrom){const h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:y}=h;if(f&&y){const x=W();ss(x,s.layoutBox,f.layoutBox);const g=W();ss(g,n,y.layoutBox),uc(x,g)||(u=!0),h.options.layoutRoot&&(e.relativeTarget=g,e.relativeTargetOrigin=x,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:s,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(e.isLead()){const{onExitComplete:n}=e.options;n&&n()}e.options.transition=void 0}function Vm(e){Xt&&et.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Om(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Lm(e){e.clearSnapshot()}function yo(e){e.clearMeasurements()}function Im(e){e.isLayoutDirty=!1}function Fm(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function go(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Bm(e){e.resolveTargetDelta()}function Um(e){e.calcProjection()}function _m(e){e.resetSkewAndRotation()}function zm(e){e.removeLeadSnapshot()}function xo(e,t,s){e.translate=_(t.translate,0,s),e.scale=_(t.scale,1,s),e.origin=t.origin,e.originPoint=t.originPoint}function vo(e,t,s,n){e.min=_(t.min,s.min,n),e.max=_(t.max,s.max,n)}function Hm(e,t,s,n){vo(e.x,t.x,s.x,n),vo(e.y,t.y,s.y,n)}function Km(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Wm={duration:.45,ease:[.4,0,.1,1]},bo=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),wo=bo("applewebkit/")&&!bo("chrome/")?Math.round:Q;function To(e){e.min=wo(e.min),e.max=wo(e.max)}function Gm(e){To(e.x),To(e.y)}function fc(e,t,s){return e==="position"||e==="preserve-aspect"&&!qp(fo(t),fo(s),.2)}function qm(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const $m=hc({attachResizeListener:(e,t)=>Ee(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),yn={current:void 0},pc=hc({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!yn.current){const e=new $m({});e.mount(window),e.setOptions({layoutScroll:!0}),yn.current=e}return yn.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Qm={pan:{Feature:cm},drag:{Feature:lm,ProjectionNode:pc,MeasureLayout:oc}};function Po(e,t,s){const{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover",s);const r=n[s?"onHoverStart":"onHoverEnd"];r&&L.postRender(()=>r(t,ms(t)))}class Ym extends Ye{mount(){const{current:t,props:s}=this.node;t&&(this.unmount=Ip(t,n=>(Po(this.node,n,!0),r=>Po(this.node,r,!1)),{passive:!s.onHoverStart&&!s.onHoverEnd}))}unmount(){}}class Xm extends Ye{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=qe(Ee(this.node.current,"focus",()=>this.onFocus()),Ee(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const mc=(e,t)=>t?e===t?!0:mc(e,t.parentElement):!1;function gn(e,t){if(!t)return;const s=new PointerEvent("pointer"+e);t(s,ms(s))}class Zm extends Ye{constructor(){super(...arguments),this.removeStartListeners=Q,this.removeEndListeners=Q,this.removeAccessibleListeners=Q,this.startPointerPress=(t,s)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),o=$e(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:u,globalTapTarget:h}=this.node.getProps(),f=!h&&!mc(this.node.current,a.target)?u:c;f&&L.update(()=>f(a,l))},{passive:!(n.onTap||n.onPointerUp)}),i=$e(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=qe(o,i),this.startPress(t,s)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const i=a=>{a.key!=="Enter"||!this.checkPressEnd()||gn("up",(l,c)=>{const{onTap:u}=this.node.getProps();u&&L.postRender(()=>u(l,c))})};this.removeEndListeners(),this.removeEndListeners=Ee(this.node.current,"keyup",i),gn("down",(a,l)=>{this.startPress(a,l)})},s=Ee(this.node.current,"keydown",t),n=()=>{this.isPressing&&gn("cancel",(o,i)=>this.cancelPress(o,i))},r=Ee(this.node.current,"blur",n);this.removeAccessibleListeners=qe(s,r)}}startPress(t,s){this.isPressing=!0;const{onTapStart:n,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&L.postRender(()=>n(t,s))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!$l()}cancelPress(t,s){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&L.postRender(()=>n(t,s))}mount(){const t=this.node.getProps(),s=$e(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),n=Ee(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=qe(s,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const er=new WeakMap,xn=new WeakMap,Jm=e=>{const t=er.get(e.target);t&&t(e)},ey=e=>{e.forEach(Jm)};function ty({root:e,...t}){const s=e||document;xn.has(s)||xn.set(s,{});const n=xn.get(s),r=JSON.stringify(t);return n[r]||(n[r]=new IntersectionObserver(ey,{root:e,...t})),n[r]}function sy(e,t,s){const n=ty(t);return er.set(e,s),n.observe(e),()=>{er.delete(e),n.unobserve(e)}}const ny={some:0,all:1};class ry extends Ye{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:s,margin:n,amount:r="some",once:o}=t,i={root:s?s.current:void 0,rootMargin:n,threshold:typeof r=="number"?r:ny[r]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(l)};return sy(this.node.current,i,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:s}=this.node;["amount","margin","root"].some(iy(t,s))&&this.startObserver()}unmount(){}}function iy({viewport:e={}},{viewport:t={}}={}){return s=>e[s]!==t[s]}const oy={inView:{Feature:ry},tap:{Feature:Zm},focus:{Feature:Xm},hover:{Feature:Ym}},ay={layout:{ProjectionNode:pc,MeasureLayout:oc}},$s=m.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Qs=m.createContext({}),Ur=typeof window<"u",Ys=Ur?m.useLayoutEffect:m.useEffect,yc=m.createContext({strict:!1});function ly(e,t,s,n,r){var o,i;const{visualElement:a}=m.useContext(Qs),l=m.useContext(yc),c=m.useContext(qs),u=m.useContext($s).reducedMotion,h=m.useRef();n=n||l.renderer,!h.current&&n&&(h.current=n(e,{visualState:t,parent:a,props:s,presenceContext:c,blockInitialAnimation:c?c.initial===!1:!1,reducedMotionConfig:u}));const f=h.current,y=m.useContext(ic);f&&!f.projection&&r&&(f.type==="html"||f.type==="svg")&&cy(h.current,s,r,y);const x=m.useRef(!1);m.useInsertionEffect(()=>{f&&x.current&&f.update(s,c)});const g=s[Kl],v=m.useRef(!!g&&!(!((o=window.MotionHandoffIsComplete)===null||o===void 0)&&o.call(window,g))&&((i=window.MotionHasOptimisedAnimation)===null||i===void 0?void 0:i.call(window,g)));return Ys(()=>{f&&(x.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Br.render(f.render),v.current&&f.animationState&&f.animationState.animateChanges())}),m.useEffect(()=>{f&&(!v.current&&f.animationState&&f.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var b;(b=window.MotionHandoffMarkAsComplete)===null||b===void 0||b.call(window,g)}),v.current=!1))}),f}function cy(e,t,s,n){const{layoutId:r,layout:o,drag:i,dragConstraints:a,layoutScroll:l,layoutRoot:c}=t;e.projection=new s(e.latestValues,t["data-framer-portal-id"]?void 0:gc(e.parent)),e.projection.setOptions({layoutId:r,layout:o,alwaysMeasureLayout:!!i||a&&gt(a),visualElement:e,animationType:typeof o=="string"?o:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:c})}function gc(e){if(e)return e.options.allowProjection!==!1?e.projection:gc(e.parent)}function uy(e,t,s){return m.useCallback(n=>{n&&e.mount&&e.mount(n),t&&(n?t.mount(n):t.unmount()),s&&(typeof s=="function"?s(n):gt(s)&&(s.current=n))},[t])}function Xs(e){return Ks(e.animate)||mr.some(t=>is(e[t]))}function xc(e){return!!(Xs(e)||e.variants)}function dy(e,t){if(Xs(e)){const{initial:s,animate:n}=e;return{initial:s===!1||is(s)?s:void 0,animate:is(n)?n:void 0}}return e.inherit!==!1?t:{}}function hy(e){const{initial:t,animate:s}=dy(e,m.useContext(Qs));return m.useMemo(()=>({initial:t,animate:s}),[So(t),So(s)])}function So(e){return Array.isArray(e)?e.join(" "):e}const Co={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Bt={};for(const e in Co)Bt[e]={isEnabled:t=>Co[e].some(s=>!!t[s])};function fy(e){for(const t in e)Bt[t]={...Bt[t],...e[t]}}const py=Symbol.for("motionComponentSymbol");function my({preloadedFeatures:e,createVisualElement:t,useRender:s,useVisualState:n,Component:r}){e&&fy(e);function o(a,l){let c;const u={...m.useContext($s),...a,layoutId:yy(a)},{isStatic:h}=u,f=hy(a),y=n(a,h);if(!h&&Ur){gy();const x=xy(u);c=x.MeasureLayout,f.visualElement=ly(r,y,u,t,x.ProjectionNode)}return d.jsxs(Qs.Provider,{value:f,children:[c&&f.visualElement?d.jsx(c,{visualElement:f.visualElement,...u}):null,s(r,a,uy(y,f.visualElement,l),y,h,f.visualElement)]})}const i=m.forwardRef(o);return i[py]=r,i}function yy({layoutId:e}){const t=m.useContext(Fr).id;return t&&e!==void 0?t+"-"+e:e}function gy(e,t){m.useContext(yc).strict}function xy(e){const{drag:t,layout:s}=Bt;if(!t&&!s)return{};const n={...t,...s};return{MeasureLayout:t!=null&&t.isEnabled(e)||s!=null&&s.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}const vy=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function _r(e){return typeof e!="string"||e.includes("-")?!1:!!(vy.indexOf(e)>-1||/[A-Z]/u.test(e))}function vc(e,{style:t,vars:s},n,r){Object.assign(e.style,t,r&&r.getProjectionStyles(n));for(const o in s)e.style.setProperty(o,s[o])}const bc=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function wc(e,t,s,n){vc(e,t,void 0,n);for(const r in t.attrs)e.setAttribute(bc.has(r)?r:Lr(r),t.attrs[r])}function Tc(e,{layout:t,layoutId:s}){return ft.has(e)||e.startsWith("origin")||(t||s!==void 0)&&(!!Bs[e]||e==="opacity")}function zr(e,t,s){var n;const{style:r}=e,o={};for(const i in r)(J(r[i])||t.style&&J(t.style[i])||Tc(i,e)||((n=s==null?void 0:s.getValue(i))===null||n===void 0?void 0:n.liveStyle)!==void 0)&&(o[i]=r[i]);return o}function Pc(e,t,s){const n=zr(e,t,s);for(const r in e)if(J(e[r])||J(t[r])){const o=hs.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[o]=e[r]}return n}function Ht(e){const t=m.useRef(null);return t.current===null&&(t.current=e()),t.current}function by({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:s},n,r,o){const i={latestValues:wy(n,r,o,e),renderState:t()};return s&&(i.mount=a=>s(n,a,i)),i}const Sc=e=>(t,s)=>{const n=m.useContext(Qs),r=m.useContext(qs),o=()=>by(e,t,n,r);return s?o():Ht(o)};function wy(e,t,s,n){const r={},o=n(e,{});for(const f in o)r[f]=Rs(o[f]);let{initial:i,animate:a}=e;const l=Xs(e),c=xc(e);t&&c&&!l&&e.inherit!==!1&&(i===void 0&&(i=t.initial),a===void 0&&(a=t.animate));let u=s?s.initial===!1:!1;u=u||i===!1;const h=u?a:i;if(h&&typeof h!="boolean"&&!Ks(h)){const f=Array.isArray(h)?h:[h];for(let y=0;y<f.length;y++){const x=fr(e,f[y]);if(x){const{transitionEnd:g,transition:v,...b}=x;for(const w in b){let T=b[w];if(Array.isArray(T)){const j=u?T.length-1:0;T=T[j]}T!==null&&(r[w]=T)}for(const w in g)r[w]=g[w]}}}return r}const Hr=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Cc=()=>({...Hr(),attrs:{}}),jc=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Ty={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Py=hs.length;function Sy(e,t,s){let n="",r=!0;for(let o=0;o<Py;o++){const i=hs[o],a=e[i];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(i.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||s){const c=jc(a,Pr[i]);if(!l){r=!1;const u=Ty[i]||i;n+=`${u}(${c}) `}s&&(t[i]=c)}}return n=n.trim(),s?n=s(t,r?"":n):r&&(n="none"),n}function Kr(e,t,s){const{style:n,vars:r,transformOrigin:o}=e;let i=!1,a=!1;for(const l in t){const c=t[l];if(ft.has(l)){i=!0;continue}else if(xl(l)){r[l]=c;continue}else{const u=jc(c,Pr[l]);l.startsWith("origin")?(a=!0,o[l]=u):n[l]=u}}if(t.transform||(i||s?n.transform=Sy(t,e.transform,s):n.transform&&(n.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;n.transformOrigin=`${l} ${c} ${u}`}}function jo(e,t,s){return typeof e=="string"?e:R.transform(t+s*e)}function Cy(e,t,s){const n=jo(t,e.x,e.width),r=jo(s,e.y,e.height);return`${n} ${r}`}const jy={offset:"stroke-dashoffset",array:"stroke-dasharray"},Ay={offset:"strokeDashoffset",array:"strokeDasharray"};function Ey(e,t,s=1,n=0,r=!0){e.pathLength=1;const o=r?jy:Ay;e[o.offset]=R.transform(-n);const i=R.transform(t),a=R.transform(s);e[o.array]=`${i} ${a}`}function Wr(e,{attrX:t,attrY:s,attrScale:n,originX:r,originY:o,pathLength:i,pathSpacing:a=1,pathOffset:l=0,...c},u,h){if(Kr(e,c,h),u){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:y,dimensions:x}=e;f.transform&&(x&&(y.transform=f.transform),delete f.transform),x&&(r!==void 0||o!==void 0||y.transform)&&(y.transformOrigin=Cy(x,r!==void 0?r:.5,o!==void 0?o:.5)),t!==void 0&&(f.x=t),s!==void 0&&(f.y=s),n!==void 0&&(f.scale=n),i!==void 0&&Ey(f,i,a,l,!1)}const Gr=e=>typeof e=="string"&&e.toLowerCase()==="svg",Ry={useVisualState:Sc({scrapeMotionValuesFromProps:Pc,createRenderState:Cc,onMount:(e,t,{renderState:s,latestValues:n})=>{L.read(()=>{try{s.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{s.dimensions={x:0,y:0,width:0,height:0}}}),L.render(()=>{Wr(s,n,Gr(t.tagName),e.transformTemplate),wc(t,s)})}})},ky={useVisualState:Sc({scrapeMotionValuesFromProps:zr,createRenderState:Hr})};function Ac(e,t,s){for(const n in t)!J(t[n])&&!Tc(n,s)&&(e[n]=t[n])}function My({transformTemplate:e},t){return m.useMemo(()=>{const s=Hr();return Kr(s,t,e),Object.assign({},s.vars,s.style)},[t])}function Ny(e,t){const s=e.style||{},n={};return Ac(n,s,e),Object.assign(n,My(e,t)),n}function Dy(e,t){const s={},n=Ny(e,t);return e.drag&&e.dragListener!==!1&&(s.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(s.tabIndex=0),s.style=n,s}const Vy=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Us(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Vy.has(e)}let Ec=e=>!Us(e);function Oy(e){e&&(Ec=t=>t.startsWith("on")?!Us(t):e(t))}try{Oy(require("@emotion/is-prop-valid").default)}catch{}function Ly(e,t,s){const n={};for(const r in e)r==="values"&&typeof e.values=="object"||(Ec(r)||s===!0&&Us(r)||!t&&!Us(r)||e.draggable&&r.startsWith("onDrag"))&&(n[r]=e[r]);return n}function Iy(e,t,s,n){const r=m.useMemo(()=>{const o=Cc();return Wr(o,t,Gr(n),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Ac(o,e.style,e),r.style={...o,...r.style}}return r}function Fy(e=!1){return(s,n,r,{latestValues:o},i)=>{const l=(_r(s)?Iy:Dy)(n,o,i,s),c=Ly(n,typeof s=="string",e),u=s!==m.Fragment?{...c,...l,ref:r}:{},{children:h}=n,f=m.useMemo(()=>J(h)?h.get():h,[h]);return m.createElement(s,{...u,children:f})}}function By(e,t){return function(n,{forwardMotionProps:r}={forwardMotionProps:!1}){const i={..._r(n)?Ry:ky,preloadedFeatures:e,useRender:Fy(r),createVisualElement:t,Component:n};return my(i)}}const tr={current:null},Rc={current:!1};function Uy(){if(Rc.current=!0,!!Ur)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>tr.current=e.matches;e.addListener(t),t()}else tr.current=!1}function _y(e,t,s){for(const n in t){const r=t[n],o=s[n];if(J(r))e.addValue(n,r);else if(J(o))e.addValue(n,be(r,{owner:e}));else if(o!==r)if(e.hasValue(n)){const i=e.getValue(n);i.liveStyle===!0?i.jump(r):i.hasAnimated||i.set(r)}else{const i=e.getStaticValue(n);e.addValue(n,be(i!==void 0?i:r,{owner:e}))}}for(const n in s)t[n]===void 0&&e.removeValue(n);return t}const Ao=new WeakMap,zy=[...wl,Z,Qe],Hy=e=>zy.find(bl(e)),Eo=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Ky{scrapeMotionValuesFromProps(t,s,n){return{}}constructor({parent:t,props:s,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:o,visualState:i},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=br,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=Te.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,L.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=i;this.latestValues=l,this.baseTarget={...l},this.initialValues=s.initial?{...l}:{},this.renderState=c,this.parent=t,this.props=s,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Xs(s),this.isVariantNode=xc(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(s,{},this);for(const f in h){const y=h[f];l[f]!==void 0&&J(y)&&y.set(l[f],!1)}}mount(t){this.current=t,Ao.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,n)=>this.bindToMotionValue(n,s)),Rc.current||Uy(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:tr.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Ao.delete(this.current),this.projection&&this.projection.unmount(),me(this.notifyUpdate),me(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const s=this.features[t];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(t,s){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=ft.has(t),r=s.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&L.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=s.on("renderRequest",this.scheduleRender);let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,s)),this.valueSubscriptions.set(t,()=>{r(),o(),i&&i(),s.owner&&s.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Bt){const s=Bt[t];if(!s)continue;const{isEnabled:n,Feature:r}=s;if(!this.features[t]&&r&&n(this.props)&&(this.features[t]=new r(this)),this.features[t]){const o=this.features[t];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):W()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,s){this.latestValues[t]=s}update(t,s){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let n=0;n<Eo.length;n++){const r=Eo[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const o="on"+r,i=t[o];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=_y(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(t),()=>s.variantChildren.delete(t)}addValue(t,s){const n=this.values.get(t);s!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,s),this.values.set(t,s),this.latestValues[t]=s.get())}removeValue(t){this.values.delete(t);const s=this.valueSubscriptions.get(t);s&&(s(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,s){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return n===void 0&&s!==void 0&&(n=be(s===null?void 0:s,{owner:this}),this.addValue(t,n)),n}readValue(t,s){var n;let r=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options);return r!=null&&(typeof r=="string"&&(yl(r)||ml(r))?r=parseFloat(r):!Hy(r)&&Qe.test(s)&&(r=Rl(t,s)),this.setBaseTarget(t,J(r)?r.get():r)),J(r)?r.get():r}setBaseTarget(t,s){this.baseTarget[t]=s}getBaseTarget(t){var s;const{initial:n}=this.props;let r;if(typeof n=="string"||typeof n=="object"){const i=fr(this.props,n,(s=this.presenceContext)===null||s===void 0?void 0:s.custom);i&&(r=i[t])}if(n&&r!==void 0)return r;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!J(o)?o:this.initialValues[t]!==void 0&&r===void 0?void 0:this.baseTarget[t]}on(t,s){return this.events[t]||(this.events[t]=new Or),this.events[t].add(s)}notify(t,...s){this.events[t]&&this.events[t].notify(...s)}}class kc extends Ky{constructor(){super(...arguments),this.KeyframeResolver=kl}sortInstanceNodePosition(t,s){return t.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(t,s){return t.style?t.style[s]:void 0}removeValueFromRenderState(t,{vars:s,style:n}){delete s[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;J(t)&&(this.childSubscription=t.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function Wy(e){return window.getComputedStyle(e)}class Gy extends kc{constructor(){super(...arguments),this.type="html",this.renderInstance=vc}readValueFromInstance(t,s){if(ft.has(s)){const n=Sr(s);return n&&n.default||0}else{const n=Wy(t),r=(xl(s)?n.getPropertyValue(s):n[s])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:s}){return nc(t,s)}build(t,s,n){Kr(t,s,n.transformTemplate)}scrapeMotionValuesFromProps(t,s,n){return zr(t,s,n)}}class qy extends kc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=W}getBaseTargetFromProps(t,s){return t[s]}readValueFromInstance(t,s){if(ft.has(s)){const n=Sr(s);return n&&n.default||0}return s=bc.has(s)?s:Lr(s),t.getAttribute(s)}scrapeMotionValuesFromProps(t,s,n){return Pc(t,s,n)}build(t,s,n){Wr(t,s,this.isSVGTag,n.transformTemplate)}renderInstance(t,s,n,r){wc(t,s,n,r)}mount(t){this.isSVGTag=Gr(t.tagName),super.mount(t)}}const $y=(e,t)=>_r(e)?new qy(t):new Gy(t,{allowProjection:e!==m.Fragment}),Qy=By({...Lp,...oy,...Qm,...ay},$y),P=Oh(Qy);class Yy extends m.Component{getSnapshotBeforeUpdate(t){const s=this.props.childRef.current;if(s&&t.isPresent&&!this.props.isPresent){const n=this.props.sizeRef.current;n.height=s.offsetHeight||0,n.width=s.offsetWidth||0,n.top=s.offsetTop,n.left=s.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Xy({children:e,isPresent:t}){const s=m.useId(),n=m.useRef(null),r=m.useRef({width:0,height:0,top:0,left:0}),{nonce:o}=m.useContext($s);return m.useInsertionEffect(()=>{const{width:i,height:a,top:l,left:c}=r.current;if(t||!n.current||!i||!a)return;n.current.dataset.motionPopId=s;const u=document.createElement("style");return o&&(u.nonce=o),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${i}px !important;
            height: ${a}px !important;
            top: ${l}px !important;
            left: ${c}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),d.jsx(Yy,{isPresent:t,childRef:n,sizeRef:r,children:m.cloneElement(e,{ref:n})})}const Zy=({children:e,initial:t,isPresent:s,onExitComplete:n,custom:r,presenceAffectsLayout:o,mode:i})=>{const a=Ht(Jy),l=m.useId(),c=m.useCallback(h=>{a.set(h,!0);for(const f of a.values())if(!f)return;n&&n()},[a,n]),u=m.useMemo(()=>({id:l,initial:t,isPresent:s,custom:r,onExitComplete:c,register:h=>(a.set(h,!1),()=>a.delete(h))}),o?[Math.random(),c]:[s,c]);return m.useMemo(()=>{a.forEach((h,f)=>a.set(f,!1))},[s]),m.useEffect(()=>{!s&&!a.size&&n&&n()},[s]),i==="popLayout"&&(e=d.jsx(Xy,{isPresent:s,children:e})),d.jsx(qs.Provider,{value:u,children:e})};function Jy(){return new Map}const Ps=e=>e.key||"";function Ro(e){const t=[];return m.Children.forEach(e,s=>{m.isValidElement(s)&&t.push(s)}),t}const eg=({children:e,exitBeforeEnter:t,custom:s,initial:n=!0,onExitComplete:r,presenceAffectsLayout:o=!0,mode:i="sync"})=>{const a=m.useMemo(()=>Ro(e),[e]),l=a.map(Ps),c=m.useRef(!0),u=m.useRef(a),h=Ht(()=>new Map),[f,y]=m.useState(a),[x,g]=m.useState(a);Ys(()=>{c.current=!1,u.current=a;for(let w=0;w<x.length;w++){const T=Ps(x[w]);l.includes(T)?h.delete(T):h.get(T)!==!0&&h.set(T,!1)}},[x,l.length,l.join("-")]);const v=[];if(a!==f){let w=[...a];for(let T=0;T<x.length;T++){const j=x[T],S=Ps(j);l.includes(S)||(w.splice(T,0,j),v.push(j))}i==="wait"&&v.length&&(w=v),g(Ro(w)),y(a);return}const{forceRender:b}=m.useContext(Fr);return d.jsx(d.Fragment,{children:x.map(w=>{const T=Ps(w),j=a===x||l.includes(T),S=()=>{if(h.has(T))h.set(T,!0);else return;let k=!0;h.forEach(V=>{V||(k=!1)}),k&&(b==null||b(),g(u.current),r&&r())};return d.jsx(Zy,{isPresent:j,initial:!c.current||n?void 0:!1,custom:j?void 0:s,presenceAffectsLayout:o,mode:i,onExitComplete:j?void 0:S,children:w},T)})})};function tg(e){const t=Ht(()=>be(e)),{isStatic:s}=m.useContext($s);if(s){const[,n]=m.useState(e);m.useEffect(()=>t.on("change",n),[])}return t}function Mc(e,t){const s=tg(t()),n=()=>s.set(t());return n(),Ys(()=>{const r=()=>L.preRender(n,!1,!0),o=e.map(i=>i.on("change",r));return()=>{o.forEach(i=>i()),me(n)}}),s}const sg=e=>e&&typeof e=="object"&&e.mix,ng=e=>sg(e)?e.mix:void 0;function rg(...e){const t=!Array.isArray(e[0]),s=t?0:-1,n=e[0+s],r=e[1+s],o=e[2+s],i=e[3+s],a=Rr(r,o,{mixer:ng(o[0]),...i});return t?a(n):a}function ig(e){es.current=[],e();const t=Mc(es.current,e);return es.current=void 0,t}function ko(e,t,s,n){if(typeof e=="function")return ig(e);const r=typeof t=="function"?t:rg(t,s,n);return Array.isArray(e)?Mo(e,r):Mo([e],([o])=>r(o))}function Mo(e,t){const s=Ht(()=>[]);return Mc(e,()=>{s.length=0;const n=e.length;for(let r=0;r<n;r++)s[r]=e[r].get();return t(s)})}const ks=new WeakMap;let Ie;function og(e,t){if(t){const{inlineSize:s,blockSize:n}=t[0];return{width:s,height:n}}else return e instanceof SVGElement&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight}}function ag({target:e,contentRect:t,borderBoxSize:s}){var n;(n=ks.get(e))===null||n===void 0||n.forEach(r=>{r({target:e,contentSize:t,get size(){return og(e,s)}})})}function lg(e){e.forEach(ag)}function cg(){typeof ResizeObserver>"u"||(Ie=new ResizeObserver(lg))}function ug(e,t){Ie||cg();const s=Ir(e);return s.forEach(n=>{let r=ks.get(n);r||(r=new Set,ks.set(n,r)),r.add(t),Ie==null||Ie.observe(n)}),()=>{s.forEach(n=>{const r=ks.get(n);r==null||r.delete(t),r!=null&&r.size||Ie==null||Ie.unobserve(n)})}}const Ms=new Set;let ns;function dg(){ns=()=>{const e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};Ms.forEach(s=>s(t))},window.addEventListener("resize",ns)}function hg(e){return Ms.add(e),ns||dg(),()=>{Ms.delete(e),!Ms.size&&ns&&(ns=void 0)}}function fg(e,t){return typeof e=="function"?hg(e):ug(e,t)}const pg=50,No=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),mg=()=>({time:0,x:No(),y:No()}),yg={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Do(e,t,s,n){const r=s[t],{length:o,position:i}=yg[t],a=r.current,l=s.time;r.current=e[`scroll${i}`],r.scrollLength=e[`scroll${o}`]-e[`client${o}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=ht(0,r.scrollLength,r.current);const c=n-l;r.velocity=c>pg?0:jr(r.current-a,c)}function gg(e,t,s){Do(e,"x",t,s),Do(e,"y",t,s),t.time=s}function xg(e,t){const s={x:0,y:0};let n=e;for(;n&&n!==t;)if(n instanceof HTMLElement)s.x+=n.offsetLeft,s.y+=n.offsetTop,n=n.offsetParent;else if(n.tagName==="svg"){const r=n.getBoundingClientRect();n=n.parentElement;const o=n.getBoundingClientRect();s.x+=r.left-o.left,s.y+=r.top-o.top}else if(n instanceof SVGGraphicsElement){const{x:r,y:o}=n.getBBox();s.x+=r,s.y+=o;let i=null,a=n.parentNode;for(;!i;)a.tagName==="svg"&&(i=a),a=n.parentNode;n=i}else break;return s}const vg={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},sr={start:0,center:.5,end:1};function Vo(e,t,s=0){let n=0;if(e in sr&&(e=sr[e]),typeof e=="string"){const r=parseFloat(e);e.endsWith("px")?n=r:e.endsWith("%")?e=r/100:e.endsWith("vw")?n=r/100*document.documentElement.clientWidth:e.endsWith("vh")?n=r/100*document.documentElement.clientHeight:e=r}return typeof e=="number"&&(n=t*e),s+n}const bg=[0,0];function wg(e,t,s,n){let r=Array.isArray(e)?e:bg,o=0,i=0;return typeof e=="number"?r=[e,e]:typeof e=="string"&&(e=e.trim(),e.includes(" ")?r=e.split(" "):r=[e,sr[e]?e:"0"]),o=Vo(r[0],s,n),i=Vo(r[1],t),o-i}const Tg={x:0,y:0};function Pg(e){return"getBBox"in e&&e.tagName!=="svg"?e.getBBox():{width:e.clientWidth,height:e.clientHeight}}function Sg(e,t,s){const{offset:n=vg.All}=s,{target:r=e,axis:o="y"}=s,i=o==="y"?"height":"width",a=r!==e?xg(r,e):Tg,l=r===e?{width:e.scrollWidth,height:e.scrollHeight}:Pg(r),c={width:e.clientWidth,height:e.clientHeight};t[o].offset.length=0;let u=!t[o].interpolate;const h=n.length;for(let f=0;f<h;f++){const y=wg(n[f],c[i],l[i],a[o]);!u&&y!==t[o].interpolatorOffsets[f]&&(u=!0),t[o].offset[f]=y}u&&(t[o].interpolate=Rr(t[o].offset,Bl(n)),t[o].interpolatorOffsets=[...t[o].offset]),t[o].progress=t[o].interpolate(t[o].current)}function Cg(e,t=e,s){if(s.x.targetOffset=0,s.y.targetOffset=0,t!==e){let n=t;for(;n&&n!==e;)s.x.targetOffset+=n.offsetLeft,s.y.targetOffset+=n.offsetTop,n=n.offsetParent}s.x.targetLength=t===e?t.scrollWidth:t.clientWidth,s.y.targetLength=t===e?t.scrollHeight:t.clientHeight,s.x.containerLength=e.clientWidth,s.y.containerLength=e.clientHeight}function jg(e,t,s,n={}){return{measure:()=>Cg(e,n.target,s),update:r=>{gg(e,s,r),(n.offset||n.target)&&Sg(e,s,n)},notify:()=>t(s)}}const $t=new WeakMap,Oo=new WeakMap,vn=new WeakMap,Lo=e=>e===document.documentElement?window:e;function qr(e,{container:t=document.documentElement,...s}={}){let n=vn.get(t);n||(n=new Set,vn.set(t,n));const r=mg(),o=jg(t,e,r,s);if(n.add(o),!$t.has(t)){const a=()=>{for(const f of n)f.measure()},l=()=>{for(const f of n)f.update($.timestamp)},c=()=>{for(const f of n)f.notify()},u=()=>{L.read(a,!1,!0),L.read(l,!1,!0),L.update(c,!1,!0)};$t.set(t,u);const h=Lo(t);window.addEventListener("resize",u,{passive:!0}),t!==document.documentElement&&Oo.set(t,fg(t,u)),h.addEventListener("scroll",u,{passive:!0})}const i=$t.get(t);return L.read(i,!1,!0),()=>{var a;me(i);const l=vn.get(t);if(!l||(l.delete(o),l.size))return;const c=$t.get(t);$t.delete(t),c&&(Lo(t).removeEventListener("scroll",c),(a=Oo.get(t))===null||a===void 0||a(),window.removeEventListener("resize",c))}}function Nc(e,t){let s;const n=()=>{const{currentTime:r}=t,i=(r===null?0:r.value)/100;s!==i&&e(i),s=i};return L.update(n,!0),()=>me(n)}function Ag({source:e,container:t,axis:s="y"}){e&&(t=e);const n={value:0},r=qr(o=>{n.value=o[s].progress*100},{container:t,axis:s});return{currentTime:n,cancel:r}}const bn=new Map;function Dc({source:e,container:t=document.documentElement,axis:s="y"}={}){e&&(t=e),bn.has(t)||bn.set(t,{});const n=bn.get(t);return n[s]||(n[s]=Hl()?new ScrollTimeline({source:t,axis:s}):Ag({source:t,axis:s})),n[s]}function Eg(e){return e.length===2}function Vc(e){return e&&(e.target||e.offset)}function Rg(e,t){return Eg(e)||Vc(t)?qr(s=>{e(s[t.axis].progress,s)},t):Nc(e,Dc(t))}function kg(e,t){if(e.flatten(),Vc(t))return e.pause(),qr(s=>{e.time=e.duration*s[t.axis].progress},t);{const s=Dc(t);return e.attachTimeline?e.attachTimeline(s,n=>(n.pause(),Nc(r=>{n.time=n.duration*r},s))):Q}}function Mg(e,{axis:t="y",...s}={}){const n={axis:t,...s};return typeof e=="function"?Rg(e,n):kg(e,n)}function Io(e,t){Hh(!!(!t||t.current))}const Ng=()=>({scrollX:be(0),scrollY:be(0),scrollXProgress:be(0),scrollYProgress:be(0)});function Dg({container:e,target:t,layoutEffect:s=!0,...n}={}){const r=Ht(Ng);return(s?Ys:m.useEffect)(()=>(Io("target",t),Io("container",e),Mg((i,{x:a,y:l})=>{r.scrollX.set(a.current),r.scrollXProgress.set(a.progress),r.scrollY.set(l.current),r.scrollYProgress.set(l.progress)},{...n,container:(e==null?void 0:e.current)||void 0,target:(t==null?void 0:t.current)||void 0})),[e,t,JSON.stringify(n.offset)]),r}const Vg={some:0,all:1};function Og(e,t,{root:s,margin:n,amount:r="some"}={}){const o=Ir(e),i=new WeakMap,a=c=>{c.forEach(u=>{const h=i.get(u.target);if(u.isIntersecting!==!!h)if(u.isIntersecting){const f=t(u);typeof f=="function"?i.set(u.target,f):l.unobserve(u.target)}else h&&(h(u),i.delete(u.target))})},l=new IntersectionObserver(a,{root:s,rootMargin:n,threshold:typeof r=="number"?r:Vg[r]});return o.forEach(c=>l.observe(c)),()=>l.disconnect()}function ys(e,{root:t,margin:s,amount:n,once:r=!1}={}){const[o,i]=m.useState(!1);return m.useEffect(()=>{if(!e.current||r&&o)return;const a=()=>(i(!0),r?void 0:()=>i(!1)),l={root:t&&t.current||void 0,margin:s,amount:n};return Og(e.current,a,l)},[t,e,s,r,n]),o}const Lg="/assets/BullBuster_1752063257019-D-NwXa2E.png";function Ig(){const[e,t]=m.useState(!1),[s,n]=m.useState(!1),[r,o]=m.useState("home");m.useEffect(()=>{const l=()=>{t(window.scrollY>50);const c=["home","about","menu","gallery","track","contact"],u=window.scrollY+100;for(const h of c){const f=document.getElementById(h);if(f){const{offsetTop:y,offsetHeight:x}=f;if(u>=y&&u<y+x){o(h);break}}}};return window.addEventListener("scroll",l),()=>window.removeEventListener("scroll",l)},[]);const i=l=>{const c=document.getElementById(l);c&&(c.scrollIntoView({behavior:"smooth",block:"start"}),n(!1))},a=[{id:"home",label:"Home"},{id:"about",label:"About"},{id:"menu",label:"Menu"},{id:"gallery",label:"Gallery"},{id:"track",label:"Track Order"},{id:"contact",label:"Contact"}];return d.jsxs(d.Fragment,{children:[d.jsx(P.div,{initial:{y:-50,opacity:0},animate:{y:0,opacity:1},className:`fixed top-0 w-full z-50 bg-brand-black text-white py-2 text-sm transition-all duration-300 ${e?"transform -translate-y-full":""}`,children:d.jsxs("div",{className:"max-w-7xl mx-auto px-6 flex justify-between items-center",children:[d.jsxs("div",{className:"flex items-center space-x-6",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(La,{className:"w-4 h-4 text-brand-yellow"}),d.jsx("span",{children:"+92 300 1234567"})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Oa,{className:"w-4 h-4 text-brand-yellow"}),d.jsx("span",{children:"Lahore, Pakistan"})]})]}),d.jsx("div",{className:"hidden md:block",children:d.jsx("span",{className:"text-brand-yellow font-semibold",children:"Free Delivery on Orders Above Rs. 1000"})})]})}),d.jsx(P.nav,{initial:{y:-100},animate:{y:0},className:`fixed w-full z-40 transition-all duration-500 ${e?"top-0 glass-navbar shadow-2xl":"top-10 bg-transparent"}`,children:d.jsx("div",{className:"max-w-7xl mx-auto px-6 py-4",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs(P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-3",children:[d.jsxs(P.div,{whileHover:{rotate:360},transition:{duration:.6},className:"relative",children:[d.jsx("img",{src:Lg,alt:"BullBuster Logo",className:"w-12 h-12 object-contain"}),d.jsx("div",{className:"absolute inset-0 bg-brand-yellow/20 rounded-full blur-lg"})]}),d.jsxs("div",{children:[d.jsx("span",{className:"text-2xl font-black gradient-text",children:"BullBuster"}),d.jsx("div",{className:"text-xs text-brand-yellow font-medium",children:"Premium Fast Food"})]})]}),d.jsx("div",{className:"hidden lg:flex items-center space-x-1",children:a.map(l=>d.jsxs(P.button,{onClick:()=>i(l.id),className:`relative px-4 py-2 rounded-lg font-medium transition-all duration-300 ${r===l.id?"text-brand-yellow":"text-white hover:text-brand-yellow"}`,whileHover:{scale:1.05},whileTap:{scale:.95},children:[l.label,r===l.id&&d.jsx(P.div,{layoutId:"activeSection",className:"absolute bottom-0 left-0 right-0 h-0.5 bg-brand-yellow rounded-full",initial:!1,transition:{type:"spring",stiffness:380,damping:30}})]},l.id))}),d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsxs(P.button,{whileHover:{scale:1.05,boxShadow:"0 0 25px rgba(255, 193, 7, 0.4)"},whileTap:{scale:.95},onClick:()=>i("menu"),className:"hidden md:flex items-center space-x-2 bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-6 py-3 rounded-full font-bold hover:shadow-lg transition-all duration-300",children:[d.jsx(jd,{className:"w-4 h-4"}),d.jsx("span",{children:"Order Now"})]}),d.jsx(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"lg:hidden relative z-50 p-2 rounded-lg bg-brand-yellow/10 backdrop-blur-sm border border-brand-yellow/20",onClick:()=>n(!s),children:d.jsx(P.div,{animate:s?{rotate:180}:{rotate:0},transition:{duration:.3},children:s?d.jsx(Fa,{className:"w-6 h-6 text-brand-yellow"}):d.jsx(wd,{className:"w-6 h-6 text-brand-yellow"})})})]})]})})}),d.jsx(eg,{children:s&&d.jsxs(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-30 lg:hidden",children:[d.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/80 backdrop-blur-sm",onClick:()=>n(!1)}),d.jsx(P.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"spring",damping:25,stiffness:200},className:"absolute right-0 top-0 h-full w-80 bg-brand-black-soft/95 backdrop-blur-xl border-l border-brand-yellow/20 shadow-2xl",children:d.jsx("div",{className:"p-8 pt-24",children:d.jsxs("div",{className:"space-y-6",children:[a.map((l,c)=>d.jsx(P.button,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{delay:c*.1},onClick:()=>i(l.id),className:`block w-full text-left py-3 px-4 rounded-lg font-medium transition-all duration-300 ${r===l.id?"bg-brand-yellow text-brand-black":"text-white hover:bg-brand-yellow/10 hover:text-brand-yellow"}`,children:l.label},l.id)),d.jsx(P.button,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},onClick:()=>i("menu"),className:"w-full bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black py-4 rounded-xl font-bold mt-8 hover:shadow-lg transition-all duration-300",children:"Order Now"})]})})})]})})]})}function Fg(){const e=m.useRef(null),{scrollYProgress:t}=Dg({target:e,offset:["start start","end start"]}),s=ko(t,[0,1],["0%","50%"]),n=ko(t,[0,1],[1,0]),r=o=>{const i=document.getElementById(o);i&&i.scrollIntoView({behavior:"smooth",block:"start"})};return d.jsxs("section",{ref:e,id:"home",className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[d.jsxs(P.div,{style:{y:s},className:"absolute inset-0 parallax-bg",children:[d.jsx("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080')"}}),d.jsx("div",{className:"absolute inset-0 hero-overlay"}),d.jsx(P.div,{animate:{background:["radial-gradient(circle at 20% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 50%)","radial-gradient(circle at 80% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 50%)","radial-gradient(circle at 20% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 50%)"]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute inset-0"})]}),d.jsx(P.div,{animate:{y:[0,-30,0],rotate:[0,5,-5,0],scale:[1,1.1,1]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute top-20 left-10 text-6xl opacity-20",children:"🍕"}),d.jsx(P.div,{animate:{y:[0,-25,0],rotate:[0,-5,5,0],scale:[1,1.05,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:-2},className:"absolute top-40 right-20 text-5xl opacity-25",children:"🍗"}),d.jsx(P.div,{animate:{y:[0,-35,0],rotate:[0,10,-10,0],scale:[1,1.15,1]},transition:{duration:9,repeat:1/0,ease:"easeInOut",delay:-4},className:"absolute bottom-32 left-20 text-7xl opacity-30",children:"🍔"}),d.jsx(P.div,{animate:{rotate:360},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 right-1/4 w-32 h-32 border-2 border-brand-yellow/10 rounded-full"}),d.jsx(P.div,{animate:{rotate:-360},transition:{duration:25,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 left-1/4 w-24 h-24 border-2 border-brand-yellow/15 rounded-lg"}),d.jsxs(P.div,{style:{opacity:n},className:"relative z-10 text-center text-white px-6 max-w-7xl mx-auto",children:[d.jsxs(P.div,{initial:{y:-30,opacity:0},animate:{y:0,opacity:1},transition:{duration:.6,delay:.2},className:"flex justify-center items-center space-x-8 mb-8 text-sm",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Ad,{className:"w-4 h-4 text-brand-yellow"}),d.jsx("span",{children:"4.9/5 Rating"})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Da,{className:"w-4 h-4 text-brand-yellow"}),d.jsx("span",{children:"Best in Lahore"})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Va,{className:"w-4 h-4 text-brand-yellow"}),d.jsx("span",{children:"15 Min Delivery"})]})]}),d.jsxs(P.h1,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{duration:.8,ease:"easeOut",delay:.4},className:"text-6xl md:text-8xl lg:text-9xl font-black mb-6 leading-none",children:[d.jsx("span",{className:"gradient-text",children:"BULL"}),d.jsx("span",{className:"text-white",children:"BUSTER"})]}),d.jsxs(P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.6},className:"mb-8",children:[d.jsxs("p",{className:"text-xl md:text-2xl lg:text-3xl font-light max-w-4xl mx-auto leading-relaxed",children:["Experience the"," ",d.jsx("span",{className:"text-brand-yellow font-semibold",children:"finest fast food"})," ","in Lahore."]}),d.jsx("p",{className:"text-lg md:text-xl text-gray-300 mt-4 max-w-3xl mx-auto",children:"Premium burgers, legendary taste, unforgettable moments."})]}),d.jsxs(P.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5,ease:"easeOut",delay:.8},className:"flex flex-col sm:flex-row gap-6 justify-center items-center",children:[d.jsxs(P.button,{whileHover:{scale:1.05,boxShadow:"0 20px 40px rgba(255, 193, 7, 0.3)",y:-5},whileTap:{scale:.95},onClick:()=>r("menu"),className:"group bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-10 py-5 rounded-2xl text-lg font-bold transition-all duration-300 shadow-2xl flex items-center gap-3 hover-glow",children:[d.jsx(Ia,{className:"w-6 h-6 group-hover:rotate-12 transition-transform duration-300"}),d.jsx("span",{children:"Explore Menu"})]}),d.jsxs(P.button,{whileHover:{scale:1.05,backgroundColor:"rgba(255, 193, 7, 1)",color:"rgba(0, 0, 0, 1)",y:-5},whileTap:{scale:.95},onClick:()=>r("about"),className:"group border-2 border-brand-yellow text-brand-yellow px-10 py-5 rounded-2xl text-lg font-bold transition-all duration-300 flex items-center gap-3 backdrop-blur-sm bg-white/5",children:[d.jsx(Td,{className:"w-6 h-6 group-hover:scale-110 transition-transform duration-300"}),d.jsx("span",{children:"Our Story"})]})]})]}),d.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2,duration:.6},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer group",onClick:()=>r("about"),children:d.jsxs(P.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"flex flex-col items-center space-y-2",children:[d.jsx("span",{className:"text-brand-yellow text-sm font-medium opacity-80 group-hover:opacity-100 transition-opacity",children:"Scroll Down"}),d.jsx(P.div,{whileHover:{scale:1.2},className:"p-3 rounded-full border-2 border-brand-yellow/30 group-hover:border-brand-yellow/60 transition-colors",children:d.jsx(md,{className:"text-brand-yellow text-xl"})})]})})]})}function Bg(){const e=m.useRef(null),t=ys(e,{once:!0,amount:.3});return d.jsx("section",{id:"about",ref:e,className:"py-20 bg-brand-black relative overflow-hidden",children:d.jsx("div",{className:"max-w-7xl mx-auto px-6",children:d.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[d.jsxs(P.div,{initial:{x:-50,opacity:0},animate:t?{x:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut"},children:[d.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Our Story"}),d.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Crafting Perfection Since Day One"}),d.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-8",children:"Born in the heart of Lahore, BullBuster represents a revolution in fast food excellence. We combine traditional Pakistani hospitality with international culinary standards to create an unforgettable dining experience."}),d.jsxs("div",{className:"grid grid-cols-2 gap-8 mb-8",children:[d.jsxs(P.div,{initial:{scale:.8,opacity:0},animate:t?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.2},className:"text-center",children:[d.jsx("div",{className:"text-3xl font-black text-brand-yellow mb-2",children:"50K+"}),d.jsx("div",{className:"text-gray-300 font-medium",children:"Happy Customers"})]}),d.jsxs(P.div,{initial:{scale:.8,opacity:0},animate:t?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.4},className:"text-center",children:[d.jsx("div",{className:"text-3xl font-black text-brand-yellow mb-2",children:"24/7"}),d.jsx("div",{className:"text-gray-300 font-medium",children:"Service Available"})]})]}),d.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300",children:"Learn More About Us"})]}),d.jsxs(P.div,{initial:{x:50,opacity:0},animate:t?{x:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut",delay:.3},className:"relative",children:[d.jsx("img",{src:"https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",alt:"Luxury restaurant interior with warm ambiance",className:"rounded-2xl shadow-2xl w-full h-96 object-cover"}),d.jsx(P.div,{initial:{scale:0,rotate:-180},animate:t?{scale:1,rotate:0}:{},transition:{duration:.6,delay:.6},className:"absolute -top-8 -left-8 w-24 h-24 bg-brand-yellow rounded-full flex items-center justify-center shadow-lg",children:d.jsx(Da,{className:"text-brand-black text-2xl"})})]})]})})})}function Ug(){const e=m.useRef(null),t=ys(e,{once:!0,amount:.2}),[s,n]=m.useState("All"),{data:r=[],isLoading:o,error:i}=Aa({queryKey:["/api/menu"],retry:3,staleTime:5*60*1e3}),a=["All",...Array.from(new Set(r.map(u=>u.category)))],l=s==="All"?r:r.filter(u=>u.category===s),c=u=>`Rs. ${(u/100).toFixed(0)}`;return o?d.jsx("section",{className:"py-20 bg-brand-dark-gray",children:d.jsx("div",{className:"max-w-7xl mx-auto px-6",children:d.jsxs("div",{className:"text-center",children:[d.jsx(P.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"w-16 h-16 border-4 border-brand-yellow/20 border-t-brand-yellow rounded-full mx-auto"}),d.jsx("p",{className:"mt-6 text-gray-300 text-lg",children:"Loading delicious menu..."})]})})}):i?d.jsx("section",{className:"py-20 bg-brand-dark-gray",children:d.jsx("div",{className:"max-w-7xl mx-auto px-6",children:d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-red-400 text-6xl mb-4",children:"😞"}),d.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"Oops! Something went wrong"}),d.jsx("p",{className:"text-gray-300 mb-6",children:"We couldn't load our delicious menu right now."}),d.jsx("button",{onClick:()=>window.location.reload(),className:"bg-brand-yellow text-brand-black px-6 py-3 rounded-lg font-semibold hover:bg-brand-yellow-light transition-colors",children:"Try Again"})]})})}):d.jsx("section",{id:"menu",ref:e,className:"py-20 bg-brand-dark-gray",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[d.jsxs(P.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[d.jsx(P.span,{initial:{scale:.8,opacity:0},animate:t?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.2},className:"inline-block text-brand-yellow font-bold text-lg uppercase tracking-wider bg-brand-yellow/10 px-6 py-2 rounded-full border border-brand-yellow/20",children:"Our Menu"}),d.jsxs(P.h2,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.3},className:"text-5xl md:text-6xl font-black text-white mt-6 mb-6",children:[d.jsx("span",{className:"gradient-text",children:"Delicious"})," Selections"]}),d.jsxs(P.p,{initial:{y:20,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.4},className:"text-gray-300 text-lg md:text-xl max-w-3xl mx-auto leading-relaxed",children:["From signature burgers to crispy delights, every bite tells a story of quality and passion.",d.jsxs("span",{className:"text-brand-yellow font-semibold",children:[" ","Crafted with love, served with pride."]})]})]}),d.jsx(P.div,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.5},className:"flex justify-center mb-16",children:d.jsx("div",{className:"flex flex-wrap gap-3 bg-brand-black-soft/80 backdrop-blur-xl rounded-2xl p-3 shadow-2xl border border-brand-yellow/20 glow-effect",children:a.map((u,h)=>d.jsxs(P.button,{initial:{opacity:0,scale:.8},animate:t?{opacity:1,scale:1}:{},transition:{duration:.3,delay:.6+h*.1},whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},onClick:()=>n(u),className:`relative px-6 py-3 rounded-xl font-bold transition-all duration-300 ${s===u?"bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black shadow-lg":"hover:bg-brand-yellow/10 text-white hover:text-brand-yellow"}`,children:[u,s===u&&d.jsx(P.div,{layoutId:"activeFilter",className:"absolute inset-0 bg-gradient-to-r from-brand-yellow to-brand-yellow-light rounded-xl -z-10",initial:!1,transition:{type:"spring",stiffness:380,damping:30}})]},u))})}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:l.map((u,h)=>d.jsxs(P.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.1*h},whileHover:{y:-12,scale:1.02,boxShadow:"0 25px 50px rgba(255, 193, 7, 0.15)"},className:"group bg-brand-black-soft/60 backdrop-blur-xl border border-brand-yellow/20 rounded-3xl shadow-xl overflow-hidden hover:border-brand-yellow/40 transition-all duration-500 hover-glow",children:[d.jsxs("div",{className:"relative overflow-hidden rounded-t-3xl",children:[d.jsx(P.img,{src:u.image,alt:u.name,className:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110",whileHover:{scale:1.1}}),d.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),!u.available&&d.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},className:"absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center",children:d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-4xl mb-2",children:"😔"}),d.jsx("span",{className:"text-white font-bold text-lg",children:"Currently Unavailable"})]})}),u.available&&d.jsx(P.div,{initial:{opacity:0,y:20},whileHover:{opacity:1,y:0},className:"absolute top-4 right-4 bg-brand-yellow text-brand-black px-3 py-1 rounded-full text-sm font-bold",children:"Available"})]}),d.jsxs("div",{className:"p-6",children:[d.jsx(P.h3,{className:"text-xl font-black text-white mb-3 group-hover:text-brand-yellow transition-colors duration-300",children:u.name}),d.jsx("p",{className:"text-gray-300 mb-6 leading-relaxed",children:u.description}),d.jsxs("div",{className:"flex justify-between items-center",children:[d.jsx(P.span,{className:"text-3xl font-black gradient-text",whileHover:{scale:1.1},children:c(u.price)}),d.jsxs(P.button,{whileHover:{scale:1.1,boxShadow:"0 10px 20px rgba(255, 193, 7, 0.3)"},whileTap:{scale:.9},disabled:!u.available,className:"bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-6 py-3 rounded-full font-bold hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",children:[d.jsx(Pd,{className:"w-5 h-5"}),d.jsx("span",{children:"Add"})]})]})]})]},u.id))}),d.jsx(P.div,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.5},className:"text-center mt-12",children:d.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300",children:"View Full Menu"})})]})})}const _g=[{url:"https://images.unsplash.com/photo-1512152272829-e3139592d56f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Artistic burger presentation with professional plating"},{url:"https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Modern restaurant interior with sleek design"},{url:"https://images.unsplash.com/photo-1551782450-17144efb9c50?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Gourmet burger with dramatic lighting and composition"},{url:"https://images.unsplash.com/photo-1565299507177-b0ac66763828?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Artistic food plating with professional garnish"},{url:"https://images.unsplash.com/photo-1544148103-0773bf10d330?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Luxury restaurant ambiance with elegant lighting"},{url:"https://images.unsplash.com/photo-1586190848861-99aa4a171e90?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=500",alt:"Premium burger presentation with professional photography"}];function zg(){const e=m.useRef(null),t=ys(e,{once:!0,amount:.2});return d.jsx("section",{id:"gallery",ref:e,className:"py-20 bg-brand-black",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[d.jsxs(P.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[d.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Gallery"}),d.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Visual Feast"}),d.jsx("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"Every dish is a masterpiece, every moment is memorable."})]}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:_g.map((s,n)=>d.jsxs(P.div,{initial:{y:50,opacity:0,scale:.9},animate:t?{y:0,opacity:1,scale:1}:{},transition:{duration:.6,delay:.1*n},whileHover:{scale:1.05},className:"relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer group",children:[d.jsx("img",{src:s.url,alt:s.alt,className:"w-full h-80 object-cover transition-transform duration-300 group-hover:scale-110"}),d.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center",children:d.jsx(Ln,{className:"text-white text-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"})})]},n))})]})})}const wn=[{id:"confirmed",label:"Order Confirmed",description:"Your order has been received and confirmed",icon:pd},{id:"preparing",label:"Preparing",description:"Our chefs are preparing your delicious meal",icon:Ia},{id:"out_for_delivery",label:"Out for Delivery",description:"Your order is on its way to you",icon:fd},{id:"delivered",label:"Delivered",description:"Order delivered successfully",icon:xd}];function Hg(){const e=m.useRef(null),t=ys(e,{once:!0,amount:.2}),[s,n]=m.useState(""),[r,o]=m.useState(""),{data:i,isLoading:a,error:l}=Aa({queryKey:["/api/orders",r],enabled:!!r}),c=()=>{s.trim()&&o(s.trim())},u=f=>{if(!i)return"pending";const y=wn.findIndex(g=>g.id===i.status),x=wn.findIndex(g=>g.id===f);return x<y?"completed":x===y?"active":"pending"},h=f=>new Date(f).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0});return d.jsx("section",{id:"track",ref:e,className:"py-20 bg-brand-dark-gray",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[d.jsxs(P.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[d.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Track Your Order"}),d.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Real-Time Updates"}),d.jsx("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"Stay informed with live tracking and precise delivery estimates."})]}),d.jsxs("div",{className:"max-w-4xl mx-auto",children:[d.jsx(P.div,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.2},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl p-8 shadow-lg mb-12",children:d.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[d.jsx("input",{type:"text",placeholder:"Enter your order ID (e.g., BB123456789)",value:s,onChange:f=>n(f.target.value),className:"flex-1 px-6 py-4 border border-brand-yellow/30 bg-black/20 text-white placeholder-gray-400 rounded-full focus:border-brand-yellow focus:outline-none transition-colors duration-300",onKeyPress:f=>f.key==="Enter"&&c()}),d.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:c,disabled:a,className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 flex items-center gap-2 disabled:opacity-50",children:[d.jsx(Ln,{className:"w-5 h-5"}),a?"Searching...":"Track Order"]})]})}),l&&d.jsx(P.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl p-8 shadow-lg text-center",children:d.jsxs("div",{className:"text-red-500 mb-4",children:[d.jsx(Ln,{className:"w-12 h-12 mx-auto mb-2"}),d.jsx("h3",{className:"text-xl font-bold text-white",children:"Order Not Found"}),d.jsx("p",{className:"text-gray-300 mt-2",children:"Please check your order number and try again."})]})}),i&&d.jsxs(P.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},transition:{duration:.6},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl p-8 shadow-lg",children:[d.jsxs("div",{className:"mb-8",children:[d.jsxs("h3",{className:"text-2xl font-black text-white mb-2",children:["Order #",i.orderNumber]}),d.jsxs("p",{className:"text-gray-300",children:["Customer: ",i.customerName]}),d.jsxs("p",{className:"text-gray-300",children:["Total: Rs. ",(i.total/100).toFixed(0)]}),d.jsx("p",{className:"text-gray-300",children:"Estimated delivery: 25-30 minutes"})]}),d.jsx("div",{className:"space-y-6",children:wn.map((f,y)=>{const x=u(f.id),g=f.icon;return d.jsxs(P.div,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{duration:.4,delay:.1*y},className:"flex items-center space-x-4",children:[d.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center font-bold transition-all duration-300 ${x==="completed"?"bg-brand-yellow text-brand-black":x==="active"?"bg-brand-yellow text-brand-black animate-pulse":"bg-gray-200 text-gray-400"}`,children:d.jsx(g,{className:"w-6 h-6"})}),d.jsxs("div",{className:"flex-1",children:[d.jsx("h4",{className:`font-semibold ${x==="pending"?"text-gray-500":"text-white"}`,children:f.label}),d.jsx("p",{className:`text-sm ${x==="pending"?"text-gray-500":"text-gray-300"}`,children:f.description}),x!=="pending"&&d.jsx("span",{className:"text-xs text-gray-400",children:x==="active"?"In Progress":h(i.updatedAt)})]})]},f.id)})})]})]})]})})}function Kg(){const e=m.useRef(null),t=ys(e,{once:!0,amount:.2}),{toast:s}=Ra();ur();const[n,r]=m.useState({name:"",email:"",subject:"",message:""}),o=ed({mutationFn:async l=>(await td("POST","/api/contact",l)).json(),onSuccess:()=>{s({title:"Message Sent!",description:"Thank you for contacting us. We'll get back to you soon."}),r({name:"",email:"",subject:"",message:""})},onError:()=>{s({title:"Error",description:"Failed to send message. Please try again.",variant:"destructive"})}}),i=l=>{l.preventDefault(),o.mutate(n)},a=l=>{r(c=>({...c,[l.target.name]:l.target.value}))};return d.jsxs("section",{id:"contact",ref:e,className:"py-20 bg-brand-black text-white relative overflow-hidden",children:[d.jsxs("div",{className:"absolute inset-0 opacity-10",children:[d.jsx("div",{className:"text-9xl absolute top-20 left-20",children:"🍔"}),d.jsx("div",{className:"text-7xl absolute bottom-32 right-32",children:"🍕"})]}),d.jsxs("div",{className:"max-w-7xl mx-auto px-6 relative z-10",children:[d.jsxs(P.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[d.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Contact Us"}),d.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Get In Touch"}),d.jsx("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"Visit our locations or reach out to us for any inquiries."})]}),d.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16",children:[d.jsxs(P.div,{initial:{x:-50,opacity:0},animate:t?{x:0,opacity:1}:{},transition:{duration:.6,delay:.2},className:"space-y-8",children:[d.jsxs("div",{className:"flex items-start space-x-4",children:[d.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:d.jsx(Oa,{className:"text-brand-black"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Main Location"}),d.jsx("p",{className:"text-gray-300",children:"MM Alam Road, Gulberg III, Lahore, Pakistan"})]})]}),d.jsxs("div",{className:"flex items-start space-x-4",children:[d.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:d.jsx(La,{className:"text-brand-black"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Phone"}),d.jsx("p",{className:"text-gray-300",children:"+92 42 1234 5678"})]})]}),d.jsxs("div",{className:"flex items-start space-x-4",children:[d.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:d.jsx(Va,{className:"text-brand-black"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Hours"}),d.jsx("p",{className:"text-gray-300",children:"Open 24/7 for your convenience"})]})]}),d.jsxs("div",{className:"flex items-start space-x-4",children:[d.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:d.jsx(bd,{className:"text-brand-black"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Email"}),d.jsx("p",{className:"text-gray-300",children:"<EMAIL>"})]})]})]}),d.jsxs(P.div,{initial:{x:50,opacity:0},animate:t?{x:0,opacity:1}:{},transition:{duration:.6,delay:.4},className:"glass-effect rounded-2xl p-8",children:[d.jsx("h3",{className:"text-2xl font-bold text-white mb-6",children:"Send us a Message"}),d.jsxs("form",{onSubmit:i,className:"space-y-6",children:[d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[d.jsx("input",{type:"text",name:"name",placeholder:"Your Name",value:n.name,onChange:a,required:!0,className:"bg-white/20 border border-white/30 rounded-full px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),d.jsx("input",{type:"email",name:"email",placeholder:"Your Email",value:n.email,onChange:a,required:!0,className:"bg-white/20 border border-white/30 rounded-full px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"})]}),d.jsx("input",{type:"text",name:"subject",placeholder:"Subject",value:n.subject,onChange:a,required:!0,className:"w-full bg-white/20 border border-white/30 rounded-full px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),d.jsx("textarea",{name:"message",placeholder:"Your Message",rows:5,value:n.message,onChange:a,required:!0,className:"w-full bg-white/20 border border-white/30 rounded-2xl px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300 resize-none"}),d.jsx(P.button,{type:"submit",disabled:o.isPending,whileHover:{scale:1.05},whileTap:{scale:.95},className:"w-full bg-brand-yellow text-brand-black py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 disabled:opacity-50",children:o.isPending?"Sending...":"Send Message"})]})]})]})]})]})}function Wg(){const[e,t]=m.useState(""),s=r=>{r.preventDefault(),console.log("Newsletter subscription:",e),t("")},n=r=>{const o=document.getElementById(r);o&&o.scrollIntoView({behavior:"smooth",block:"start"})};return d.jsx("footer",{className:"bg-brand-black-soft text-white py-16",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[d.jsxs(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6},viewport:{once:!0},children:[d.jsxs("div",{className:"flex items-center space-x-2 mb-6",children:[d.jsx(Sd,{className:"text-3xl text-brand-yellow"}),d.jsx("span",{className:"text-2xl font-black text-white",children:"BullBuster"})]}),d.jsx("p",{className:"text-gray-400 mb-6",children:"Premium fast food experience in the heart of Lahore, serving quality meals with passion and excellence."}),d.jsxs("div",{className:"flex space-x-4",children:[d.jsx(P.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:d.jsx(gd,{className:"w-5 h-5"})}),d.jsx(P.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:d.jsx(vd,{className:"w-5 h-5"})}),d.jsx(P.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:d.jsx(Ed,{className:"w-5 h-5"})})]})]}),d.jsxs(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.1},viewport:{once:!0},children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Quick Links"}),d.jsx("ul",{className:"space-y-3",children:["home","about","menu","gallery","contact"].map(r=>d.jsx("li",{children:d.jsx("button",{onClick:()=>n(r),className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300 capitalize",children:r==="home"?"Home":r==="about"?"About Us":r.charAt(0).toUpperCase()+r.slice(1)})},r))})]}),d.jsxs(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Services"}),d.jsxs("ul",{className:"space-y-3",children:[d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Dine In"})}),d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Takeaway"})}),d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Home Delivery"})}),d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Catering"})}),d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Party Orders"})})]})]}),d.jsxs(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.3},viewport:{once:!0},children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Newsletter"}),d.jsx("p",{className:"text-gray-400 mb-4",children:"Subscribe to get special offers and updates."}),d.jsxs("form",{onSubmit:s,className:"flex",children:[d.jsx("input",{type:"email",placeholder:"Your email",value:e,onChange:r=>t(r.target.value),required:!0,className:"flex-1 bg-white/20 border border-white/30 rounded-l-full px-4 py-3 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),d.jsx(P.button,{type:"submit",whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-6 py-3 rounded-r-full hover:bg-brand-yellow-light transition-colors duration-300",children:d.jsx(Cd,{className:"w-5 h-5"})})]})]})]}),d.jsx(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"border-t border-gray-700 pt-8 text-center",children:d.jsx("p",{className:"text-gray-400",children:"© 2024 BullBsuter. All rights reserved. | Privacy Policy | Terms of Service"})})]})})}function Gg(){return d.jsxs("div",{className:"min-h-screen bg-brand-black",children:[d.jsx(Ig,{}),d.jsx(Fg,{}),d.jsx(Bg,{}),d.jsx(Ug,{}),d.jsx(zg,{}),d.jsx(Hg,{}),d.jsx(Kg,{}),d.jsx(Wg,{})]})}function qg(){return d.jsxs(Tu,{children:[d.jsx(ti,{path:"/",component:Gg}),d.jsx(ti,{component:Vh})]})}function $g(){return d.jsx(zu,{client:nd,children:d.jsxs(Eh,{children:[d.jsx(uh,{}),d.jsx(qg,{})]})})}sa(document.getElementById("root")).render(d.jsx($g,{}));
