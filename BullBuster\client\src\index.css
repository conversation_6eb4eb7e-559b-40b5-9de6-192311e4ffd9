@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(0, 0%, 100%);
  --muted: hsl(0, 0%, 15%);
  --muted-foreground: hsl(0, 0%, 70%);
  --popover: hsl(0, 0%, 8%);
  --popover-foreground: hsl(0, 0%, 100%);
  --card: hsl(0, 0%, 8%);
  --card-foreground: hsl(0, 0%, 100%);
  --border: hsl(0, 0%, 20%);
  --input: hsl(0, 0%, 15%);
  --primary: hsl(51, 100%, 50%);
  --primary-foreground: hsl(0, 0%, 0%);
  --secondary: hsl(0, 0%, 15%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --accent: hsl(0, 0%, 15%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --ring: hsl(51, 100%, 50%);
  --radius: 0.5rem;
  
  /* Enhanced Brand Colors */
  --brand-yellow: hsl(45, 100%, 55%);
  --brand-yellow-light: hsl(48, 100%, 65%);
  --brand-yellow-dark: hsl(42, 100%, 45%);
  --brand-yellow-glow: hsl(45, 100%, 55%, 0.3);
  --brand-black: hsl(0, 0%, 0%);
  --brand-black-soft: hsl(0, 0%, 8%);
  --brand-black-lighter: hsl(0, 0%, 12%);
  --brand-dark-gray: hsl(0, 0%, 3%);
  --brand-gray: hsl(0, 0%, 8%);
  --brand-gray-light: hsl(0, 0%, 12%);
  --brand-accent: hsl(30, 100%, 60%);
  --brand-accent-dark: hsl(25, 100%, 50%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(51, 100%, 50%);
  --primary-foreground: hsl(0, 0%, 0%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes slideUp {
  0% { transform: translateY(50px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes scaleUp {
  0% { transform: scale(0.95); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}

.animate-fade-in {
  animation: fadeIn 1s ease-out;
}

.animate-scale-up {
  animation: scaleUp 0.5s ease-out;
}

.parallax-bg {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.hero-overlay {
  background: linear-gradient(135deg, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.6) 50%, rgba(255,193,7,0.15) 100%);
}

.glass-effect {
  backdrop-filter: blur(16px);
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 193, 7, 0.3);
  box-shadow: 0 8px 32px rgba(255, 193, 7, 0.1);
}

.glass-navbar {
  backdrop-filter: blur(24px);
  background: rgba(0, 0, 0, 0.9);
  border-bottom: 1px solid rgba(255, 193, 7, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.gradient-text {
  background: linear-gradient(135deg, hsl(45, 100%, 55%) 0%, hsl(48, 100%, 65%) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-effect {
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.3), 0 0 40px rgba(255, 193, 7, 0.1);
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(255, 193, 7, 0.4), 0 0 60px rgba(255, 193, 7, 0.2);
  transform: translateY(-2px);
}

/* Text Colors */
.text-brand-yellow {
  color: hsl(45, 100%, 55%);
}

.text-brand-yellow-light {
  color: hsl(48, 100%, 65%);
}

.text-brand-yellow-dark {
  color: hsl(42, 100%, 45%);
}

.text-brand-black {
  color: hsl(0, 0%, 0%);
}

.text-brand-black-soft {
  color: hsl(0, 0%, 8%);
}

.text-brand-gray {
  color: hsl(0, 0%, 70%);
}

/* Background Colors */
.bg-brand-yellow {
  background-color: hsl(45, 100%, 55%);
}

.bg-brand-yellow-light {
  background-color: hsl(48, 100%, 65%);
}

.bg-brand-yellow-dark {
  background-color: hsl(42, 100%, 45%);
}

.bg-brand-black {
  background-color: hsl(0, 0%, 0%);
}

.bg-brand-black-soft {
  background-color: hsl(0, 0%, 8%);
}

.bg-brand-black-lighter {
  background-color: hsl(0, 0%, 12%);
}

.bg-brand-dark-gray {
  background-color: hsl(0, 0%, 6%);
}

.bg-brand-gray {
  background-color: hsl(0, 0%, 15%);
}

.bg-brand-gray-light {
  background-color: hsl(0, 0%, 25%);
}

/* Border Colors */
.border-brand-yellow {
  border-color: hsl(45, 100%, 55%);
}

.border-brand-yellow-light {
  border-color: hsl(48, 100%, 65%);
}

/* Modern Button Styles */
.btn-primary {
  @apply bg-brand-yellow text-brand-black font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:bg-brand-yellow-light hover:shadow-lg hover:scale-105 active:scale-95;
}

.btn-secondary {
  @apply border-2 border-brand-yellow text-brand-yellow font-bold py-3 px-6 rounded-xl transition-all duration-300 hover:bg-brand-yellow hover:text-brand-black hover:shadow-lg hover:scale-105 active:scale-95;
}

.btn-ghost {
  @apply text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 hover:bg-white/10 hover:text-brand-yellow;
}
