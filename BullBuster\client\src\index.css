@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(0, 0%, 100%);
  --muted: hsl(0, 0%, 15%);
  --muted-foreground: hsl(0, 0%, 70%);
  --popover: hsl(0, 0%, 8%);
  --popover-foreground: hsl(0, 0%, 100%);
  --card: hsl(0, 0%, 8%);
  --card-foreground: hsl(0, 0%, 100%);
  --border: hsl(0, 0%, 20%);
  --input: hsl(0, 0%, 15%);
  --primary: hsl(51, 100%, 50%);
  --primary-foreground: hsl(0, 0%, 0%);
  --secondary: hsl(0, 0%, 15%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --accent: hsl(0, 0%, 15%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --ring: hsl(51, 100%, 50%);
  --radius: 0.5rem;
  
  /* Brand Colors */
  --brand-yellow: hsl(51, 100%, 50%);
  --brand-yellow-light: hsl(60, 100%, 61%);
  --brand-black: hsl(0, 0%, 0%);
  --brand-black-soft: hsl(0, 0%, 11%);
  --brand-dark-gray: hsl(0, 0%, 8%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(51, 100%, 50%);
  --primary-foreground: hsl(0, 0%, 0%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes slideUp {
  0% { transform: translateY(50px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes scaleUp {
  0% { transform: scale(0.95); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}

.animate-fade-in {
  animation: fadeIn 1s ease-out;
}

.animate-scale-up {
  animation: scaleUp 0.5s ease-out;
}

.parallax-bg {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.hero-overlay {
  background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(255,215,0,0.1) 100%);
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.glass-navbar {
  backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 215, 0, 0.1);
}

.brand-yellow {
  color: hsl(51, 100%, 50%);
}

.brand-yellow-light {
  color: hsl(60, 100%, 61%);
}

.brand-black {
  color: hsl(0, 0%, 0%);
}

.brand-black-soft {
  color: hsl(0, 0%, 11%);
}

.bg-brand-yellow {
  background-color: hsl(51, 100%, 50%);
}

.bg-brand-yellow-light {
  background-color: hsl(60, 100%, 61%);
}

.bg-brand-black {
  background-color: hsl(0, 0%, 0%);
}

.bg-brand-black-soft {
  background-color: hsl(0, 0%, 11%);
}

.bg-brand-dark-gray {
  background-color: hsl(0, 0%, 8%);
}
