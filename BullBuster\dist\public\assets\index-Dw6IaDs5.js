var Gr=t=>{throw TypeError(t)};var Ys=(t,e,s)=>e.has(t)||Gr("Cannot "+s);var p=(t,e,s)=>(Ys(t,e,"read from private field"),s?s.call(t):e.get(t)),k=(t,e,s)=>e.has(t)?Gr("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),S=(t,e,s,n)=>(Ys(t,e,"write to private field"),n?n.call(t,s):e.set(t,s),s),D=(t,e,s)=>(Ys(t,e,"access private method"),s);var gs=(t,e,s,n)=>({set _(r){S(t,e,r,s)},get _(){return p(t,e,n)}});import{j as d,V as Qo,R as Yo,A as Xo,C as Zo,T as Jo,D as ea,P as Vc,c as Oc,a as ta,u as sa,b as Lc,d as Ic,e as mt,f as Fc,g as Bc,h as Uc,i as _c,k as zc,l as Hc}from"./ui-7t35Orsm.js";import{a as Kc,r as g,c as Wc}from"./vendor-CX2mysxk.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))n(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function s(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(r){if(r.ep)return;r.ep=!0;const o=s(r);fetch(r.href,o)}})();var na,qr=Kc;na=qr.createRoot,qr.hydrateRoot;function Gc(t,e){if(t instanceof RegExp)return{keys:!1,pattern:t};var s,n,r,o,i=[],a="",l=t.split("/");for(l[0]||l.shift();r=l.shift();)s=r[0],s==="*"?(i.push(s),a+=r[1]==="?"?"(?:/(.*))?":"/(.*)"):s===":"?(n=r.indexOf("?",1),o=r.indexOf(".",1),i.push(r.substring(1,~n?n:~o?o:r.length)),a+=~n&&!~o?"(?:/([^/]+?))?":"/([^/]+?)",~o&&(a+=(~n?"?":"")+"\\"+r.substring(o))):a+="/"+r;return{keys:i,pattern:new RegExp("^"+a+(e?"(?=$|/)":"/?$"),"i")}}var ra={exports:{}},ia={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lt=g;function qc(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var $c=typeof Object.is=="function"?Object.is:qc,Qc=Lt.useState,Yc=Lt.useEffect,Xc=Lt.useLayoutEffect,Zc=Lt.useDebugValue;function Jc(t,e){var s=e(),n=Qc({inst:{value:s,getSnapshot:e}}),r=n[0].inst,o=n[1];return Xc(function(){r.value=s,r.getSnapshot=e,Xs(r)&&o({inst:r})},[t,s,e]),Yc(function(){return Xs(r)&&o({inst:r}),t(function(){Xs(r)&&o({inst:r})})},[t]),Zc(s),s}function Xs(t){var e=t.getSnapshot;t=t.value;try{var s=e();return!$c(t,s)}catch{return!0}}function eu(t,e){return e()}var tu=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?eu:Jc;ia.useSyncExternalStore=Lt.useSyncExternalStore!==void 0?Lt.useSyncExternalStore:tu;ra.exports=ia;var su=ra.exports;const nu=Wc.useInsertionEffect,ru=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",iu=ru?g.useLayoutEffect:g.useEffect,ou=nu||iu,oa=t=>{const e=g.useRef([t,(...s)=>e[0](...s)]).current;return ou(()=>{e[0]=t}),e[1]},au="popstate",Jn="pushState",er="replaceState",lu="hashchange",$r=[au,Jn,er,lu],cu=t=>{for(const e of $r)addEventListener(e,t);return()=>{for(const e of $r)removeEventListener(e,t)}},aa=(t,e)=>su.useSyncExternalStore(cu,t,e),uu=()=>location.search,du=({ssrSearch:t=""}={})=>aa(uu,()=>t),Qr=()=>location.pathname,hu=({ssrPath:t}={})=>aa(Qr,t?()=>t:Qr),fu=(t,{replace:e=!1,state:s=null}={})=>history[e?er:Jn](s,"",t),pu=(t={})=>[hu(t),fu],Yr=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Yr]>"u"){for(const t of[Jn,er]){const e=history[t];history[t]=function(){const s=e.apply(this,arguments),n=new Event(t);return n.arguments=arguments,dispatchEvent(n),s}}Object.defineProperty(window,Yr,{value:!0})}const mu=(t,e)=>e.toLowerCase().indexOf(t.toLowerCase())?"~"+e:e.slice(t.length)||"/",la=(t="")=>t==="/"?"":t,yu=(t,e)=>t[0]==="~"?t.slice(1):la(e)+t,gu=(t="",e)=>mu(Xr(la(t)),Xr(e)),Xr=t=>{try{return decodeURI(t)}catch{return t}},ca={hook:pu,searchHook:du,parser:Gc,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:t=>t},ua=g.createContext(ca),Us=()=>g.useContext(ua),da={},ha=g.createContext(da),xu=()=>g.useContext(ha),tr=t=>{const[e,s]=t.hook(t);return[gu(t.base,e),oa((n,r)=>s(yu(n,t.base),r))]},fa=(t,e,s,n)=>{const{pattern:r,keys:o}=e instanceof RegExp?{keys:!1,pattern:e}:t(e||"*",n),i=r.exec(s)||[],[a,...l]=i;return a!==void 0?[!0,(()=>{const c=o!==!1?Object.fromEntries(o.map((h,f)=>[h,l[f]])):i.groups;let u={...l};return c&&Object.assign(u,c),u})(),...n?[a]:[]]:[!1,null]},vu=({children:t,...e})=>{var u,h;const s=Us(),n=e.hook?ca:s;let r=n;const[o,i]=((u=e.ssrPath)==null?void 0:u.split("?"))??[];i&&(e.ssrSearch=i,e.ssrPath=o),e.hrefs=e.hrefs??((h=e.hook)==null?void 0:h.hrefs);let a=g.useRef({}),l=a.current,c=l;for(let f in n){const m=f==="base"?n[f]+(e[f]||""):e[f]||n[f];l===c&&m!==c[f]&&(a.current=c={...c}),c[f]=m,m!==n[f]&&(r=c)}return g.createElement(ua.Provider,{value:r,children:t})},Zr=({children:t,component:e},s)=>e?g.createElement(e,{params:s}):typeof t=="function"?t(s):t,bu=t=>{let e=g.useRef(da),s=e.current;for(const n in t)t[n]!==s[n]&&(s=t);return Object.keys(t).length===0&&(s=t),e.current=s},Jr=({path:t,nest:e,match:s,...n})=>{const r=Us(),[o]=tr(r),[i,a,l]=s??fa(r.parser,t,o,e),c=bu({...xu(),...a});if(!i)return null;const u=l?g.createElement(vu,{base:l},Zr(n,c)):Zr(n,c);return g.createElement(ha.Provider,{value:c,children:u})};g.forwardRef((t,e)=>{const s=Us(),[n,r]=tr(s),{to:o="",href:i=o,onClick:a,asChild:l,children:c,className:u,replace:h,state:f,...m}=t,v=oa(x=>{x.ctrlKey||x.metaKey||x.altKey||x.shiftKey||x.button!==0||(a==null||a(x),x.defaultPrevented||(x.preventDefault(),r(i,t)))}),y=s.hrefs(i[0]==="~"?i.slice(1):s.base+i,s);return l&&g.isValidElement(c)?g.cloneElement(c,{onClick:v,href:y}):g.createElement("a",{...m,onClick:v,href:y,className:u!=null&&u.call?u(n===i):u,children:c,ref:e})});const pa=t=>Array.isArray(t)?t.flatMap(e=>pa(e&&e.type===g.Fragment?e.props.children:e)):[t],wu=({children:t,location:e})=>{const s=Us(),[n]=tr(s);for(const r of pa(t)){let o=0;if(g.isValidElement(r)&&(o=fa(s.parser,r.props.path,e||n,r.props.nest))[0])return g.cloneElement(r,{match:o})}return null};var Ut=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},ut=typeof window>"u"||"Deno"in globalThis;function de(){}function Tu(t,e){return typeof t=="function"?t(e):t}function vn(t){return typeof t=="number"&&t>=0&&t!==1/0}function ma(t,e){return Math.max(t+(e||0)-Date.now(),0)}function wt(t,e){return typeof t=="function"?t(e):t}function pe(t,e){return typeof t=="function"?t(e):t}function ei(t,e){const{type:s="all",exact:n,fetchStatus:r,predicate:o,queryKey:i,stale:a}=t;if(i){if(n){if(e.queryHash!==sr(i,e.options))return!1}else if(!ns(e.queryKey,i))return!1}if(s!=="all"){const l=e.isActive();if(s==="active"&&!l||s==="inactive"&&l)return!1}return!(typeof a=="boolean"&&e.isStale()!==a||r&&r!==e.state.fetchStatus||o&&!o(e))}function ti(t,e){const{exact:s,status:n,predicate:r,mutationKey:o}=t;if(o){if(!e.options.mutationKey)return!1;if(s){if(dt(e.options.mutationKey)!==dt(o))return!1}else if(!ns(e.options.mutationKey,o))return!1}return!(n&&e.state.status!==n||r&&!r(e))}function sr(t,e){return((e==null?void 0:e.queryKeyHashFn)||dt)(t)}function dt(t){return JSON.stringify(t,(e,s)=>bn(s)?Object.keys(s).sort().reduce((n,r)=>(n[r]=s[r],n),{}):s)}function ns(t,e){return t===e?!0:typeof t!=typeof e?!1:t&&e&&typeof t=="object"&&typeof e=="object"?!Object.keys(e).some(s=>!ns(t[s],e[s])):!1}function ya(t,e){if(t===e)return t;const s=si(t)&&si(e);if(s||bn(t)&&bn(e)){const n=s?t:Object.keys(t),r=n.length,o=s?e:Object.keys(e),i=o.length,a=s?[]:{};let l=0;for(let c=0;c<i;c++){const u=s?c:o[c];(!s&&n.includes(u)||s)&&t[u]===void 0&&e[u]===void 0?(a[u]=void 0,l++):(a[u]=ya(t[u],e[u]),a[u]===t[u]&&t[u]!==void 0&&l++)}return r===i&&l===r?t:a}return e}function Ms(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function si(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function bn(t){if(!ni(t))return!1;const e=t.constructor;if(e===void 0)return!0;const s=e.prototype;return!(!ni(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function ni(t){return Object.prototype.toString.call(t)==="[object Object]"}function Pu(t){return new Promise(e=>{setTimeout(e,t)})}function wn(t,e,s){return typeof s.structuralSharing=="function"?s.structuralSharing(t,e):s.structuralSharing!==!1?ya(t,e):e}function Su(t,e,s=0){const n=[...t,e];return s&&n.length>s?n.slice(1):n}function Cu(t,e,s=0){const n=[e,...t];return s&&n.length>s?n.slice(0,-1):n}var nr=Symbol();function ga(t,e){return!t.queryFn&&(e!=null&&e.initialPromise)?()=>e.initialPromise:!t.queryFn||t.queryFn===nr?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}var st,Fe,Pt,Bo,ju=(Bo=class extends Ut{constructor(){super();k(this,st);k(this,Fe);k(this,Pt);S(this,Pt,e=>{if(!ut&&window.addEventListener){const s=()=>e();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){p(this,Fe)||this.setEventListener(p(this,Pt))}onUnsubscribe(){var e;this.hasListeners()||((e=p(this,Fe))==null||e.call(this),S(this,Fe,void 0))}setEventListener(e){var s;S(this,Pt,e),(s=p(this,Fe))==null||s.call(this),S(this,Fe,e(n=>{typeof n=="boolean"?this.setFocused(n):this.onFocus()}))}setFocused(e){p(this,st)!==e&&(S(this,st,e),this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach(s=>{s(e)})}isFocused(){var e;return typeof p(this,st)=="boolean"?p(this,st):((e=globalThis.document)==null?void 0:e.visibilityState)!=="hidden"}},st=new WeakMap,Fe=new WeakMap,Pt=new WeakMap,Bo),rr=new ju,St,Be,Ct,Uo,Au=(Uo=class extends Ut{constructor(){super();k(this,St,!0);k(this,Be);k(this,Ct);S(this,Ct,e=>{if(!ut&&window.addEventListener){const s=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",n)}}})}onSubscribe(){p(this,Be)||this.setEventListener(p(this,Ct))}onUnsubscribe(){var e;this.hasListeners()||((e=p(this,Be))==null||e.call(this),S(this,Be,void 0))}setEventListener(e){var s;S(this,Ct,e),(s=p(this,Be))==null||s.call(this),S(this,Be,e(this.setOnline.bind(this)))}setOnline(e){p(this,St)!==e&&(S(this,St,e),this.listeners.forEach(n=>{n(e)}))}isOnline(){return p(this,St)}},St=new WeakMap,Be=new WeakMap,Ct=new WeakMap,Uo),Ns=new Au;function Tn(){let t,e;const s=new Promise((r,o)=>{t=r,e=o});s.status="pending",s.catch(()=>{});function n(r){Object.assign(s,r),delete s.resolve,delete s.reject}return s.resolve=r=>{n({status:"fulfilled",value:r}),t(r)},s.reject=r=>{n({status:"rejected",reason:r}),e(r)},s}function ku(t){return Math.min(1e3*2**t,3e4)}function xa(t){return(t??"online")==="online"?Ns.isOnline():!0}var va=class extends Error{constructor(t){super("CancelledError"),this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent}};function Zs(t){return t instanceof va}function ba(t){let e=!1,s=0,n=!1,r;const o=Tn(),i=y=>{var x;n||(f(new va(y)),(x=t.abort)==null||x.call(t))},a=()=>{e=!0},l=()=>{e=!1},c=()=>rr.isFocused()&&(t.networkMode==="always"||Ns.isOnline())&&t.canRun(),u=()=>xa(t.networkMode)&&t.canRun(),h=y=>{var x;n||(n=!0,(x=t.onSuccess)==null||x.call(t,y),r==null||r(),o.resolve(y))},f=y=>{var x;n||(n=!0,(x=t.onError)==null||x.call(t,y),r==null||r(),o.reject(y))},m=()=>new Promise(y=>{var x;r=b=>{(n||c())&&y(b)},(x=t.onPause)==null||x.call(t)}).then(()=>{var y;r=void 0,n||(y=t.onContinue)==null||y.call(t)}),v=()=>{if(n)return;let y;const x=s===0?t.initialPromise:void 0;try{y=x??t.fn()}catch(b){y=Promise.reject(b)}Promise.resolve(y).then(h).catch(b=>{var M;if(n)return;const w=t.retry??(ut?0:3),T=t.retryDelay??ku,A=typeof T=="function"?T(s,b):T,C=w===!0||typeof w=="number"&&s<w||typeof w=="function"&&w(s,b);if(e||!C){f(b);return}s++,(M=t.onFail)==null||M.call(t,s,b),Pu(A).then(()=>c()?void 0:m()).then(()=>{e?f(b):v()})})};return{promise:o,cancel:i,continue:()=>(r==null||r(),o),cancelRetry:a,continueRetry:l,canStart:u,start:()=>(u()?v():m().then(v),o)}}function Eu(){let t=[],e=0,s=a=>{a()},n=a=>{a()},r=a=>setTimeout(a,0);const o=a=>{e?t.push(a):r(()=>{s(a)})},i=()=>{const a=t;t=[],a.length&&r(()=>{n(()=>{a.forEach(l=>{s(l)})})})};return{batch:a=>{let l;e++;try{l=a()}finally{e--,e||i()}return l},batchCalls:a=>(...l)=>{o(()=>{a(...l)})},schedule:o,setNotifyFunction:a=>{s=a},setBatchNotifyFunction:a=>{n=a},setScheduler:a=>{r=a}}}var G=Eu(),nt,_o,wa=(_o=class{constructor(){k(this,nt)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),vn(this.gcTime)&&S(this,nt,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(ut?1/0:5*60*1e3))}clearGcTimeout(){p(this,nt)&&(clearTimeout(p(this,nt)),S(this,nt,void 0))}},nt=new WeakMap,_o),jt,At,ue,X,as,rt,fe,je,zo,Ru=(zo=class extends wa{constructor(e){super();k(this,fe);k(this,jt);k(this,At);k(this,ue);k(this,X);k(this,as);k(this,rt);S(this,rt,!1),S(this,as,e.defaultOptions),this.setOptions(e.options),this.observers=[],S(this,ue,e.cache),this.queryKey=e.queryKey,this.queryHash=e.queryHash,S(this,jt,Mu(this.options)),this.state=e.state??p(this,jt),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var e;return(e=p(this,X))==null?void 0:e.promise}setOptions(e){this.options={...p(this,as),...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&p(this,ue).remove(this)}setData(e,s){const n=wn(this.state.data,e,this.options);return D(this,fe,je).call(this,{data:n,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),n}setState(e,s){D(this,fe,je).call(this,{type:"setState",state:e,setStateOptions:s})}cancel(e){var n,r;const s=(n=p(this,X))==null?void 0:n.promise;return(r=p(this,X))==null||r.cancel(e),s?s.then(de).catch(de):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(p(this,jt))}isActive(){return this.observers.some(e=>pe(e.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===nr||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(e=0){return this.state.isInvalidated||this.state.data===void 0||!ma(this.state.dataUpdatedAt,e)}onFocus(){var s;const e=this.observers.find(n=>n.shouldFetchOnWindowFocus());e==null||e.refetch({cancelRefetch:!1}),(s=p(this,X))==null||s.continue()}onOnline(){var s;const e=this.observers.find(n=>n.shouldFetchOnReconnect());e==null||e.refetch({cancelRefetch:!1}),(s=p(this,X))==null||s.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),p(this,ue).notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(s=>s!==e),this.observers.length||(p(this,X)&&(p(this,rt)?p(this,X).cancel({revert:!0}):p(this,X).cancelRetry()),this.scheduleGc()),p(this,ue).notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||D(this,fe,je).call(this,{type:"invalidate"})}fetch(e,s){var l,c,u;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(p(this,X))return p(this,X).continueRetry(),p(this,X).promise}if(e&&this.setOptions(e),!this.options.queryFn){const h=this.observers.find(f=>f.options.queryFn);h&&this.setOptions(h.options)}const n=new AbortController,r=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(S(this,rt,!0),n.signal)})},o=()=>{const h=ga(this.options,s),f={queryKey:this.queryKey,meta:this.meta};return r(f),S(this,rt,!1),this.options.persister?this.options.persister(h,f,this):h(f)},i={fetchOptions:s,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:o};r(i),(l=this.options.behavior)==null||l.onFetch(i,this),S(this,At,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=i.fetchOptions)==null?void 0:c.meta))&&D(this,fe,je).call(this,{type:"fetch",meta:(u=i.fetchOptions)==null?void 0:u.meta});const a=h=>{var f,m,v,y;Zs(h)&&h.silent||D(this,fe,je).call(this,{type:"error",error:h}),Zs(h)||((m=(f=p(this,ue).config).onError)==null||m.call(f,h,this),(y=(v=p(this,ue).config).onSettled)==null||y.call(v,this.state.data,h,this)),this.scheduleGc()};return S(this,X,ba({initialPromise:s==null?void 0:s.initialPromise,fn:i.fetchFn,abort:n.abort.bind(n),onSuccess:h=>{var f,m,v,y;if(h===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(x){a(x);return}(m=(f=p(this,ue).config).onSuccess)==null||m.call(f,h,this),(y=(v=p(this,ue).config).onSettled)==null||y.call(v,h,this.state.error,this),this.scheduleGc()},onError:a,onFail:(h,f)=>{D(this,fe,je).call(this,{type:"failed",failureCount:h,error:f})},onPause:()=>{D(this,fe,je).call(this,{type:"pause"})},onContinue:()=>{D(this,fe,je).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),p(this,X).start()}},jt=new WeakMap,At=new WeakMap,ue=new WeakMap,X=new WeakMap,as=new WeakMap,rt=new WeakMap,fe=new WeakSet,je=function(e){const s=n=>{switch(e.type){case"failed":return{...n,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...n,fetchStatus:"paused"};case"continue":return{...n,fetchStatus:"fetching"};case"fetch":return{...n,...Ta(n.data,this.options),fetchMeta:e.meta??null};case"success":return{...n,data:e.data,dataUpdateCount:n.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return Zs(r)&&r.revert&&p(this,At)?{...p(this,At),fetchStatus:"idle"}:{...n,error:r,errorUpdateCount:n.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:n.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...n,isInvalidated:!0};case"setState":return{...n,...e.state}}};this.state=s(this.state),G.batch(()=>{this.observers.forEach(n=>{n.onQueryUpdate()}),p(this,ue).notify({query:this,type:"updated",action:e})})},zo);function Ta(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:xa(e.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function Mu(t){const e=typeof t.initialData=="function"?t.initialData():t.initialData,s=e!==void 0,n=s?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var ye,Ho,Nu=(Ho=class extends Ut{constructor(e={}){super();k(this,ye);this.config=e,S(this,ye,new Map)}build(e,s,n){const r=s.queryKey,o=s.queryHash??sr(r,s);let i=this.get(o);return i||(i=new Ru({cache:this,queryKey:r,queryHash:o,options:e.defaultQueryOptions(s),state:n,defaultOptions:e.getQueryDefaults(r)}),this.add(i)),i}add(e){p(this,ye).has(e.queryHash)||(p(this,ye).set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const s=p(this,ye).get(e.queryHash);s&&(e.destroy(),s===e&&p(this,ye).delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){G.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return p(this,ye).get(e)}getAll(){return[...p(this,ye).values()]}find(e){const s={exact:!0,...e};return this.getAll().find(n=>ei(s,n))}findAll(e={}){const s=this.getAll();return Object.keys(e).length>0?s.filter(n=>ei(e,n)):s}notify(e){G.batch(()=>{this.listeners.forEach(s=>{s(e)})})}onFocus(){G.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){G.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},ye=new WeakMap,Ho),ge,ee,it,xe,Oe,Ko,Du=(Ko=class extends wa{constructor(e){super();k(this,xe);k(this,ge);k(this,ee);k(this,it);this.mutationId=e.mutationId,S(this,ee,e.mutationCache),S(this,ge,[]),this.state=e.state||Pa(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){p(this,ge).includes(e)||(p(this,ge).push(e),this.clearGcTimeout(),p(this,ee).notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){S(this,ge,p(this,ge).filter(s=>s!==e)),this.scheduleGc(),p(this,ee).notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){p(this,ge).length||(this.state.status==="pending"?this.scheduleGc():p(this,ee).remove(this))}continue(){var e;return((e=p(this,it))==null?void 0:e.continue())??this.execute(this.state.variables)}async execute(e){var r,o,i,a,l,c,u,h,f,m,v,y,x,b,w,T,A,C,M,V;S(this,it,ba({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(j,R)=>{D(this,xe,Oe).call(this,{type:"failed",failureCount:j,error:R})},onPause:()=>{D(this,xe,Oe).call(this,{type:"pause"})},onContinue:()=>{D(this,xe,Oe).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>p(this,ee).canRun(this)}));const s=this.state.status==="pending",n=!p(this,it).canStart();try{if(!s){D(this,xe,Oe).call(this,{type:"pending",variables:e,isPaused:n}),await((o=(r=p(this,ee).config).onMutate)==null?void 0:o.call(r,e,this));const R=await((a=(i=this.options).onMutate)==null?void 0:a.call(i,e));R!==this.state.context&&D(this,xe,Oe).call(this,{type:"pending",context:R,variables:e,isPaused:n})}const j=await p(this,it).start();return await((c=(l=p(this,ee).config).onSuccess)==null?void 0:c.call(l,j,e,this.state.context,this)),await((h=(u=this.options).onSuccess)==null?void 0:h.call(u,j,e,this.state.context)),await((m=(f=p(this,ee).config).onSettled)==null?void 0:m.call(f,j,null,this.state.variables,this.state.context,this)),await((y=(v=this.options).onSettled)==null?void 0:y.call(v,j,null,e,this.state.context)),D(this,xe,Oe).call(this,{type:"success",data:j}),j}catch(j){try{throw await((b=(x=p(this,ee).config).onError)==null?void 0:b.call(x,j,e,this.state.context,this)),await((T=(w=this.options).onError)==null?void 0:T.call(w,j,e,this.state.context)),await((C=(A=p(this,ee).config).onSettled)==null?void 0:C.call(A,void 0,j,this.state.variables,this.state.context,this)),await((V=(M=this.options).onSettled)==null?void 0:V.call(M,void 0,j,e,this.state.context)),j}finally{D(this,xe,Oe).call(this,{type:"error",error:j})}}finally{p(this,ee).runNext(this)}}},ge=new WeakMap,ee=new WeakMap,it=new WeakMap,xe=new WeakSet,Oe=function(e){const s=n=>{switch(e.type){case"failed":return{...n,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...n,isPaused:!0};case"continue":return{...n,isPaused:!1};case"pending":return{...n,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...n,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...n,data:void 0,error:e.error,failureCount:n.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}};this.state=s(this.state),G.batch(()=>{p(this,ge).forEach(n=>{n.onMutationUpdate(e)}),p(this,ee).notify({mutation:this,type:"updated",action:e})})},Ko);function Pa(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var ie,ls,Wo,Vu=(Wo=class extends Ut{constructor(e={}){super();k(this,ie);k(this,ls);this.config=e,S(this,ie,new Map),S(this,ls,Date.now())}build(e,s,n){const r=new Du({mutationCache:this,mutationId:++gs(this,ls)._,options:e.defaultMutationOptions(s),state:n});return this.add(r),r}add(e){const s=xs(e),n=p(this,ie).get(s)??[];n.push(e),p(this,ie).set(s,n),this.notify({type:"added",mutation:e})}remove(e){var n;const s=xs(e);if(p(this,ie).has(s)){const r=(n=p(this,ie).get(s))==null?void 0:n.filter(o=>o!==e);r&&(r.length===0?p(this,ie).delete(s):p(this,ie).set(s,r))}this.notify({type:"removed",mutation:e})}canRun(e){var n;const s=(n=p(this,ie).get(xs(e)))==null?void 0:n.find(r=>r.state.status==="pending");return!s||s===e}runNext(e){var n;const s=(n=p(this,ie).get(xs(e)))==null?void 0:n.find(r=>r!==e&&r.state.isPaused);return(s==null?void 0:s.continue())??Promise.resolve()}clear(){G.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}getAll(){return[...p(this,ie).values()].flat()}find(e){const s={exact:!0,...e};return this.getAll().find(n=>ti(s,n))}findAll(e={}){return this.getAll().filter(s=>ti(e,s))}notify(e){G.batch(()=>{this.listeners.forEach(s=>{s(e)})})}resumePausedMutations(){const e=this.getAll().filter(s=>s.state.isPaused);return G.batch(()=>Promise.all(e.map(s=>s.continue().catch(de))))}},ie=new WeakMap,ls=new WeakMap,Wo);function xs(t){var e;return((e=t.options.scope)==null?void 0:e.id)??String(t.mutationId)}function ri(t){return{onFetch:(e,s)=>{var u,h,f,m,v;const n=e.options,r=(f=(h=(u=e.fetchOptions)==null?void 0:u.meta)==null?void 0:h.fetchMore)==null?void 0:f.direction,o=((m=e.state.data)==null?void 0:m.pages)||[],i=((v=e.state.data)==null?void 0:v.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const c=async()=>{let y=!1;const x=T=>{Object.defineProperty(T,"signal",{enumerable:!0,get:()=>(e.signal.aborted?y=!0:e.signal.addEventListener("abort",()=>{y=!0}),e.signal)})},b=ga(e.options,e.fetchOptions),w=async(T,A,C)=>{if(y)return Promise.reject();if(A==null&&T.pages.length)return Promise.resolve(T);const M={queryKey:e.queryKey,pageParam:A,direction:C?"backward":"forward",meta:e.options.meta};x(M);const V=await b(M),{maxPages:j}=e.options,R=C?Cu:Su;return{pages:R(T.pages,V,j),pageParams:R(T.pageParams,A,j)}};if(r&&o.length){const T=r==="backward",A=T?Ou:ii,C={pages:o,pageParams:i},M=A(n,C);a=await w(C,M,T)}else{const T=t??o.length;do{const A=l===0?i[0]??n.initialPageParam:ii(n,a);if(l>0&&A==null)break;a=await w(a,A),l++}while(l<T)}return a};e.options.persister?e.fetchFn=()=>{var y,x;return(x=(y=e.options).persister)==null?void 0:x.call(y,c,{queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s)}:e.fetchFn=c}}}function ii(t,{pages:e,pageParams:s}){const n=e.length-1;return e.length>0?t.getNextPageParam(e[n],e,s[n],s):void 0}function Ou(t,{pages:e,pageParams:s}){var n;return e.length>0?(n=t.getPreviousPageParam)==null?void 0:n.call(t,e[0],e,s[0],s):void 0}var H,Ue,_e,kt,Et,ze,Rt,Mt,Go,Lu=(Go=class{constructor(t={}){k(this,H);k(this,Ue);k(this,_e);k(this,kt);k(this,Et);k(this,ze);k(this,Rt);k(this,Mt);S(this,H,t.queryCache||new Nu),S(this,Ue,t.mutationCache||new Vu),S(this,_e,t.defaultOptions||{}),S(this,kt,new Map),S(this,Et,new Map),S(this,ze,0)}mount(){gs(this,ze)._++,p(this,ze)===1&&(S(this,Rt,rr.subscribe(async t=>{t&&(await this.resumePausedMutations(),p(this,H).onFocus())})),S(this,Mt,Ns.subscribe(async t=>{t&&(await this.resumePausedMutations(),p(this,H).onOnline())})))}unmount(){var t,e;gs(this,ze)._--,p(this,ze)===0&&((t=p(this,Rt))==null||t.call(this),S(this,Rt,void 0),(e=p(this,Mt))==null||e.call(this),S(this,Mt,void 0))}isFetching(t){return p(this,H).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return p(this,Ue).findAll({...t,status:"pending"}).length}getQueryData(t){var s;const e=this.defaultQueryOptions({queryKey:t});return(s=p(this,H).get(e.queryHash))==null?void 0:s.state.data}ensureQueryData(t){const e=this.getQueryData(t.queryKey);if(e===void 0)return this.fetchQuery(t);{const s=this.defaultQueryOptions(t),n=p(this,H).build(this,s);return t.revalidateIfStale&&n.isStaleByTime(wt(s.staleTime,n))&&this.prefetchQuery(s),Promise.resolve(e)}}getQueriesData(t){return p(this,H).findAll(t).map(({queryKey:e,state:s})=>{const n=s.data;return[e,n]})}setQueryData(t,e,s){const n=this.defaultQueryOptions({queryKey:t}),r=p(this,H).get(n.queryHash),o=r==null?void 0:r.state.data,i=Tu(e,o);if(i!==void 0)return p(this,H).build(this,n).setData(i,{...s,manual:!0})}setQueriesData(t,e,s){return G.batch(()=>p(this,H).findAll(t).map(({queryKey:n})=>[n,this.setQueryData(n,e,s)]))}getQueryState(t){var s;const e=this.defaultQueryOptions({queryKey:t});return(s=p(this,H).get(e.queryHash))==null?void 0:s.state}removeQueries(t){const e=p(this,H);G.batch(()=>{e.findAll(t).forEach(s=>{e.remove(s)})})}resetQueries(t,e){const s=p(this,H),n={type:"active",...t};return G.batch(()=>(s.findAll(t).forEach(r=>{r.reset()}),this.refetchQueries(n,e)))}cancelQueries(t={},e={}){const s={revert:!0,...e},n=G.batch(()=>p(this,H).findAll(t).map(r=>r.cancel(s)));return Promise.all(n).then(de).catch(de)}invalidateQueries(t={},e={}){return G.batch(()=>{if(p(this,H).findAll(t).forEach(n=>{n.invalidate()}),t.refetchType==="none")return Promise.resolve();const s={...t,type:t.refetchType??t.type??"active"};return this.refetchQueries(s,e)})}refetchQueries(t={},e){const s={...e,cancelRefetch:(e==null?void 0:e.cancelRefetch)??!0},n=G.batch(()=>p(this,H).findAll(t).filter(r=>!r.isDisabled()).map(r=>{let o=r.fetch(void 0,s);return s.throwOnError||(o=o.catch(de)),r.state.fetchStatus==="paused"?Promise.resolve():o}));return Promise.all(n).then(de)}fetchQuery(t){const e=this.defaultQueryOptions(t);e.retry===void 0&&(e.retry=!1);const s=p(this,H).build(this,e);return s.isStaleByTime(wt(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(de).catch(de)}fetchInfiniteQuery(t){return t.behavior=ri(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(de).catch(de)}ensureInfiniteQueryData(t){return t.behavior=ri(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return Ns.isOnline()?p(this,Ue).resumePausedMutations():Promise.resolve()}getQueryCache(){return p(this,H)}getMutationCache(){return p(this,Ue)}getDefaultOptions(){return p(this,_e)}setDefaultOptions(t){S(this,_e,t)}setQueryDefaults(t,e){p(this,kt).set(dt(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...p(this,kt).values()];let s={};return e.forEach(n=>{ns(t,n.queryKey)&&(s={...s,...n.defaultOptions})}),s}setMutationDefaults(t,e){p(this,Et).set(dt(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...p(this,Et).values()];let s={};return e.forEach(n=>{ns(t,n.mutationKey)&&(s={...s,...n.defaultOptions})}),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...p(this,_e).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=sr(e.queryKey,e)),e.refetchOnReconnect===void 0&&(e.refetchOnReconnect=e.networkMode!=="always"),e.throwOnError===void 0&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.enabled!==!0&&e.queryFn===nr&&(e.enabled=!1),e}defaultMutationOptions(t){return t!=null&&t._defaulted?t:{...p(this,_e).mutations,...(t==null?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){p(this,H).clear(),p(this,Ue).clear()}},H=new WeakMap,Ue=new WeakMap,_e=new WeakMap,kt=new WeakMap,Et=new WeakMap,ze=new WeakMap,Rt=new WeakMap,Mt=new WeakMap,Go),se,O,cs,te,ot,Nt,He,ve,us,Dt,Vt,at,lt,Ke,Ot,F,$t,Pn,Sn,Cn,jn,An,kn,En,Sa,qo,Iu=(qo=class extends Ut{constructor(e,s){super();k(this,F);k(this,se);k(this,O);k(this,cs);k(this,te);k(this,ot);k(this,Nt);k(this,He);k(this,ve);k(this,us);k(this,Dt);k(this,Vt);k(this,at);k(this,lt);k(this,Ke);k(this,Ot,new Set);this.options=s,S(this,se,e),S(this,ve,null),S(this,He,Tn()),this.options.experimental_prefetchInRender||p(this,He).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(p(this,O).addObserver(this),oi(p(this,O),this.options)?D(this,F,$t).call(this):this.updateResult(),D(this,F,jn).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Rn(p(this,O),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Rn(p(this,O),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,D(this,F,An).call(this),D(this,F,kn).call(this),p(this,O).removeObserver(this)}setOptions(e,s){const n=this.options,r=p(this,O);if(this.options=p(this,se).defaultQueryOptions(e),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof pe(this.options.enabled,p(this,O))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");D(this,F,En).call(this),p(this,O).setOptions(this.options),n._defaulted&&!Ms(this.options,n)&&p(this,se).getQueryCache().notify({type:"observerOptionsUpdated",query:p(this,O),observer:this});const o=this.hasListeners();o&&ai(p(this,O),r,this.options,n)&&D(this,F,$t).call(this),this.updateResult(s),o&&(p(this,O)!==r||pe(this.options.enabled,p(this,O))!==pe(n.enabled,p(this,O))||wt(this.options.staleTime,p(this,O))!==wt(n.staleTime,p(this,O)))&&D(this,F,Pn).call(this);const i=D(this,F,Sn).call(this);o&&(p(this,O)!==r||pe(this.options.enabled,p(this,O))!==pe(n.enabled,p(this,O))||i!==p(this,Ke))&&D(this,F,Cn).call(this,i)}getOptimisticResult(e){const s=p(this,se).getQueryCache().build(p(this,se),e),n=this.createResult(s,e);return Bu(this,n)&&(S(this,te,n),S(this,Nt,this.options),S(this,ot,p(this,O).state)),n}getCurrentResult(){return p(this,te)}trackResult(e,s){const n={};return Object.keys(e).forEach(r=>{Object.defineProperty(n,r,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(r),s==null||s(r),e[r])})}),n}trackProp(e){p(this,Ot).add(e)}getCurrentQuery(){return p(this,O)}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const s=p(this,se).defaultQueryOptions(e),n=p(this,se).getQueryCache().build(p(this,se),s);return n.fetch().then(()=>this.createResult(n,s))}fetch(e){return D(this,F,$t).call(this,{...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),p(this,te)))}createResult(e,s){var j;const n=p(this,O),r=this.options,o=p(this,te),i=p(this,ot),a=p(this,Nt),c=e!==n?e.state:p(this,cs),{state:u}=e;let h={...u},f=!1,m;if(s._optimisticResults){const R=this.hasListeners(),_=!R&&oi(e,s),Y=R&&ai(e,n,s,r);(_||Y)&&(h={...h,...Ta(u.data,e.options)}),s._optimisticResults==="isRestoring"&&(h.fetchStatus="idle")}let{error:v,errorUpdatedAt:y,status:x}=h;if(s.select&&h.data!==void 0)if(o&&h.data===(i==null?void 0:i.data)&&s.select===p(this,us))m=p(this,Dt);else try{S(this,us,s.select),m=s.select(h.data),m=wn(o==null?void 0:o.data,m,s),S(this,Dt,m),S(this,ve,null)}catch(R){S(this,ve,R)}else m=h.data;if(s.placeholderData!==void 0&&m===void 0&&x==="pending"){let R;if(o!=null&&o.isPlaceholderData&&s.placeholderData===(a==null?void 0:a.placeholderData))R=o.data;else if(R=typeof s.placeholderData=="function"?s.placeholderData((j=p(this,Vt))==null?void 0:j.state.data,p(this,Vt)):s.placeholderData,s.select&&R!==void 0)try{R=s.select(R),S(this,ve,null)}catch(_){S(this,ve,_)}R!==void 0&&(x="success",m=wn(o==null?void 0:o.data,R,s),f=!0)}p(this,ve)&&(v=p(this,ve),m=p(this,Dt),y=Date.now(),x="error");const b=h.fetchStatus==="fetching",w=x==="pending",T=x==="error",A=w&&b,C=m!==void 0,V={status:x,fetchStatus:h.fetchStatus,isPending:w,isSuccess:x==="success",isError:T,isInitialLoading:A,isLoading:A,data:m,dataUpdatedAt:h.dataUpdatedAt,error:v,errorUpdatedAt:y,failureCount:h.fetchFailureCount,failureReason:h.fetchFailureReason,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>c.dataUpdateCount||h.errorUpdateCount>c.errorUpdateCount,isFetching:b,isRefetching:b&&!w,isLoadingError:T&&!C,isPaused:h.fetchStatus==="paused",isPlaceholderData:f,isRefetchError:T&&C,isStale:ir(e,s),refetch:this.refetch,promise:p(this,He)};if(this.options.experimental_prefetchInRender){const R=I=>{V.status==="error"?I.reject(V.error):V.data!==void 0&&I.resolve(V.data)},_=()=>{const I=S(this,He,V.promise=Tn());R(I)},Y=p(this,He);switch(Y.status){case"pending":e.queryHash===n.queryHash&&R(Y);break;case"fulfilled":(V.status==="error"||V.data!==Y.value)&&_();break;case"rejected":(V.status!=="error"||V.error!==Y.reason)&&_();break}}return V}updateResult(e){const s=p(this,te),n=this.createResult(p(this,O),this.options);if(S(this,ot,p(this,O).state),S(this,Nt,this.options),p(this,ot).data!==void 0&&S(this,Vt,p(this,O)),Ms(n,s))return;S(this,te,n);const r={},o=()=>{if(!s)return!0;const{notifyOnChangeProps:i}=this.options,a=typeof i=="function"?i():i;if(a==="all"||!a&&!p(this,Ot).size)return!0;const l=new Set(a??p(this,Ot));return this.options.throwOnError&&l.add("error"),Object.keys(p(this,te)).some(c=>{const u=c;return p(this,te)[u]!==s[u]&&l.has(u)})};(e==null?void 0:e.listeners)!==!1&&o()&&(r.listeners=!0),D(this,F,Sa).call(this,{...r,...e})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&D(this,F,jn).call(this)}},se=new WeakMap,O=new WeakMap,cs=new WeakMap,te=new WeakMap,ot=new WeakMap,Nt=new WeakMap,He=new WeakMap,ve=new WeakMap,us=new WeakMap,Dt=new WeakMap,Vt=new WeakMap,at=new WeakMap,lt=new WeakMap,Ke=new WeakMap,Ot=new WeakMap,F=new WeakSet,$t=function(e){D(this,F,En).call(this);let s=p(this,O).fetch(this.options,e);return e!=null&&e.throwOnError||(s=s.catch(de)),s},Pn=function(){D(this,F,An).call(this);const e=wt(this.options.staleTime,p(this,O));if(ut||p(this,te).isStale||!vn(e))return;const n=ma(p(this,te).dataUpdatedAt,e)+1;S(this,at,setTimeout(()=>{p(this,te).isStale||this.updateResult()},n))},Sn=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(p(this,O)):this.options.refetchInterval)??!1},Cn=function(e){D(this,F,kn).call(this),S(this,Ke,e),!(ut||pe(this.options.enabled,p(this,O))===!1||!vn(p(this,Ke))||p(this,Ke)===0)&&S(this,lt,setInterval(()=>{(this.options.refetchIntervalInBackground||rr.isFocused())&&D(this,F,$t).call(this)},p(this,Ke)))},jn=function(){D(this,F,Pn).call(this),D(this,F,Cn).call(this,D(this,F,Sn).call(this))},An=function(){p(this,at)&&(clearTimeout(p(this,at)),S(this,at,void 0))},kn=function(){p(this,lt)&&(clearInterval(p(this,lt)),S(this,lt,void 0))},En=function(){const e=p(this,se).getQueryCache().build(p(this,se),this.options);if(e===p(this,O))return;const s=p(this,O);S(this,O,e),S(this,cs,e.state),this.hasListeners()&&(s==null||s.removeObserver(this),e.addObserver(this))},Sa=function(e){G.batch(()=>{e.listeners&&this.listeners.forEach(s=>{s(p(this,te))}),p(this,se).getQueryCache().notify({query:p(this,O),type:"observerResultsUpdated"})})},qo);function Fu(t,e){return pe(e.enabled,t)!==!1&&t.state.data===void 0&&!(t.state.status==="error"&&e.retryOnMount===!1)}function oi(t,e){return Fu(t,e)||t.state.data!==void 0&&Rn(t,e,e.refetchOnMount)}function Rn(t,e,s){if(pe(e.enabled,t)!==!1){const n=typeof s=="function"?s(t):s;return n==="always"||n!==!1&&ir(t,e)}return!1}function ai(t,e,s,n){return(t!==e||pe(n.enabled,t)===!1)&&(!s.suspense||t.state.status!=="error")&&ir(t,s)}function ir(t,e){return pe(e.enabled,t)!==!1&&t.isStaleByTime(wt(e.staleTime,t))}function Bu(t,e){return!Ms(t.getCurrentResult(),e)}var We,Ge,ne,Ae,Me,Ts,Mn,$o,Uu=($o=class extends Ut{constructor(s,n){super();k(this,Me);k(this,We);k(this,Ge);k(this,ne);k(this,Ae);S(this,We,s),this.setOptions(n),this.bindMethods(),D(this,Me,Ts).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(s){var r;const n=this.options;this.options=p(this,We).defaultMutationOptions(s),Ms(this.options,n)||p(this,We).getMutationCache().notify({type:"observerOptionsUpdated",mutation:p(this,ne),observer:this}),n!=null&&n.mutationKey&&this.options.mutationKey&&dt(n.mutationKey)!==dt(this.options.mutationKey)?this.reset():((r=p(this,ne))==null?void 0:r.state.status)==="pending"&&p(this,ne).setOptions(this.options)}onUnsubscribe(){var s;this.hasListeners()||(s=p(this,ne))==null||s.removeObserver(this)}onMutationUpdate(s){D(this,Me,Ts).call(this),D(this,Me,Mn).call(this,s)}getCurrentResult(){return p(this,Ge)}reset(){var s;(s=p(this,ne))==null||s.removeObserver(this),S(this,ne,void 0),D(this,Me,Ts).call(this),D(this,Me,Mn).call(this)}mutate(s,n){var r;return S(this,Ae,n),(r=p(this,ne))==null||r.removeObserver(this),S(this,ne,p(this,We).getMutationCache().build(p(this,We),this.options)),p(this,ne).addObserver(this),p(this,ne).execute(s)}},We=new WeakMap,Ge=new WeakMap,ne=new WeakMap,Ae=new WeakMap,Me=new WeakSet,Ts=function(){var n;const s=((n=p(this,ne))==null?void 0:n.state)??Pa();S(this,Ge,{...s,isPending:s.status==="pending",isSuccess:s.status==="success",isError:s.status==="error",isIdle:s.status==="idle",mutate:this.mutate,reset:this.reset})},Mn=function(s){G.batch(()=>{var n,r,o,i,a,l,c,u;if(p(this,Ae)&&this.hasListeners()){const h=p(this,Ge).variables,f=p(this,Ge).context;(s==null?void 0:s.type)==="success"?((r=(n=p(this,Ae)).onSuccess)==null||r.call(n,s.data,h,f),(i=(o=p(this,Ae)).onSettled)==null||i.call(o,s.data,null,h,f)):(s==null?void 0:s.type)==="error"&&((l=(a=p(this,Ae)).onError)==null||l.call(a,s.error,h,f),(u=(c=p(this,Ae)).onSettled)==null||u.call(c,void 0,s.error,h,f))}this.listeners.forEach(h=>{h(p(this,Ge))})})},$o),Ca=g.createContext(void 0),or=t=>{const e=g.useContext(Ca);if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},_u=({client:t,children:e})=>(g.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),d.jsx(Ca.Provider,{value:t,children:e})),ja=g.createContext(!1),zu=()=>g.useContext(ja);ja.Provider;function Hu(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var Ku=g.createContext(Hu()),Wu=()=>g.useContext(Ku);function Aa(t,e){return typeof t=="function"?t(...e):!!t}function Nn(){}var Gu=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))},qu=t=>{g.useEffect(()=>{t.clearReset()},[t])},$u=({result:t,errorResetBoundary:e,throwOnError:s,query:n})=>t.isError&&!e.isReset()&&!t.isFetching&&n&&Aa(s,[t.error,n]),Qu=t=>{t.suspense&&(t.staleTime===void 0&&(t.staleTime=1e3),typeof t.gcTime=="number"&&(t.gcTime=Math.max(t.gcTime,1e3)))},Yu=(t,e)=>t.isLoading&&t.isFetching&&!e,Xu=(t,e)=>(t==null?void 0:t.suspense)&&e.isPending,li=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function Zu(t,e,s){var u,h,f,m,v;const n=or(),r=zu(),o=Wu(),i=n.defaultQueryOptions(t);(h=(u=n.getDefaultOptions().queries)==null?void 0:u._experimental_beforeQuery)==null||h.call(u,i),i._optimisticResults=r?"isRestoring":"optimistic",Qu(i),Gu(i,o),qu(o);const a=!n.getQueryCache().get(i.queryHash),[l]=g.useState(()=>new e(n,i)),c=l.getOptimisticResult(i);if(g.useSyncExternalStore(g.useCallback(y=>{const x=r?Nn:l.subscribe(G.batchCalls(y));return l.updateResult(),x},[l,r]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),g.useEffect(()=>{l.setOptions(i,{listeners:!1})},[i,l]),Xu(i,c))throw li(i,l,o);if($u({result:c,errorResetBoundary:o,throwOnError:i.throwOnError,query:n.getQueryCache().get(i.queryHash)}))throw c.error;if((m=(f=n.getDefaultOptions().queries)==null?void 0:f._experimental_afterQuery)==null||m.call(f,i,c),i.experimental_prefetchInRender&&!ut&&Yu(c,r)){const y=a?li(i,l,o):(v=n.getQueryCache().get(i.queryHash))==null?void 0:v.promise;y==null||y.catch(Nn).finally(()=>{l.updateResult()})}return i.notifyOnChangeProps?c:l.trackResult(c)}function Ju(t,e){return Zu(t,Iu)}function ed(t,e){const s=or(),[n]=g.useState(()=>new Uu(s,t));g.useEffect(()=>{n.setOptions(t)},[n,t]);const r=g.useSyncExternalStore(g.useCallback(i=>n.subscribe(G.batchCalls(i)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),o=g.useCallback((i,a)=>{n.mutate(i,a).catch(Nn)},[n]);if(r.error&&Aa(n.options.throwOnError,[r.error]))throw r.error;return{...r,mutate:o,mutateAsync:r.mutate}}async function ka(t){if(!t.ok){const e=await t.text()||t.statusText;throw new Error(`${t.status}: ${e}`)}}async function td(t,e,s){const n=await fetch(e,{method:t,headers:s?{"Content-Type":"application/json"}:{},body:s?JSON.stringify(s):void 0,credentials:"include"});return await ka(n),n}const sd=({on401:t})=>async({queryKey:e})=>{const s=await fetch(e.join("/"),{credentials:"include"});return t==="returnNull"&&s.status===401?null:(await ka(s),await s.json())},nd=new Lu({defaultOptions:{queries:{queryFn:sd({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}}),rd=1,id=1e6;let Js=0;function od(){return Js=(Js+1)%Number.MAX_SAFE_INTEGER,Js.toString()}const en=new Map,ci=t=>{if(en.has(t))return;const e=setTimeout(()=>{en.delete(t),Xt({type:"REMOVE_TOAST",toastId:t})},id);en.set(t,e)},ad=(t,e)=>{switch(e.type){case"ADD_TOAST":return{...t,toasts:[e.toast,...t.toasts].slice(0,rd)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(s=>s.id===e.toast.id?{...s,...e.toast}:s)};case"DISMISS_TOAST":{const{toastId:s}=e;return s?ci(s):t.toasts.forEach(n=>{ci(n.id)}),{...t,toasts:t.toasts.map(n=>n.id===s||s===void 0?{...n,open:!1}:n)}}case"REMOVE_TOAST":return e.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(s=>s.id!==e.toastId)}}},Ps=[];let Ss={toasts:[]};function Xt(t){Ss=ad(Ss,t),Ps.forEach(e=>{e(Ss)})}function ld({...t}){const e=od(),s=r=>Xt({type:"UPDATE_TOAST",toast:{...r,id:e}}),n=()=>Xt({type:"DISMISS_TOAST",toastId:e});return Xt({type:"ADD_TOAST",toast:{...t,id:e,open:!0,onOpenChange:r=>{r||n()}}}),{id:e,dismiss:n,update:s}}function Ea(){const[t,e]=g.useState(Ss);return g.useEffect(()=>(Ps.push(e),()=>{const s=Ps.indexOf(e);s>-1&&Ps.splice(s,1)}),[t]),{...t,toast:ld,dismiss:s=>Xt({type:"DISMISS_TOAST",toastId:s})}}function Ra(t){var e,s,n="";if(typeof t=="string"||typeof t=="number")n+=t;else if(typeof t=="object")if(Array.isArray(t)){var r=t.length;for(e=0;e<r;e++)t[e]&&(s=Ra(t[e]))&&(n&&(n+=" "),n+=s)}else for(s in t)t[s]&&(n&&(n+=" "),n+=s);return n}function Ma(){for(var t,e,s=0,n="",r=arguments.length;s<r;s++)(t=arguments[s])&&(e=Ra(t))&&(n&&(n+=" "),n+=e);return n}const ui=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,di=Ma,cd=(t,e)=>s=>{var n;if((e==null?void 0:e.variants)==null)return di(t,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:r,defaultVariants:o}=e,i=Object.keys(r).map(c=>{const u=s==null?void 0:s[c],h=o==null?void 0:o[c];if(u===null)return null;const f=ui(u)||ui(h);return r[c][f]}),a=s&&Object.entries(s).reduce((c,u)=>{let[h,f]=u;return f===void 0||(c[h]=f),c},{}),l=e==null||(n=e.compoundVariants)===null||n===void 0?void 0:n.reduce((c,u)=>{let{class:h,className:f,...m}=u;return Object.entries(m).every(v=>{let[y,x]=v;return Array.isArray(x)?x.includes({...o,...a}[y]):{...o,...a}[y]===x})?[...c,h,f]:c},[]);return di(t,i,l,s==null?void 0:s.class,s==null?void 0:s.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ud=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Na=(...t)=>t.filter((e,s,n)=>!!e&&n.indexOf(e)===s).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var dd={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hd=g.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:r="",children:o,iconNode:i,...a},l)=>g.createElement("svg",{ref:l,...dd,width:e,height:e,stroke:t,strokeWidth:n?Number(s)*24/Number(e):s,className:Na("lucide",r),...a},[...i.map(([c,u])=>g.createElement(c,u)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U=(t,e)=>{const s=g.forwardRef(({className:n,...r},o)=>g.createElement(hd,{ref:o,iconNode:e,className:Na(`lucide-${ud(t)}`,n),...r}));return s.displayName=`${t}`,s};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Da=U("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fd=U("Bike",[["circle",{cx:"18.5",cy:"17.5",r:"3.5",key:"15x4ox"}],["circle",{cx:"5.5",cy:"17.5",r:"3.5",key:"1noe27"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["path",{d:"M12 17.5V14l-3-3 4-3 2 3h2",key:"1npguv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hi=U("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pd=U("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const md=U("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yd=U("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Va=U("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gd=U("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fi=U("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xd=U("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cs=U("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vd=U("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bd=U("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wd=U("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Td=U("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pd=U("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sd=U("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cd=U("Sandwich",[["path",{d:"m2.37 11.223 8.372-6.777a2 2 0 0 1 2.516 0l8.371 6.777",key:"f1wd0e"}],["path",{d:"M21 15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5.25",key:"1pfu07"}],["path",{d:"M3 15a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h9",key:"1oq9qw"}],["path",{d:"m6.67 15 6.13 4.6a2 2 0 0 0 2.8-.4l3.15-4.2",key:"1fnwu5"}],["rect",{width:"20",height:"4",x:"2",y:"11",rx:"1",key:"itshg"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pi=U("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jd=U("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ad=U("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kd=U("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oa=U("Utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const La=U("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),ar="-",Ed=t=>{const e=Md(t),{conflictingClassGroups:s,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:i=>{const a=i.split(ar);return a[0]===""&&a.length!==1&&a.shift(),Ia(a,e)||Rd(i)},getConflictingClassGroupIds:(i,a)=>{const l=s[i]||[];return a&&n[i]?[...l,...n[i]]:l}}},Ia=(t,e)=>{var i;if(t.length===0)return e.classGroupId;const s=t[0],n=e.nextPart.get(s),r=n?Ia(t.slice(1),n):void 0;if(r)return r;if(e.validators.length===0)return;const o=t.join(ar);return(i=e.validators.find(({validator:a})=>a(o)))==null?void 0:i.classGroupId},mi=/^\[(.+)\]$/,Rd=t=>{if(mi.test(t)){const e=mi.exec(t)[1],s=e==null?void 0:e.substring(0,e.indexOf(":"));if(s)return"arbitrary.."+s}},Md=t=>{const{theme:e,prefix:s}=t,n={nextPart:new Map,validators:[]};return Dd(Object.entries(t.classGroups),s).forEach(([o,i])=>{Dn(i,n,o,e)}),n},Dn=(t,e,s,n)=>{t.forEach(r=>{if(typeof r=="string"){const o=r===""?e:yi(e,r);o.classGroupId=s;return}if(typeof r=="function"){if(Nd(r)){Dn(r(n),e,s,n);return}e.validators.push({validator:r,classGroupId:s});return}Object.entries(r).forEach(([o,i])=>{Dn(i,yi(e,o),s,n)})})},yi=(t,e)=>{let s=t;return e.split(ar).forEach(n=>{s.nextPart.has(n)||s.nextPart.set(n,{nextPart:new Map,validators:[]}),s=s.nextPart.get(n)}),s},Nd=t=>t.isThemeGetter,Dd=(t,e)=>e?t.map(([s,n])=>{const r=n.map(o=>typeof o=="string"?e+o:typeof o=="object"?Object.fromEntries(Object.entries(o).map(([i,a])=>[e+i,a])):o);return[s,r]}):t,Vd=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,s=new Map,n=new Map;const r=(o,i)=>{s.set(o,i),e++,e>t&&(e=0,n=s,s=new Map)};return{get(o){let i=s.get(o);if(i!==void 0)return i;if((i=n.get(o))!==void 0)return r(o,i),i},set(o,i){s.has(o)?s.set(o,i):r(o,i)}}},Fa="!",Od=t=>{const{separator:e,experimentalParseClassName:s}=t,n=e.length===1,r=e[0],o=e.length,i=a=>{const l=[];let c=0,u=0,h;for(let x=0;x<a.length;x++){let b=a[x];if(c===0){if(b===r&&(n||a.slice(x,x+o)===e)){l.push(a.slice(u,x)),u=x+o;continue}if(b==="/"){h=x;continue}}b==="["?c++:b==="]"&&c--}const f=l.length===0?a:a.substring(u),m=f.startsWith(Fa),v=m?f.substring(1):f,y=h&&h>u?h-u:void 0;return{modifiers:l,hasImportantModifier:m,baseClassName:v,maybePostfixModifierPosition:y}};return s?a=>s({className:a,parseClassName:i}):i},Ld=t=>{if(t.length<=1)return t;const e=[];let s=[];return t.forEach(n=>{n[0]==="["?(e.push(...s.sort(),n),s=[]):s.push(n)}),e.push(...s.sort()),e},Id=t=>({cache:Vd(t.cacheSize),parseClassName:Od(t),...Ed(t)}),Fd=/\s+/,Bd=(t,e)=>{const{parseClassName:s,getClassGroupId:n,getConflictingClassGroupIds:r}=e,o=[],i=t.trim().split(Fd);let a="";for(let l=i.length-1;l>=0;l-=1){const c=i[l],{modifiers:u,hasImportantModifier:h,baseClassName:f,maybePostfixModifierPosition:m}=s(c);let v=!!m,y=n(v?f.substring(0,m):f);if(!y){if(!v){a=c+(a.length>0?" "+a:a);continue}if(y=n(f),!y){a=c+(a.length>0?" "+a:a);continue}v=!1}const x=Ld(u).join(":"),b=h?x+Fa:x,w=b+y;if(o.includes(w))continue;o.push(w);const T=r(y,v);for(let A=0;A<T.length;++A){const C=T[A];o.push(b+C)}a=c+(a.length>0?" "+a:a)}return a};function Ud(){let t=0,e,s,n="";for(;t<arguments.length;)(e=arguments[t++])&&(s=Ba(e))&&(n&&(n+=" "),n+=s);return n}const Ba=t=>{if(typeof t=="string")return t;let e,s="";for(let n=0;n<t.length;n++)t[n]&&(e=Ba(t[n]))&&(s&&(s+=" "),s+=e);return s};function _d(t,...e){let s,n,r,o=i;function i(l){const c=e.reduce((u,h)=>h(u),t());return s=Id(c),n=s.cache.get,r=s.cache.set,o=a,a(l)}function a(l){const c=n(l);if(c)return c;const u=Bd(l,s);return r(l,u),u}return function(){return o(Ud.apply(null,arguments))}}const B=t=>{const e=s=>s[t]||[];return e.isThemeGetter=!0,e},Ua=/^\[(?:([a-z-]+):)?(.+)\]$/i,zd=/^\d+\/\d+$/,Hd=new Set(["px","full","screen"]),Kd=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Wd=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Gd=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,qd=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,$d=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ce=t=>Tt(t)||Hd.has(t)||zd.test(t),De=t=>_t(t,"length",sh),Tt=t=>!!t&&!Number.isNaN(Number(t)),tn=t=>_t(t,"number",Tt),Kt=t=>!!t&&Number.isInteger(Number(t)),Qd=t=>t.endsWith("%")&&Tt(t.slice(0,-1)),N=t=>Ua.test(t),Ve=t=>Kd.test(t),Yd=new Set(["length","size","percentage"]),Xd=t=>_t(t,Yd,_a),Zd=t=>_t(t,"position",_a),Jd=new Set(["image","url"]),eh=t=>_t(t,Jd,rh),th=t=>_t(t,"",nh),Wt=()=>!0,_t=(t,e,s)=>{const n=Ua.exec(t);return n?n[1]?typeof e=="string"?n[1]===e:e.has(n[1]):s(n[2]):!1},sh=t=>Wd.test(t)&&!Gd.test(t),_a=()=>!1,nh=t=>qd.test(t),rh=t=>$d.test(t),ih=()=>{const t=B("colors"),e=B("spacing"),s=B("blur"),n=B("brightness"),r=B("borderColor"),o=B("borderRadius"),i=B("borderSpacing"),a=B("borderWidth"),l=B("contrast"),c=B("grayscale"),u=B("hueRotate"),h=B("invert"),f=B("gap"),m=B("gradientColorStops"),v=B("gradientColorStopPositions"),y=B("inset"),x=B("margin"),b=B("opacity"),w=B("padding"),T=B("saturate"),A=B("scale"),C=B("sepia"),M=B("skew"),V=B("space"),j=B("translate"),R=()=>["auto","contain","none"],_=()=>["auto","hidden","clip","visible","scroll"],Y=()=>["auto",N,e],I=()=>[N,e],ys=()=>["",Ce,De],Xe=()=>["auto",Tt,N],Qs=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Ht=()=>["solid","dashed","dotted","double","none"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],re=()=>["start","end","center","between","around","evenly","stretch"],Pe=()=>["","0",N],pt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Se=()=>[Tt,N];return{cacheSize:500,separator:":",theme:{colors:[Wt],spacing:[Ce,De],blur:["none","",Ve,N],brightness:Se(),borderColor:[t],borderRadius:["none","","full",Ve,N],borderSpacing:I(),borderWidth:ys(),contrast:Se(),grayscale:Pe(),hueRotate:Se(),invert:Pe(),gap:I(),gradientColorStops:[t],gradientColorStopPositions:[Qd,De],inset:Y(),margin:Y(),opacity:Se(),padding:I(),saturate:Se(),scale:Se(),sepia:Pe(),skew:Se(),space:I(),translate:I()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[Ve]}],"break-after":[{"break-after":pt()}],"break-before":[{"break-before":pt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Qs(),N]}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Kt,N]}],basis:[{basis:Y()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:Pe()}],shrink:[{shrink:Pe()}],order:[{order:["first","last","none",Kt,N]}],"grid-cols":[{"grid-cols":[Wt]}],"col-start-end":[{col:["auto",{span:["full",Kt,N]},N]}],"col-start":[{"col-start":Xe()}],"col-end":[{"col-end":Xe()}],"grid-rows":[{"grid-rows":[Wt]}],"row-start-end":[{row:["auto",{span:[Kt,N]},N]}],"row-start":[{"row-start":Xe()}],"row-end":[{"row-end":Xe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...re()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...re(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...re(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[w]}],px:[{px:[w]}],py:[{py:[w]}],ps:[{ps:[w]}],pe:[{pe:[w]}],pt:[{pt:[w]}],pr:[{pr:[w]}],pb:[{pb:[w]}],pl:[{pl:[w]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[V]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[V]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",N,e]}],"min-w":[{"min-w":[N,e,"min","max","fit"]}],"max-w":[{"max-w":[N,e,"none","full","min","max","fit","prose",{screen:[Ve]},Ve]}],h:[{h:[N,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[N,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[N,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[N,e,"auto","min","max","fit"]}],"font-size":[{text:["base",Ve,De]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",tn]}],"font-family":[{font:[Wt]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",Tt,tn]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ce,N]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Ht(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ce,De]}],"underline-offset":[{"underline-offset":["auto",Ce,N]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Qs(),Zd]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Xd]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},eh]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[v]}],"gradient-via-pos":[{via:[v]}],"gradient-to-pos":[{to:[v]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:[...Ht(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:Ht()}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["",...Ht()]}],"outline-offset":[{"outline-offset":[Ce,N]}],"outline-w":[{outline:[Ce,De]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:ys()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[Ce,De]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",Ve,th]}],"shadow-color":[{shadow:[Wt]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":[...q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none"]}],blur:[{blur:[s]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Ve,N]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[h]}],saturate:[{saturate:[T]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[s]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[T]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:Se()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:Se()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[A]}],"scale-x":[{"scale-x":[A]}],"scale-y":[{"scale-y":[A]}],rotate:[{rotate:[Kt,N]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[M]}],"skew-y":[{"skew-y":[M]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[Ce,De,tn]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},oh=_d(ih);function ae(...t){return oh(Ma(t))}const ah=Vc,za=g.forwardRef(({className:t,...e},s)=>d.jsx(Qo,{ref:s,className:ae("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),...e}));za.displayName=Qo.displayName;const lh=cd("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Ha=g.forwardRef(({className:t,variant:e,...s},n)=>d.jsx(Yo,{ref:n,className:ae(lh({variant:e}),t),...s}));Ha.displayName=Yo.displayName;const ch=g.forwardRef(({className:t,...e},s)=>d.jsx(Xo,{ref:s,className:ae("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",t),...e}));ch.displayName=Xo.displayName;const Ka=g.forwardRef(({className:t,...e},s)=>d.jsx(Zo,{ref:s,className:ae("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),"toast-close":"",...e,children:d.jsx(La,{className:"h-4 w-4"})}));Ka.displayName=Zo.displayName;const Wa=g.forwardRef(({className:t,...e},s)=>d.jsx(Jo,{ref:s,className:ae("text-sm font-semibold",t),...e}));Wa.displayName=Jo.displayName;const Ga=g.forwardRef(({className:t,...e},s)=>d.jsx(ea,{ref:s,className:ae("text-sm opacity-90",t),...e}));Ga.displayName=ea.displayName;function uh(){const{toasts:t}=Ea();return d.jsxs(ah,{children:[t.map(function({id:e,title:s,description:n,action:r,...o}){return d.jsxs(Ha,{...o,children:[d.jsxs("div",{className:"grid gap-1",children:[s&&d.jsx(Wa,{children:s}),n&&d.jsx(Ga,{children:n})]}),r,d.jsx(Ka,{})]},e)}),d.jsx(za,{})]})}var[_s,$g]=Oc("Tooltip",[ta]),lr=ta(),qa="TooltipProvider",dh=700,gi="tooltip.open",[hh,$a]=_s(qa),Qa=t=>{const{__scopeTooltip:e,delayDuration:s=dh,skipDelayDuration:n=300,disableHoverableContent:r=!1,children:o}=t,i=g.useRef(!0),a=g.useRef(!1),l=g.useRef(0);return g.useEffect(()=>{const c=l.current;return()=>window.clearTimeout(c)},[]),d.jsx(hh,{scope:e,isOpenDelayedRef:i,delayDuration:s,onOpen:g.useCallback(()=>{window.clearTimeout(l.current),i.current=!1},[]),onClose:g.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.current=!0,n)},[n]),isPointerInTransitRef:a,onPointerInTransitChange:g.useCallback(c=>{a.current=c},[]),disableHoverableContent:r,children:o})};Qa.displayName=qa;var Ya="Tooltip",[Qg,zs]=_s(Ya),Vn="TooltipTrigger",fh=g.forwardRef((t,e)=>{const{__scopeTooltip:s,...n}=t,r=zs(Vn,s),o=$a(Vn,s),i=lr(s),a=g.useRef(null),l=sa(e,a,r.onTriggerChange),c=g.useRef(!1),u=g.useRef(!1),h=g.useCallback(()=>c.current=!1,[]);return g.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),d.jsx(Lc,{asChild:!0,...i,children:d.jsx(Ic.button,{"aria-describedby":r.open?r.contentId:void 0,"data-state":r.stateAttribute,...n,ref:l,onPointerMove:mt(t.onPointerMove,f=>{f.pointerType!=="touch"&&!u.current&&!o.isPointerInTransitRef.current&&(r.onTriggerEnter(),u.current=!0)}),onPointerLeave:mt(t.onPointerLeave,()=>{r.onTriggerLeave(),u.current=!1}),onPointerDown:mt(t.onPointerDown,()=>{r.open&&r.onClose(),c.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:mt(t.onFocus,()=>{c.current||r.onOpen()}),onBlur:mt(t.onBlur,r.onClose),onClick:mt(t.onClick,r.onClose)})})});fh.displayName=Vn;var ph="TooltipPortal",[Yg,mh]=_s(ph,{forceMount:void 0}),It="TooltipContent",Xa=g.forwardRef((t,e)=>{const s=mh(It,t.__scopeTooltip),{forceMount:n=s.forceMount,side:r="top",...o}=t,i=zs(It,t.__scopeTooltip);return d.jsx(Fc,{present:n||i.open,children:i.disableHoverableContent?d.jsx(Za,{side:r,...o,ref:e}):d.jsx(yh,{side:r,...o,ref:e})})}),yh=g.forwardRef((t,e)=>{const s=zs(It,t.__scopeTooltip),n=$a(It,t.__scopeTooltip),r=g.useRef(null),o=sa(e,r),[i,a]=g.useState(null),{trigger:l,onClose:c}=s,u=r.current,{onPointerInTransitChange:h}=n,f=g.useCallback(()=>{a(null),h(!1)},[h]),m=g.useCallback((v,y)=>{const x=v.currentTarget,b={x:v.clientX,y:v.clientY},w=wh(b,x.getBoundingClientRect()),T=Th(b,w),A=Ph(y.getBoundingClientRect()),C=Ch([...T,...A]);a(C),h(!0)},[h]);return g.useEffect(()=>()=>f(),[f]),g.useEffect(()=>{if(l&&u){const v=x=>m(x,u),y=x=>m(x,l);return l.addEventListener("pointerleave",v),u.addEventListener("pointerleave",y),()=>{l.removeEventListener("pointerleave",v),u.removeEventListener("pointerleave",y)}}},[l,u,m,f]),g.useEffect(()=>{if(i){const v=y=>{const x=y.target,b={x:y.clientX,y:y.clientY},w=(l==null?void 0:l.contains(x))||(u==null?void 0:u.contains(x)),T=!Sh(b,i);w?f():T&&(f(),c())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[l,u,i,c,f]),d.jsx(Za,{...t,ref:o})}),[gh,xh]=_s(Ya,{isInside:!1}),vh=Hc("TooltipContent"),Za=g.forwardRef((t,e)=>{const{__scopeTooltip:s,children:n,"aria-label":r,onEscapeKeyDown:o,onPointerDownOutside:i,...a}=t,l=zs(It,s),c=lr(s),{onClose:u}=l;return g.useEffect(()=>(document.addEventListener(gi,u),()=>document.removeEventListener(gi,u)),[u]),g.useEffect(()=>{if(l.trigger){const h=f=>{const m=f.target;m!=null&&m.contains(l.trigger)&&u()};return window.addEventListener("scroll",h,{capture:!0}),()=>window.removeEventListener("scroll",h,{capture:!0})}},[l.trigger,u]),d.jsx(Bc,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:h=>h.preventDefault(),onDismiss:u,children:d.jsxs(Uc,{"data-state":l.stateAttribute,...c,...a,ref:e,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[d.jsx(vh,{children:n}),d.jsx(gh,{scope:s,isInside:!0,children:d.jsx(_c,{id:l.contentId,role:"tooltip",children:r||n})})]})})});Xa.displayName=It;var Ja="TooltipArrow",bh=g.forwardRef((t,e)=>{const{__scopeTooltip:s,...n}=t,r=lr(s);return xh(Ja,s).isInside?null:d.jsx(zc,{...r,...n,ref:e})});bh.displayName=Ja;function wh(t,e){const s=Math.abs(e.top-t.y),n=Math.abs(e.bottom-t.y),r=Math.abs(e.right-t.x),o=Math.abs(e.left-t.x);switch(Math.min(s,n,r,o)){case o:return"left";case r:return"right";case s:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function Th(t,e,s=5){const n=[];switch(e){case"top":n.push({x:t.x-s,y:t.y+s},{x:t.x+s,y:t.y+s});break;case"bottom":n.push({x:t.x-s,y:t.y-s},{x:t.x+s,y:t.y-s});break;case"left":n.push({x:t.x+s,y:t.y-s},{x:t.x+s,y:t.y+s});break;case"right":n.push({x:t.x-s,y:t.y-s},{x:t.x-s,y:t.y+s});break}return n}function Ph(t){const{top:e,right:s,bottom:n,left:r}=t;return[{x:r,y:e},{x:s,y:e},{x:s,y:n},{x:r,y:n}]}function Sh(t,e){const{x:s,y:n}=t;let r=!1;for(let o=0,i=e.length-1;o<e.length;i=o++){const a=e[o].x,l=e[o].y,c=e[i].x,u=e[i].y;l>n!=u>n&&s<(c-a)*(n-l)/(u-l)+a&&(r=!r)}return r}function Ch(t){const e=t.slice();return e.sort((s,n)=>s.x<n.x?-1:s.x>n.x?1:s.y<n.y?-1:s.y>n.y?1:0),jh(e)}function jh(t){if(t.length<=1)return t.slice();const e=[];for(let n=0;n<t.length;n++){const r=t[n];for(;e.length>=2;){const o=e[e.length-1],i=e[e.length-2];if((o.x-i.x)*(r.y-i.y)>=(o.y-i.y)*(r.x-i.x))e.pop();else break}e.push(r)}e.pop();const s=[];for(let n=t.length-1;n>=0;n--){const r=t[n];for(;s.length>=2;){const o=s[s.length-1],i=s[s.length-2];if((o.x-i.x)*(r.y-i.y)>=(o.y-i.y)*(r.x-i.x))s.pop();else break}s.push(r)}return s.pop(),e.length===1&&s.length===1&&e[0].x===s[0].x&&e[0].y===s[0].y?e:e.concat(s)}var Ah=Qa,el=Xa;const kh=Ah,Eh=g.forwardRef(({className:t,sideOffset:e=4,...s},n)=>d.jsx(el,{ref:n,sideOffset:e,className:ae("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",t),...s}));Eh.displayName=el.displayName;const tl=g.forwardRef(({className:t,...e},s)=>d.jsx("div",{ref:s,className:ae("rounded-lg border bg-card text-card-foreground shadow-sm",t),...e}));tl.displayName="Card";const Rh=g.forwardRef(({className:t,...e},s)=>d.jsx("div",{ref:s,className:ae("flex flex-col space-y-1.5 p-6",t),...e}));Rh.displayName="CardHeader";const Mh=g.forwardRef(({className:t,...e},s)=>d.jsx("div",{ref:s,className:ae("text-2xl font-semibold leading-none tracking-tight",t),...e}));Mh.displayName="CardTitle";const Nh=g.forwardRef(({className:t,...e},s)=>d.jsx("div",{ref:s,className:ae("text-sm text-muted-foreground",t),...e}));Nh.displayName="CardDescription";const sl=g.forwardRef(({className:t,...e},s)=>d.jsx("div",{ref:s,className:ae("p-6 pt-0",t),...e}));sl.displayName="CardContent";const Dh=g.forwardRef(({className:t,...e},s)=>d.jsx("div",{ref:s,className:ae("flex items-center p-6 pt-0",t),...e}));Dh.displayName="CardFooter";function Vh(){return d.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:d.jsx(tl,{className:"w-full max-w-md mx-4",children:d.jsxs(sl,{className:"pt-6",children:[d.jsxs("div",{className:"flex mb-4 gap-2",children:[d.jsx(yd,{className:"h-8 w-8 text-red-500"}),d.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),d.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}function Oh(t){if(typeof Proxy>"u")return t;const e=new Map,s=(...n)=>t(...n);return new Proxy(s,{get:(n,r)=>r==="create"?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}function Hs(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const On=t=>Array.isArray(t);function nl(t,e){if(!Array.isArray(e))return!1;const s=e.length;if(s!==t.length)return!1;for(let n=0;n<s;n++)if(e[n]!==t[n])return!1;return!0}function rs(t){return typeof t=="string"||Array.isArray(t)}function xi(t){const e=[{},{}];return t==null||t.values.forEach((s,n)=>{e[0][n]=s.get(),e[1][n]=s.getVelocity()}),e}function cr(t,e,s,n){if(typeof e=="function"){const[r,o]=xi(n);e=e(s!==void 0?s:t.custom,r,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[r,o]=xi(n);e=e(s!==void 0?s:t.custom,r,o)}return e}function Ks(t,e,s){const n=t.getProps();return cr(n,e,s!==void 0?s:n.custom,t)}const ur=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],dr=["initial",...ur],ds=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ft=new Set(ds),Ee=t=>t*1e3,Re=t=>t/1e3,Lh={type:"spring",stiffness:500,damping:25,restSpeed:10},Ih=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Fh={type:"keyframes",duration:.8},Bh={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Uh=(t,{keyframes:e})=>e.length>2?Fh:ft.has(t)?t.startsWith("scale")?Ih(e[1]):Lh:Bh;function hr(t,e){return t?t[e]||t.default||t:void 0}const _h={skipAnimations:!1,useManualTiming:!1},zh=t=>t!==null;function Ws(t,{repeat:e,repeatType:s="loop"},n){const r=t.filter(zh),o=e&&s!=="loop"&&e%2===1?0:r.length-1;return!o||n===void 0?r[o]:n}const Q=t=>t;let Hh=Q,Ln=Q;function Kh(t){let e=new Set,s=new Set,n=!1,r=!1;const o=new WeakSet;let i={delta:0,timestamp:0,isProcessing:!1};function a(c){o.has(c)&&(l.schedule(c),t()),c(i)}const l={schedule:(c,u=!1,h=!1)=>{const m=h&&n?e:s;return u&&o.add(c),m.has(c)||m.add(c),c},cancel:c=>{s.delete(c),o.delete(c)},process:c=>{if(i=c,n){r=!0;return}n=!0,[e,s]=[s,e],s.clear(),e.forEach(a),n=!1,r&&(r=!1,l.process(c))}};return l}const vs=["read","resolveKeyframes","update","preRender","render","postRender"],Wh=40;function rl(t,e){let s=!1,n=!0;const r={delta:0,timestamp:0,isProcessing:!1},o=()=>s=!0,i=vs.reduce((b,w)=>(b[w]=Kh(o),b),{}),{read:a,resolveKeyframes:l,update:c,preRender:u,render:h,postRender:f}=i,m=()=>{const b=performance.now();s=!1,r.delta=n?1e3/60:Math.max(Math.min(b-r.timestamp,Wh),1),r.timestamp=b,r.isProcessing=!0,a.process(r),l.process(r),c.process(r),u.process(r),h.process(r),f.process(r),r.isProcessing=!1,s&&e&&(n=!1,t(m))},v=()=>{s=!0,n=!0,r.isProcessing||t(m)};return{schedule:vs.reduce((b,w)=>{const T=i[w];return b[w]=(A,C=!1,M=!1)=>(s||v(),T.schedule(A,C,M)),b},{}),cancel:b=>{for(let w=0;w<vs.length;w++)i[vs[w]].cancel(b)},state:r,steps:i}}const{schedule:L,cancel:me,state:$,steps:sn}=rl(typeof requestAnimationFrame<"u"?requestAnimationFrame:Q,!0),il=(t,e,s)=>(((1-3*s+3*e)*t+(3*s-6*e))*t+3*e)*t,Gh=1e-7,qh=12;function $h(t,e,s,n,r){let o,i,a=0;do i=e+(s-e)/2,o=il(i,n,r)-t,o>0?s=i:e=i;while(Math.abs(o)>Gh&&++a<qh);return i}function hs(t,e,s,n){if(t===e&&s===n)return Q;const r=o=>$h(o,0,1,t,s);return o=>o===0||o===1?o:il(r(o),e,n)}const ol=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,al=t=>e=>1-t(1-e),ll=hs(.33,1.53,.69,.99),fr=al(ll),cl=ol(fr),ul=t=>(t*=2)<1?.5*fr(t):.5*(2-Math.pow(2,-10*(t-1))),pr=t=>1-Math.sin(Math.acos(t)),dl=al(pr),hl=ol(pr),fl=t=>/^0[^.\s]+$/u.test(t);function Qh(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||fl(t):!0}const pl=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ml=t=>e=>typeof e=="string"&&e.startsWith(t),yl=ml("--"),Yh=ml("var(--"),mr=t=>Yh(t)?Xh.test(t.split("/*")[0].trim()):!1,Xh=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Zh=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Jh(t){const e=Zh.exec(t);if(!e)return[,];const[,s,n,r]=e;return[`--${s??n}`,r]}function gl(t,e,s=1){const[n,r]=Jh(t);if(!n)return;const o=window.getComputedStyle(e).getPropertyValue(n);if(o){const i=o.trim();return pl(i)?parseFloat(i):i}return mr(r)?gl(r,e,s+1):r}const Ne=(t,e,s)=>s>e?e:s<t?t:s,zt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},is={...zt,transform:t=>Ne(0,1,t)},bs={...zt,default:1},fs=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Le=fs("deg"),we=fs("%"),E=fs("px"),ef=fs("vh"),tf=fs("vw"),vi={...we,parse:t=>we.parse(t)/100,transform:t=>we.transform(t*100)},sf=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),bi=t=>t===zt||t===E,wi=(t,e)=>parseFloat(t.split(", ")[e]),Ti=(t,e)=>(s,{transform:n})=>{if(n==="none"||!n)return 0;const r=n.match(/^matrix3d\((.+)\)$/u);if(r)return wi(r[1],e);{const o=n.match(/^matrix\((.+)\)$/u);return o?wi(o[1],t):0}},nf=new Set(["x","y","z"]),rf=ds.filter(t=>!nf.has(t));function of(t){const e=[];return rf.forEach(s=>{const n=t.getValue(s);n!==void 0&&(e.push([s,n.get()]),n.set(s.startsWith("scale")?1:0))}),e}const Ft={width:({x:t},{paddingLeft:e="0",paddingRight:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),height:({y:t},{paddingTop:e="0",paddingBottom:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:Ti(4,13),y:Ti(5,14)};Ft.translateX=Ft.x;Ft.translateY=Ft.y;const xl=t=>e=>e.test(t),af={test:t=>t==="auto",parse:t=>t},vl=[zt,E,we,Le,tf,ef,af],Pi=t=>vl.find(xl(t)),ct=new Set;let In=!1,Fn=!1;function bl(){if(Fn){const t=Array.from(ct).filter(n=>n.needsMeasurement),e=new Set(t.map(n=>n.element)),s=new Map;e.forEach(n=>{const r=of(n);r.length&&(s.set(n,r),n.render())}),t.forEach(n=>n.measureInitialState()),e.forEach(n=>{n.render();const r=s.get(n);r&&r.forEach(([o,i])=>{var a;(a=n.getValue(o))===null||a===void 0||a.set(i)})}),t.forEach(n=>n.measureEndState()),t.forEach(n=>{n.suspendedScrollY!==void 0&&window.scrollTo(0,n.suspendedScrollY)})}Fn=!1,In=!1,ct.forEach(t=>t.complete()),ct.clear()}function wl(){ct.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Fn=!0)})}function lf(){wl(),bl()}class yr{constructor(e,s,n,r,o,i=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=s,this.name=n,this.motionValue=r,this.element=o,this.isAsync=i}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ct.add(this),In||(In=!0,L.read(wl),L.resolveKeyframes(bl))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:s,element:n,motionValue:r}=this;for(let o=0;o<e.length;o++)if(e[o]===null)if(o===0){const i=r==null?void 0:r.get(),a=e[e.length-1];if(i!==void 0)e[0]=i;else if(n&&s){const l=n.readValue(s,a);l!=null&&(e[0]=l)}e[0]===void 0&&(e[0]=a),r&&i===void 0&&r.set(e[0])}else e[o]=e[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ct.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ct.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Zt=t=>Math.round(t*1e5)/1e5,gr=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function cf(t){return t==null}const uf=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,xr=(t,e)=>s=>!!(typeof s=="string"&&uf.test(s)&&s.startsWith(t)||e&&!cf(s)&&Object.prototype.hasOwnProperty.call(s,e)),Tl=(t,e,s)=>n=>{if(typeof n!="string")return n;const[r,o,i,a]=n.match(gr);return{[t]:parseFloat(r),[e]:parseFloat(o),[s]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},df=t=>Ne(0,255,t),nn={...zt,transform:t=>Math.round(df(t))},tt={test:xr("rgb","red"),parse:Tl("red","green","blue"),transform:({red:t,green:e,blue:s,alpha:n=1})=>"rgba("+nn.transform(t)+", "+nn.transform(e)+", "+nn.transform(s)+", "+Zt(is.transform(n))+")"};function hf(t){let e="",s="",n="",r="";return t.length>5?(e=t.substring(1,3),s=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),s=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,s+=s,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(s,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}}const Bn={test:xr("#"),parse:hf,transform:tt.transform},yt={test:xr("hsl","hue"),parse:Tl("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:s,alpha:n=1})=>"hsla("+Math.round(t)+", "+we.transform(Zt(e))+", "+we.transform(Zt(s))+", "+Zt(is.transform(n))+")"},Z={test:t=>tt.test(t)||Bn.test(t)||yt.test(t),parse:t=>tt.test(t)?tt.parse(t):yt.test(t)?yt.parse(t):Bn.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?tt.transform(t):yt.transform(t)},ff=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function pf(t){var e,s;return isNaN(t)&&typeof t=="string"&&(((e=t.match(gr))===null||e===void 0?void 0:e.length)||0)+(((s=t.match(ff))===null||s===void 0?void 0:s.length)||0)>0}const Pl="number",Sl="color",mf="var",yf="var(",Si="${}",gf=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function os(t){const e=t.toString(),s=[],n={color:[],number:[],var:[]},r=[];let o=0;const a=e.replace(gf,l=>(Z.test(l)?(n.color.push(o),r.push(Sl),s.push(Z.parse(l))):l.startsWith(yf)?(n.var.push(o),r.push(mf),s.push(l)):(n.number.push(o),r.push(Pl),s.push(parseFloat(l))),++o,Si)).split(Si);return{values:s,split:a,indexes:n,types:r}}function Cl(t){return os(t).values}function jl(t){const{split:e,types:s}=os(t),n=e.length;return r=>{let o="";for(let i=0;i<n;i++)if(o+=e[i],r[i]!==void 0){const a=s[i];a===Pl?o+=Zt(r[i]):a===Sl?o+=Z.transform(r[i]):o+=r[i]}return o}}const xf=t=>typeof t=="number"?0:t;function vf(t){const e=Cl(t);return jl(t)(e.map(xf))}const Qe={test:pf,parse:Cl,createTransformer:jl,getAnimatableNone:vf},bf=new Set(["brightness","contrast","saturate","opacity"]);function wf(t){const[e,s]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[n]=s.match(gr)||[];if(!n)return t;const r=s.replace(n,"");let o=bf.has(e)?1:0;return n!==s&&(o*=100),e+"("+o+r+")"}const Tf=/\b([a-z-]*)\(.*?\)/gu,Un={...Qe,getAnimatableNone:t=>{const e=t.match(Tf);return e?e.map(wf).join(" "):t}},Pf={borderWidth:E,borderTopWidth:E,borderRightWidth:E,borderBottomWidth:E,borderLeftWidth:E,borderRadius:E,radius:E,borderTopLeftRadius:E,borderTopRightRadius:E,borderBottomRightRadius:E,borderBottomLeftRadius:E,width:E,maxWidth:E,height:E,maxHeight:E,top:E,right:E,bottom:E,left:E,padding:E,paddingTop:E,paddingRight:E,paddingBottom:E,paddingLeft:E,margin:E,marginTop:E,marginRight:E,marginBottom:E,marginLeft:E,backgroundPositionX:E,backgroundPositionY:E},Sf={rotate:Le,rotateX:Le,rotateY:Le,rotateZ:Le,scale:bs,scaleX:bs,scaleY:bs,scaleZ:bs,skew:Le,skewX:Le,skewY:Le,distance:E,translateX:E,translateY:E,translateZ:E,x:E,y:E,z:E,perspective:E,transformPerspective:E,opacity:is,originX:vi,originY:vi,originZ:E},Ci={...zt,transform:Math.round},vr={...Pf,...Sf,zIndex:Ci,size:E,fillOpacity:is,strokeOpacity:is,numOctaves:Ci},Cf={...vr,color:Z,backgroundColor:Z,outlineColor:Z,fill:Z,stroke:Z,borderColor:Z,borderTopColor:Z,borderRightColor:Z,borderBottomColor:Z,borderLeftColor:Z,filter:Un,WebkitFilter:Un},br=t=>Cf[t];function Al(t,e){let s=br(t);return s!==Un&&(s=Qe),s.getAnimatableNone?s.getAnimatableNone(e):void 0}const jf=new Set(["auto","none","0"]);function Af(t,e,s){let n=0,r;for(;n<t.length&&!r;){const o=t[n];typeof o=="string"&&!jf.has(o)&&os(o).values.length&&(r=t[n]),n++}if(r&&s)for(const o of e)t[o]=Al(s,r)}class kl extends yr{constructor(e,s,n,r,o){super(e,s,n,r,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:s,name:n}=this;if(!s||!s.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),mr(c))){const u=gl(c,s.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!sf.has(n)||e.length!==2)return;const[r,o]=e,i=Pi(r),a=Pi(o);if(i!==a)if(bi(i)&&bi(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:s}=this,n=[];for(let r=0;r<e.length;r++)Qh(e[r])&&n.push(r);n.length&&Af(e,n,s)}measureInitialState(){const{element:e,unresolvedKeyframes:s,name:n}=this;if(!e||!e.current)return;n==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ft[n](e.measureViewportBox(),window.getComputedStyle(e.current)),s[0]=this.measuredOrigin;const r=s[s.length-1];r!==void 0&&e.getValue(n,r).jump(r,!1)}measureEndState(){var e;const{element:s,name:n,unresolvedKeyframes:r}=this;if(!s||!s.current)return;const o=s.getValue(n);o&&o.jump(this.measuredOrigin,!1);const i=r.length-1,a=r[i];r[i]=Ft[n](s.measureViewportBox(),window.getComputedStyle(s.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([l,c])=>{s.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function wr(t){return typeof t=="function"}let js;function kf(){js=void 0}const Te={now:()=>(js===void 0&&Te.set($.isProcessing||_h.useManualTiming?$.timestamp:performance.now()),js),set:t=>{js=t,queueMicrotask(kf)}},ji=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Qe.test(t)||t==="0")&&!t.startsWith("url("));function Ef(t){const e=t[0];if(t.length===1)return!0;for(let s=0;s<t.length;s++)if(t[s]!==e)return!0}function Rf(t,e,s,n){const r=t[0];if(r===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],i=ji(r,e),a=ji(o,e);return!i||!a?!1:Ef(t)||(s==="spring"||wr(s))&&n}const Mf=40;class El{constructor({autoplay:e=!0,delay:s=0,type:n="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:i="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Te.now(),this.options={autoplay:e,delay:s,type:n,repeat:r,repeatDelay:o,repeatType:i,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Mf?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&lf(),this._resolved}onKeyframesResolved(e,s){this.resolvedAt=Te.now(),this.hasAttemptedResolve=!0;const{name:n,type:r,velocity:o,delay:i,onComplete:a,onUpdate:l,isGenerator:c}=this.options;if(!c&&!Rf(e,n,r,o))if(i)this.options.duration=0;else{l==null||l(Ws(e,this.options,s)),a==null||a(),this.resolveFinishedPromise();return}const u=this.initPlayback(e,s);u!==!1&&(this._resolved={keyframes:e,finalKeyframe:s,...u},this.onPostResolved())}onPostResolved(){}then(e,s){return this.currentFinishedPromise.then(e,s)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}const ht=(t,e,s)=>{const n=e-t;return n===0?1:(s-t)/n},Rl=(t,e,s=10)=>{let n="";const r=Math.max(Math.round(e/s),2);for(let o=0;o<r;o++)n+=t(ht(0,r-1,o))+", ";return`linear(${n.substring(0,n.length-2)})`};function Tr(t,e){return e?t*(1e3/e):0}const Nf=5;function Ml(t,e,s){const n=Math.max(e-Nf,0);return Tr(s-t(n),e-n)}const K={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},rn=.001;function Df({duration:t=K.duration,bounce:e=K.bounce,velocity:s=K.velocity,mass:n=K.mass}){let r,o,i=1-e;i=Ne(K.minDamping,K.maxDamping,i),t=Ne(K.minDuration,K.maxDuration,Re(t)),i<1?(r=c=>{const u=c*i,h=u*t,f=u-s,m=_n(c,i),v=Math.exp(-h);return rn-f/m*v},o=c=>{const h=c*i*t,f=h*s+s,m=Math.pow(i,2)*Math.pow(c,2)*t,v=Math.exp(-h),y=_n(Math.pow(c,2),i);return(-r(c)+rn>0?-1:1)*((f-m)*v)/y}):(r=c=>{const u=Math.exp(-c*t),h=(c-s)*t+1;return-rn+u*h},o=c=>{const u=Math.exp(-c*t),h=(s-c)*(t*t);return u*h});const a=5/t,l=Of(r,o,a);if(t=Ee(t),isNaN(l))return{stiffness:K.stiffness,damping:K.damping,duration:t};{const c=Math.pow(l,2)*n;return{stiffness:c,damping:i*2*Math.sqrt(n*c),duration:t}}}const Vf=12;function Of(t,e,s){let n=s;for(let r=1;r<Vf;r++)n=n-t(n)/e(n);return n}function _n(t,e){return t*Math.sqrt(1-e*e)}const zn=2e4;function Nl(t){let e=0;const s=50;let n=t.next(e);for(;!n.done&&e<zn;)e+=s,n=t.next(e);return e>=zn?1/0:e}const Lf=["duration","bounce"],If=["stiffness","damping","mass"];function Ai(t,e){return e.some(s=>t[s]!==void 0)}function Ff(t){let e={velocity:K.velocity,stiffness:K.stiffness,damping:K.damping,mass:K.mass,isResolvedFromDuration:!1,...t};if(!Ai(t,If)&&Ai(t,Lf))if(t.visualDuration){const s=t.visualDuration,n=2*Math.PI/(s*1.2),r=n*n,o=2*Ne(.05,1,1-t.bounce)*Math.sqrt(r);e={...e,mass:K.mass,stiffness:r,damping:o}}else{const s=Df(t);e={...e,...s,mass:K.mass},e.isResolvedFromDuration=!0}return e}function Dl(t=K.visualDuration,e=K.bounce){const s=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:n,restDelta:r}=s;const o=s.keyframes[0],i=s.keyframes[s.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:h,velocity:f,isResolvedFromDuration:m}=Ff({...s,velocity:-Re(s.velocity||0)}),v=f||0,y=c/(2*Math.sqrt(l*u)),x=i-o,b=Re(Math.sqrt(l/u)),w=Math.abs(x)<5;n||(n=w?K.restSpeed.granular:K.restSpeed.default),r||(r=w?K.restDelta.granular:K.restDelta.default);let T;if(y<1){const C=_n(b,y);T=M=>{const V=Math.exp(-y*b*M);return i-V*((v+y*b*x)/C*Math.sin(C*M)+x*Math.cos(C*M))}}else if(y===1)T=C=>i-Math.exp(-b*C)*(x+(v+b*x)*C);else{const C=b*Math.sqrt(y*y-1);T=M=>{const V=Math.exp(-y*b*M),j=Math.min(C*M,300);return i-V*((v+y*b*x)*Math.sinh(j)+C*x*Math.cosh(j))/C}}const A={calculatedDuration:m&&h||null,next:C=>{const M=T(C);if(m)a.done=C>=h;else{let V=0;y<1&&(V=C===0?Ee(v):Ml(T,C,M));const j=Math.abs(V)<=n,R=Math.abs(i-M)<=r;a.done=j&&R}return a.value=a.done?i:M,a},toString:()=>{const C=Math.min(Nl(A),zn),M=Rl(V=>A.next(C*V).value,C,30);return C+"ms "+M}};return A}function ki({keyframes:t,velocity:e=0,power:s=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:o=500,modifyTarget:i,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},m=j=>a!==void 0&&j<a||l!==void 0&&j>l,v=j=>a===void 0?l:l===void 0||Math.abs(a-j)<Math.abs(l-j)?a:l;let y=s*e;const x=h+y,b=i===void 0?x:i(x);b!==x&&(y=b-h);const w=j=>-y*Math.exp(-j/n),T=j=>b+w(j),A=j=>{const R=w(j),_=T(j);f.done=Math.abs(R)<=c,f.value=f.done?b:_};let C,M;const V=j=>{m(f.value)&&(C=j,M=Dl({keyframes:[f.value,v(f.value)],velocity:Ml(T,j,f.value),damping:r,stiffness:o,restDelta:c,restSpeed:u}))};return V(0),{calculatedDuration:null,next:j=>{let R=!1;return!M&&C===void 0&&(R=!0,A(j),V(j)),C!==void 0&&j>=C?M.next(j-C):(!R&&A(j),f)}}}const Bf=hs(.42,0,1,1),Uf=hs(0,0,.58,1),Vl=hs(.42,0,.58,1),_f=t=>Array.isArray(t)&&typeof t[0]!="number",Pr=t=>Array.isArray(t)&&typeof t[0]=="number",Ei={linear:Q,easeIn:Bf,easeInOut:Vl,easeOut:Uf,circIn:pr,circInOut:hl,circOut:dl,backIn:fr,backInOut:cl,backOut:ll,anticipate:ul},Ri=t=>{if(Pr(t)){Ln(t.length===4);const[e,s,n,r]=t;return hs(e,s,n,r)}else if(typeof t=="string")return Ln(Ei[t]!==void 0),Ei[t];return t},zf=(t,e)=>s=>e(t(s)),qe=(...t)=>t.reduce(zf),z=(t,e,s)=>t+(e-t)*s;function on(t,e,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?t+(e-t)*6*s:s<1/2?e:s<2/3?t+(e-t)*(2/3-s)*6:t}function Hf({hue:t,saturation:e,lightness:s,alpha:n}){t/=360,e/=100,s/=100;let r=0,o=0,i=0;if(!e)r=o=i=s;else{const a=s<.5?s*(1+e):s+e-s*e,l=2*s-a;r=on(l,a,t+1/3),o=on(l,a,t),i=on(l,a,t-1/3)}return{red:Math.round(r*255),green:Math.round(o*255),blue:Math.round(i*255),alpha:n}}function Ds(t,e){return s=>s>0?e:t}const an=(t,e,s)=>{const n=t*t,r=s*(e*e-n)+n;return r<0?0:Math.sqrt(r)},Kf=[Bn,tt,yt],Wf=t=>Kf.find(e=>e.test(t));function Mi(t){const e=Wf(t);if(!e)return!1;let s=e.parse(t);return e===yt&&(s=Hf(s)),s}const Ni=(t,e)=>{const s=Mi(t),n=Mi(e);if(!s||!n)return Ds(t,e);const r={...s};return o=>(r.red=an(s.red,n.red,o),r.green=an(s.green,n.green,o),r.blue=an(s.blue,n.blue,o),r.alpha=z(s.alpha,n.alpha,o),tt.transform(r))},Hn=new Set(["none","hidden"]);function Gf(t,e){return Hn.has(t)?s=>s<=0?t:e:s=>s>=1?e:t}function qf(t,e){return s=>z(t,e,s)}function Sr(t){return typeof t=="number"?qf:typeof t=="string"?mr(t)?Ds:Z.test(t)?Ni:Yf:Array.isArray(t)?Ol:typeof t=="object"?Z.test(t)?Ni:$f:Ds}function Ol(t,e){const s=[...t],n=s.length,r=t.map((o,i)=>Sr(o)(o,e[i]));return o=>{for(let i=0;i<n;i++)s[i]=r[i](o);return s}}function $f(t,e){const s={...t,...e},n={};for(const r in s)t[r]!==void 0&&e[r]!==void 0&&(n[r]=Sr(t[r])(t[r],e[r]));return r=>{for(const o in n)s[o]=n[o](r);return s}}function Qf(t,e){var s;const n=[],r={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const i=e.types[o],a=t.indexes[i][r[i]],l=(s=t.values[a])!==null&&s!==void 0?s:0;n[o]=l,r[i]++}return n}const Yf=(t,e)=>{const s=Qe.createTransformer(e),n=os(t),r=os(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?Hn.has(t)&&!r.values.length||Hn.has(e)&&!n.values.length?Gf(t,e):qe(Ol(Qf(n,r),r.values),s):Ds(t,e)};function Ll(t,e,s){return typeof t=="number"&&typeof e=="number"&&typeof s=="number"?z(t,e,s):Sr(t)(t,e)}function Xf(t,e,s){const n=[],r=s||Ll,o=t.length-1;for(let i=0;i<o;i++){let a=r(t[i],t[i+1]);if(e){const l=Array.isArray(e)?e[i]||Q:e;a=qe(l,a)}n.push(a)}return n}function Cr(t,e,{clamp:s=!0,ease:n,mixer:r}={}){const o=t.length;if(Ln(o===e.length),o===1)return()=>e[0];if(o===2&&t[0]===t[1])return()=>e[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const i=Xf(e,n,r),a=i.length,l=c=>{let u=0;if(a>1)for(;u<t.length-2&&!(c<t[u+1]);u++);const h=ht(t[u],t[u+1],c);return i[u](h)};return s?c=>l(Ne(t[0],t[o-1],c)):l}function Zf(t,e){const s=t[t.length-1];for(let n=1;n<=e;n++){const r=ht(0,e,n);t.push(z(s,1,r))}}function Il(t){const e=[0];return Zf(e,t.length-1),e}function Jf(t,e){return t.map(s=>s*e)}function ep(t,e){return t.map(()=>e||Vl).splice(0,t.length-1)}function Vs({duration:t=300,keyframes:e,times:s,ease:n="easeInOut"}){const r=_f(n)?n.map(Ri):Ri(n),o={done:!1,value:e[0]},i=Jf(s&&s.length===e.length?s:Il(e),t),a=Cr(i,e,{ease:Array.isArray(r)?r:ep(e,r)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const tp=t=>{const e=({timestamp:s})=>t(s);return{start:()=>L.update(e,!0),stop:()=>me(e),now:()=>$.isProcessing?$.timestamp:Te.now()}},sp={decay:ki,inertia:ki,tween:Vs,keyframes:Vs,spring:Dl},np=t=>t/100;class jr extends El{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:s,motionValue:n,element:r,keyframes:o}=this.options,i=(r==null?void 0:r.KeyframeResolver)||yr,a=(l,c)=>this.onKeyframesResolved(l,c);this.resolver=new i(o,a,s,n,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:i=0}=this.options,a=wr(s)?s:sp[s]||Vs;let l,c;a!==Vs&&typeof e[0]!="number"&&(l=qe(np,Ll(e[0],e[1])),e=[0,100]);const u=a({...this.options,keyframes:e});o==="mirror"&&(c=a({...this.options,keyframes:[...e].reverse(),velocity:-i})),u.calculatedDuration===null&&(u.calculatedDuration=Nl(u));const{calculatedDuration:h}=u,f=h+r,m=f*(n+1)-r;return{generator:u,mirroredGenerator:c,mapPercentToKeyframes:l,calculatedDuration:h,resolvedDuration:f,totalDuration:m}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,s=!1){const{resolved:n}=this;if(!n){const{keyframes:j}=this.options;return{done:!0,value:j[j.length-1]}}const{finalKeyframe:r,generator:o,mirroredGenerator:i,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:c,totalDuration:u,resolvedDuration:h}=n;if(this.startTime===null)return o.next(0);const{delay:f,repeat:m,repeatType:v,repeatDelay:y,onUpdate:x}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),s?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const b=this.currentTime-f*(this.speed>=0?1:-1),w=this.speed>=0?b<0:b>u;this.currentTime=Math.max(b,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let T=this.currentTime,A=o;if(m){const j=Math.min(this.currentTime,u)/h;let R=Math.floor(j),_=j%1;!_&&j>=1&&(_=1),_===1&&R--,R=Math.min(R,m+1),!!(R%2)&&(v==="reverse"?(_=1-_,y&&(_-=y/h)):v==="mirror"&&(A=i)),T=Ne(0,1,_)*h}const C=w?{done:!1,value:l[0]}:A.next(T);a&&(C.value=a(C.value));let{done:M}=C;!w&&c!==null&&(M=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const V=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&M);return V&&r!==void 0&&(C.value=Ws(l,this.options,r)),x&&x(C.value),V&&this.finish(),C}get duration(){const{resolved:e}=this;return e?Re(e.calculatedDuration):0}get time(){return Re(this.currentTime)}set time(e){e=Ee(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const s=this.playbackSpeed!==e;this.playbackSpeed=e,s&&(this.time=Re(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=tp,onPlay:s,startTime:n}=this.options;this.driver||(this.driver=e(o=>this.tick(o))),s&&s();const r=this.driver.now();this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=r):this.startTime=n??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const rp=new Set(["opacity","clipPath","filter","transform"]);function Ar(t){let e;return()=>(e===void 0&&(e=t()),e)}const ip={linearEasing:void 0};function op(t,e){const s=Ar(t);return()=>{var n;return(n=ip[e])!==null&&n!==void 0?n:s()}}const Os=op(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing");function Fl(t){return!!(typeof t=="function"&&Os()||!t||typeof t=="string"&&(t in Kn||Os())||Pr(t)||Array.isArray(t)&&t.every(Fl))}const Qt=([t,e,s,n])=>`cubic-bezier(${t}, ${e}, ${s}, ${n})`,Kn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Qt([0,.65,.55,1]),circOut:Qt([.55,0,1,.45]),backIn:Qt([.31,.01,.66,-.59]),backOut:Qt([.33,1.53,.69,.99])};function Bl(t,e){if(t)return typeof t=="function"&&Os()?Rl(t,e):Pr(t)?Qt(t):Array.isArray(t)?t.map(s=>Bl(s,e)||Kn.easeOut):Kn[t]}function ap(t,e,s,{delay:n=0,duration:r=300,repeat:o=0,repeatType:i="loop",ease:a="easeInOut",times:l}={}){const c={[e]:s};l&&(c.offset=l);const u=Bl(a,r);return Array.isArray(u)&&(c.easing=u),t.animate(c,{delay:n,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:o+1,direction:i==="reverse"?"alternate":"normal"})}function Di(t,e){t.timeline=e,t.onfinish=null}const lp=Ar(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Ls=10,cp=2e4;function up(t){return wr(t.type)||t.type==="spring"||!Fl(t.ease)}function dp(t,e){const s=new jr({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let n={done:!1,value:t[0]};const r=[];let o=0;for(;!n.done&&o<cp;)n=s.sample(o),r.push(n.value),o+=Ls;return{times:void 0,keyframes:r,duration:o-Ls,ease:"linear"}}const Ul={anticipate:ul,backInOut:cl,circInOut:hl};function hp(t){return t in Ul}class Vi extends El{constructor(e){super(e);const{name:s,motionValue:n,element:r,keyframes:o}=this.options;this.resolver=new kl(o,(i,a)=>this.onKeyframesResolved(i,a),s,n,r),this.resolver.scheduleResolve()}initPlayback(e,s){var n;let{duration:r=300,times:o,ease:i,type:a,motionValue:l,name:c,startTime:u}=this.options;if(!(!((n=l.owner)===null||n===void 0)&&n.current))return!1;if(typeof i=="string"&&Os()&&hp(i)&&(i=Ul[i]),up(this.options)){const{onComplete:f,onUpdate:m,motionValue:v,element:y,...x}=this.options,b=dp(e,x);e=b.keyframes,e.length===1&&(e[1]=e[0]),r=b.duration,o=b.times,i=b.ease,a="keyframes"}const h=ap(l.owner.current,c,e,{...this.options,duration:r,times:o,ease:i});return h.startTime=u??this.calcStartTime(),this.pendingTimeline?(Di(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{const{onComplete:f}=this.options;l.set(Ws(e,this.options,s)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:r,times:o,type:a,ease:i,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:s}=e;return Re(s)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:s}=e;return Re(s.currentTime||0)}set time(e){const{resolved:s}=this;if(!s)return;const{animation:n}=s;n.currentTime=Ee(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:s}=e;return s.playbackRate}set speed(e){const{resolved:s}=this;if(!s)return;const{animation:n}=s;n.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:s}=e;return s.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:s}=e;return s.startTime}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:s}=this;if(!s)return Q;const{animation:n}=s;Di(n,e)}return Q}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:s}=e;s.playState==="finished"&&this.updateFinishedPromise(),s.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:s}=e;s.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:s,keyframes:n,duration:r,type:o,ease:i,times:a}=e;if(s.playState==="idle"||s.playState==="finished")return;if(this.time){const{motionValue:c,onUpdate:u,onComplete:h,element:f,...m}=this.options,v=new jr({...m,keyframes:n,duration:r,type:o,ease:i,times:a,isGenerator:!0}),y=Ee(this.time);c.setWithVelocity(v.sample(y-Ls).value,v.sample(y).value,Ls)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:s,name:n,repeatDelay:r,repeatType:o,damping:i,type:a}=e;return lp()&&n&&rp.has(n)&&s&&s.owner&&s.owner.current instanceof HTMLElement&&!s.owner.getProps().onUpdate&&!r&&o!=="mirror"&&i!==0&&a!=="inertia"}}const _l=Ar(()=>window.ScrollTimeline!==void 0);class fp{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}then(e,s){return Promise.all(this.animations).then(e).catch(s)}getAll(e){return this.animations[0][e]}setAll(e,s){for(let n=0;n<this.animations.length;n++)this.animations[n][e]=s}attachTimeline(e,s){const n=this.animations.map(r=>_l()&&r.attachTimeline?r.attachTimeline(e):s(r));return()=>{n.forEach((r,o)=>{r&&r(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let s=0;s<this.animations.length;s++)e=Math.max(e,this.animations[s].duration);return e}runAll(e){this.animations.forEach(s=>s[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function pp({when:t,delay:e,delayChildren:s,staggerChildren:n,staggerDirection:r,repeat:o,repeatType:i,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const kr=(t,e,s,n={},r,o)=>i=>{const a=hr(n,t)||{},l=a.delay||n.delay||0;let{elapsed:c=0}=n;c=c-Ee(l);let u={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{i(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:r};pp(a)||(u={...u,...Uh(t,u)}),u.duration&&(u.duration=Ee(u.duration)),u.repeatDelay&&(u.repeatDelay=Ee(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),h&&!o&&e.get()!==void 0){const f=Ws(u.keyframes,a);if(f!==void 0)return L.update(()=>{u.onUpdate(f),u.onComplete()}),new fp([])}return!o&&Vi.supports(u)?new Vi(u):new jr(u)},mp=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),yp=t=>On(t)?t[t.length-1]||0:t;function Er(t,e){t.indexOf(e)===-1&&t.push(e)}function Rr(t,e){const s=t.indexOf(e);s>-1&&t.splice(s,1)}class Mr{constructor(){this.subscriptions=[]}add(e){return Er(this.subscriptions,e),()=>Rr(this.subscriptions,e)}notify(e,s,n){const r=this.subscriptions.length;if(r)if(r===1)this.subscriptions[0](e,s,n);else for(let o=0;o<r;o++){const i=this.subscriptions[o];i&&i(e,s,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Oi=30,gp=t=>!isNaN(parseFloat(t)),Jt={current:void 0};class xp{constructor(e,s={}){this.version="11.13.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(n,r=!0)=>{const o=Te.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(n),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),r&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=s.owner}setCurrent(e){this.current=e,this.updatedAt=Te.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=gp(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,s){this.events[e]||(this.events[e]=new Mr);const n=this.events[e].add(s);return e==="change"?()=>{n(),L.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,s){this.passiveEffect=e,this.stopPassiveEffect=s}set(e,s=!0){!s||!this.passiveEffect?this.updateAndNotify(e,s):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,s,n){this.set(s),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,s=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return Jt.current&&Jt.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const e=Te.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Oi)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Oi);return Tr(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(e){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=e(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function be(t,e){return new xp(t,e)}function vp(t,e,s){t.hasValue(e)?t.getValue(e).set(s):t.addValue(e,be(s))}function bp(t,e){const s=Ks(t,e);let{transitionEnd:n={},transition:r={},...o}=s||{};o={...o,...n};for(const i in o){const a=yp(o[i]);vp(t,i,a)}}const Nr=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),wp="framerAppearId",zl="data-"+Nr(wp);function Hl(t){return t.props[zl]}const J=t=>!!(t&&t.getVelocity);function Tp(t){return!!(J(t)&&t.add)}function Wn(t,e){const s=t.getValue("willChange");if(Tp(s))return s.add(e)}function Pp({protectedKeys:t,needsAnimating:e},s){const n=t.hasOwnProperty(s)&&e[s]!==!0;return e[s]=!1,n}function Kl(t,e,{delay:s=0,transitionOverride:n,type:r}={}){var o;let{transition:i=t.getDefaultTransition(),transitionEnd:a,...l}=e;n&&(i=n);const c=[],u=r&&t.animationState&&t.animationState.getState()[r];for(const h in l){const f=t.getValue(h,(o=t.latestValues[h])!==null&&o!==void 0?o:null),m=l[h];if(m===void 0||u&&Pp(u,h))continue;const v={delay:s,...hr(i||{},h)};let y=!1;if(window.MotionHandoffAnimation){const b=Hl(t);if(b){const w=window.MotionHandoffAnimation(b,h,L);w!==null&&(v.startTime=w,y=!0)}}Wn(t,h),f.start(kr(h,f,m,t.shouldReduceMotion&&ft.has(h)?{type:!1}:v,t,y));const x=f.animation;x&&c.push(x)}return a&&Promise.all(c).then(()=>{L.update(()=>{a&&bp(t,a)})}),c}function Gn(t,e,s={}){var n;const r=Ks(t,e,s.type==="exit"?(n=t.presenceContext)===null||n===void 0?void 0:n.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=r||{};s.transitionOverride&&(o=s.transitionOverride);const i=r?()=>Promise.all(Kl(t,r,s)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:f}=o;return Sp(t,e,u+c,h,f,s)}:()=>Promise.resolve(),{when:l}=o;if(l){const[c,u]=l==="beforeChildren"?[i,a]:[a,i];return c().then(()=>u())}else return Promise.all([i(),a(s.delay)])}function Sp(t,e,s=0,n=0,r=1,o){const i=[],a=(t.variantChildren.size-1)*n,l=r===1?(c=0)=>c*n:(c=0)=>a-c*n;return Array.from(t.variantChildren).sort(Cp).forEach((c,u)=>{c.notify("AnimationStart",e),i.push(Gn(c,e,{...o,delay:s+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(i)}function Cp(t,e){return t.sortNodePosition(e)}function jp(t,e,s={}){t.notify("AnimationStart",e);let n;if(Array.isArray(e)){const r=e.map(o=>Gn(t,o,s));n=Promise.all(r)}else if(typeof e=="string")n=Gn(t,e,s);else{const r=typeof e=="function"?Ks(t,e,s.custom):e;n=Promise.all(Kl(t,r,s))}return n.then(()=>{t.notify("AnimationComplete",e)})}const Ap=dr.length;function Wl(t){if(!t)return;if(!t.isControllingVariants){const s=t.parent?Wl(t.parent)||{}:{};return t.props.initial!==void 0&&(s.initial=t.props.initial),s}const e={};for(let s=0;s<Ap;s++){const n=dr[s],r=t.props[n];(rs(r)||r===!1)&&(e[n]=r)}return e}const kp=[...ur].reverse(),Ep=ur.length;function Rp(t){return e=>Promise.all(e.map(({animation:s,options:n})=>jp(t,s,n)))}function Mp(t){let e=Rp(t),s=Li(),n=!0;const r=l=>(c,u)=>{var h;const f=Ks(t,u,l==="exit"?(h=t.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:m,transitionEnd:v,...y}=f;c={...c,...y,...v}}return c};function o(l){e=l(t)}function i(l){const{props:c}=t,u=Wl(t.parent)||{},h=[],f=new Set;let m={},v=1/0;for(let x=0;x<Ep;x++){const b=kp[x],w=s[b],T=c[b]!==void 0?c[b]:u[b],A=rs(T),C=b===l?w.isActive:null;C===!1&&(v=x);let M=T===u[b]&&T!==c[b]&&A;if(M&&n&&t.manuallyAnimateOnMount&&(M=!1),w.protectedKeys={...m},!w.isActive&&C===null||!T&&!w.prevProp||Hs(T)||typeof T=="boolean")continue;const V=Np(w.prevProp,T);let j=V||b===l&&w.isActive&&!M&&A||x>v&&A,R=!1;const _=Array.isArray(T)?T:[T];let Y=_.reduce(r(b),{});C===!1&&(Y={});const{prevResolvedValues:I={}}=w,ys={...I,...Y},Xe=q=>{j=!0,f.has(q)&&(R=!0,f.delete(q)),w.needsAnimating[q]=!0;const re=t.getValue(q);re&&(re.liveStyle=!1)};for(const q in ys){const re=Y[q],Pe=I[q];if(m.hasOwnProperty(q))continue;let pt=!1;On(re)&&On(Pe)?pt=!nl(re,Pe):pt=re!==Pe,pt?re!=null?Xe(q):f.add(q):re!==void 0&&f.has(q)?Xe(q):w.protectedKeys[q]=!0}w.prevProp=T,w.prevResolvedValues=Y,w.isActive&&(m={...m,...Y}),n&&t.blockInitialAnimation&&(j=!1),j&&(!(M&&V)||R)&&h.push(..._.map(q=>({animation:q,options:{type:b}})))}if(f.size){const x={};f.forEach(b=>{const w=t.getBaseTarget(b),T=t.getValue(b);T&&(T.liveStyle=!0),x[b]=w??null}),h.push({animation:x})}let y=!!h.length;return n&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(h):Promise.resolve()}function a(l,c){var u;if(s[l].isActive===c)return Promise.resolve();(u=t.variantChildren)===null||u===void 0||u.forEach(f=>{var m;return(m=f.animationState)===null||m===void 0?void 0:m.setActive(l,c)}),s[l].isActive=c;const h=i(l);for(const f in s)s[f].protectedKeys={};return h}return{animateChanges:i,setActive:a,setAnimateFunction:o,getState:()=>s,reset:()=>{s=Li(),n=!0}}}function Np(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!nl(e,t):!1}function Ze(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Li(){return{animate:Ze(!0),whileInView:Ze(),whileHover:Ze(),whileTap:Ze(),whileDrag:Ze(),whileFocus:Ze(),exit:Ze()}}class Ye{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Dp extends Ye{constructor(e){super(e),e.animationState||(e.animationState=Mp(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Hs(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:s}=this.node.prevProps||{};e!==s&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)===null||e===void 0||e.call(this)}}let Vp=0;class Op extends Ye{constructor(){super(...arguments),this.id=Vp++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:s}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;const r=this.node.animationState.setActive("exit",!e);s&&!e&&r.then(()=>s(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const Lp={animation:{Feature:Dp},exit:{Feature:Op}};function Dr(t,e,s){var n;if(t instanceof Element)return[t];if(typeof t=="string"){let r=document;const o=(n=void 0)!==null&&n!==void 0?n:r.querySelectorAll(t);return o?Array.from(o):[]}return Array.from(t)}const he={x:!1,y:!1};function Gl(){return he.x||he.y}function Ii(t){return e=>{e.pointerType==="touch"||Gl()||t(e)}}function Ip(t,e,s={}){const n=new AbortController,r={passive:!0,...s,signal:n.signal},o=Ii(i=>{const{target:a}=i,l=e(i);if(!l||!a)return;const c=Ii(u=>{l(u),a.removeEventListener("pointerleave",c)});a.addEventListener("pointerleave",c,r)});return Dr(t).forEach(i=>{i.addEventListener("pointerenter",o,r)}),()=>n.abort()}function Fp(t){return t==="x"||t==="y"?he[t]?null:(he[t]=!0,()=>{he[t]=!1}):he.x||he.y?null:(he.x=he.y=!0,()=>{he.x=he.y=!1})}const ql=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function ps(t){return{point:{x:t.pageX,y:t.pageY}}}const Bp=t=>e=>ql(e)&&t(e,ps(e));function ke(t,e,s,n={passive:!0}){return t.addEventListener(e,s,n),()=>t.removeEventListener(e,s)}function $e(t,e,s,n){return ke(t,e,Bp(s),n)}const Fi=(t,e)=>Math.abs(t-e);function Up(t,e){const s=Fi(t.x,e.x),n=Fi(t.y,e.y);return Math.sqrt(s**2+n**2)}class $l{constructor(e,s,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=cn(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=Up(h.offset,{x:0,y:0})>=3;if(!f&&!m)return;const{point:v}=h,{timestamp:y}=$;this.history.push({...v,timestamp:y});const{onStart:x,onMove:b}=this.handlers;f||(x&&x(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),b&&b(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=ln(f,this.transformPagePoint),L.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:m,onSessionEnd:v,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=cn(h.type==="pointercancel"?this.lastMoveEventInfo:ln(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(h,x),v&&v(h,x)},!ql(e))return;this.dragSnapToOrigin=o,this.handlers=s,this.transformPagePoint=n,this.contextWindow=r||window;const i=ps(e),a=ln(i,this.transformPagePoint),{point:l}=a,{timestamp:c}=$;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=s;u&&u(e,cn(a,this.history)),this.removeListeners=qe($e(this.contextWindow,"pointermove",this.handlePointerMove),$e(this.contextWindow,"pointerup",this.handlePointerUp),$e(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),me(this.updatePoint)}}function ln(t,e){return e?{point:e(t.point)}:t}function Bi(t,e){return{x:t.x-e.x,y:t.y-e.y}}function cn({point:t},e){return{point:t,delta:Bi(t,Ql(e)),offset:Bi(t,_p(e)),velocity:zp(e,.1)}}function _p(t){return t[0]}function Ql(t){return t[t.length-1]}function zp(t,e){if(t.length<2)return{x:0,y:0};let s=t.length-1,n=null;const r=Ql(t);for(;s>=0&&(n=t[s],!(r.timestamp-n.timestamp>Ee(e)));)s--;if(!n)return{x:0,y:0};const o=Re(r.timestamp-n.timestamp);if(o===0)return{x:0,y:0};const i={x:(r.x-n.x)/o,y:(r.y-n.y)/o};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function gt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}const Yl=1e-4,Hp=1-Yl,Kp=1+Yl,Xl=.01,Wp=0-Xl,Gp=0+Xl;function oe(t){return t.max-t.min}function qp(t,e,s){return Math.abs(t-e)<=s}function Ui(t,e,s,n=.5){t.origin=n,t.originPoint=z(e.min,e.max,t.origin),t.scale=oe(s)/oe(e),t.translate=z(s.min,s.max,t.origin)-t.originPoint,(t.scale>=Hp&&t.scale<=Kp||isNaN(t.scale))&&(t.scale=1),(t.translate>=Wp&&t.translate<=Gp||isNaN(t.translate))&&(t.translate=0)}function es(t,e,s,n){Ui(t.x,e.x,s.x,n?n.originX:void 0),Ui(t.y,e.y,s.y,n?n.originY:void 0)}function _i(t,e,s){t.min=s.min+e.min,t.max=t.min+oe(e)}function $p(t,e,s){_i(t.x,e.x,s.x),_i(t.y,e.y,s.y)}function zi(t,e,s){t.min=e.min-s.min,t.max=t.min+oe(e)}function ts(t,e,s){zi(t.x,e.x,s.x),zi(t.y,e.y,s.y)}function Qp(t,{min:e,max:s},n){return e!==void 0&&t<e?t=n?z(e,t,n.min):Math.max(t,e):s!==void 0&&t>s&&(t=n?z(s,t,n.max):Math.min(t,s)),t}function Hi(t,e,s){return{min:e!==void 0?t.min+e:void 0,max:s!==void 0?t.max+s-(t.max-t.min):void 0}}function Yp(t,{top:e,left:s,bottom:n,right:r}){return{x:Hi(t.x,s,r),y:Hi(t.y,e,n)}}function Ki(t,e){let s=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([s,n]=[n,s]),{min:s,max:n}}function Xp(t,e){return{x:Ki(t.x,e.x),y:Ki(t.y,e.y)}}function Zp(t,e){let s=.5;const n=oe(t),r=oe(e);return r>n?s=ht(e.min,e.max-n,t.min):n>r&&(s=ht(t.min,t.max-r,e.min)),Ne(0,1,s)}function Jp(t,e){const s={};return e.min!==void 0&&(s.min=e.min-t.min),e.max!==void 0&&(s.max=e.max-t.min),s}const qn=.35;function em(t=qn){return t===!1?t=0:t===!0&&(t=qn),{x:Wi(t,"left","right"),y:Wi(t,"top","bottom")}}function Wi(t,e,s){return{min:Gi(t,e),max:Gi(t,s)}}function Gi(t,e){return typeof t=="number"?t:t[e]||0}const qi=()=>({translate:0,scale:1,origin:0,originPoint:0}),xt=()=>({x:qi(),y:qi()}),$i=()=>({min:0,max:0}),W=()=>({x:$i(),y:$i()});function ce(t){return[t("x"),t("y")]}function Zl({top:t,left:e,right:s,bottom:n}){return{x:{min:e,max:s},y:{min:t,max:n}}}function tm({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function sm(t,e){if(!e)return t;const s=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:s.y,left:s.x,bottom:n.y,right:n.x}}function un(t){return t===void 0||t===1}function $n({scale:t,scaleX:e,scaleY:s}){return!un(t)||!un(e)||!un(s)}function Je(t){return $n(t)||Jl(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Jl(t){return Qi(t.x)||Qi(t.y)}function Qi(t){return t&&t!=="0%"}function Is(t,e,s){const n=t-s,r=e*n;return s+r}function Yi(t,e,s,n,r){return r!==void 0&&(t=Is(t,r,n)),Is(t,s,n)+e}function Qn(t,e=0,s=1,n,r){t.min=Yi(t.min,e,s,n,r),t.max=Yi(t.max,e,s,n,r)}function ec(t,{x:e,y:s}){Qn(t.x,e.translate,e.scale,e.originPoint),Qn(t.y,s.translate,s.scale,s.originPoint)}const Xi=.999999999999,Zi=1.0000000000001;function nm(t,e,s,n=!1){const r=s.length;if(!r)return;e.x=e.y=1;let o,i;for(let a=0;a<r;a++){o=s[a],i=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(n&&o.options.layoutScroll&&o.scroll&&o!==o.root&&bt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),i&&(e.x*=i.x.scale,e.y*=i.y.scale,ec(t,i)),n&&Je(o.latestValues)&&bt(t,o.latestValues))}e.x<Zi&&e.x>Xi&&(e.x=1),e.y<Zi&&e.y>Xi&&(e.y=1)}function vt(t,e){t.min=t.min+e,t.max=t.max+e}function Ji(t,e,s,n,r=.5){const o=z(t.min,t.max,r);Qn(t,e,s,o,n)}function bt(t,e){Ji(t.x,e.x,e.scaleX,e.scale,e.originX),Ji(t.y,e.y,e.scaleY,e.scale,e.originY)}function tc(t,e){return Zl(sm(t.getBoundingClientRect(),e))}function rm(t,e,s){const n=tc(t,s),{scroll:r}=e;return r&&(vt(n.x,r.offset.x),vt(n.y,r.offset.y)),n}const sc=({current:t})=>t?t.ownerDocument.defaultView:null,im=new WeakMap;class om{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=W(),this.visualElement=e}start(e,{snapToCursor:s=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&n.isPresent===!1)return;const r=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(ps(u).point)},o=(u,h)=>{const{drag:f,dragPropagation:m,onDragStart:v}=this.getProps();if(f&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Fp(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ce(x=>{let b=this.getAxisMotionValue(x).get()||0;if(we.test(b)){const{projection:w}=this.visualElement;if(w&&w.layout){const T=w.layout.layoutBox[x];T&&(b=oe(T)*(parseFloat(b)/100))}}this.originPoint[x]=b}),v&&L.postRender(()=>v(u,h)),Wn(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},i=(u,h)=>{const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:v,onDrag:y}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:x}=h;if(m&&this.currentDirection===null){this.currentDirection=am(x),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",h.point,x),this.updateAxis("y",h.point,x),this.visualElement.render(),y&&y(u,h)},a=(u,h)=>this.stop(u,h),l=()=>ce(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new $l(e,{onSessionStart:r,onStart:o,onMove:i,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:sc(this.visualElement)})}stop(e,s){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=s;this.startAnimation(r);const{onDragEnd:o}=this.getProps();o&&L.postRender(()=>o(e,s))}cancel(){this.isDragging=!1;const{projection:e,animationState:s}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(e,s,n){const{drag:r}=this.getProps();if(!n||!ws(e,r,this.currentDirection))return;const o=this.getAxisMotionValue(e);let i=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(i=Qp(i,this.constraints[e],this.elastic[e])),o.set(i)}resolveConstraints(){var e;const{dragConstraints:s,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,o=this.constraints;s&&gt(s)?this.constraints||(this.constraints=this.resolveRefConstraints()):s&&r?this.constraints=Yp(r.layoutBox,s):this.constraints=!1,this.elastic=em(n),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&ce(i=>{this.constraints!==!1&&this.getAxisMotionValue(i)&&(this.constraints[i]=Jp(r.layoutBox[i],this.constraints[i]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:s}=this.getProps();if(!e||!gt(e))return!1;const n=e.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const o=rm(n,r.root,this.visualElement.getTransformPagePoint());let i=Xp(r.layout.layoutBox,o);if(s){const a=s(tm(i));this.hasMutatedConstraints=!!a,a&&(i=Zl(a))}return i}startAnimation(e){const{drag:s,dragMomentum:n,dragElastic:r,dragTransition:o,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=ce(u=>{if(!ws(u,s,this.currentDirection))return;let h=l&&l[u]||{};i&&(h={min:0,max:0});const f=r?200:1e6,m=r?40:1e7,v={type:"inertia",velocity:n?e[u]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,v)});return Promise.all(c).then(a)}startAxisValueAnimation(e,s){const n=this.getAxisMotionValue(e);return Wn(this.visualElement,e),n.start(kr(e,n,0,s,this.visualElement,!1))}stopAnimation(){ce(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){ce(e=>{var s;return(s=this.getAxisMotionValue(e).animation)===null||s===void 0?void 0:s.pause()})}getAnimationState(e){var s;return(s=this.getAxisMotionValue(e).animation)===null||s===void 0?void 0:s.state}getAxisMotionValue(e){const s=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps(),r=n[s];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){ce(s=>{const{drag:n}=this.getProps();if(!ws(s,n,this.currentDirection))return;const{projection:r}=this.visualElement,o=this.getAxisMotionValue(s);if(r&&r.layout){const{min:i,max:a}=r.layout.layoutBox[s];o.set(e[s]-z(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:s}=this.getProps(),{projection:n}=this.visualElement;if(!gt(s)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};ce(i=>{const a=this.getAxisMotionValue(i);if(a&&this.constraints!==!1){const l=a.get();r[i]=Zp({min:l,max:l},this.constraints[i])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),ce(i=>{if(!ws(i,e,null))return;const a=this.getAxisMotionValue(i),{min:l,max:c}=this.constraints[i];a.set(z(l,c,r[i]))})}addListeners(){if(!this.visualElement.current)return;im.set(this.visualElement,this);const e=this.visualElement.current,s=$e(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),n=()=>{const{dragConstraints:l}=this.getProps();gt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,o=r.addEventListener("measure",n);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),L.read(n);const i=ke(window,"resize",()=>this.scalePositionWithinConstraints()),a=r.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(ce(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{i(),s(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:o=!1,dragElastic:i=qn,dragMomentum:a=!0}=e;return{...e,drag:s,dragDirectionLock:n,dragPropagation:r,dragConstraints:o,dragElastic:i,dragMomentum:a}}}function ws(t,e,s){return(e===!0||e===t)&&(s===null||s===t)}function am(t,e=10){let s=null;return Math.abs(t.y)>e?s="y":Math.abs(t.x)>e&&(s="x"),s}class lm extends Ye{constructor(e){super(e),this.removeGroupControls=Q,this.removeListeners=Q,this.controls=new om(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Q}unmount(){this.removeGroupControls(),this.removeListeners()}}const eo=t=>(e,s)=>{t&&L.postRender(()=>t(e,s))};class cm extends Ye{constructor(){super(...arguments),this.removePointerDownListener=Q}onPointerDown(e){this.session=new $l(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:sc(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:s,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:eo(e),onStart:eo(s),onMove:n,onEnd:(o,i)=>{delete this.session,r&&L.postRender(()=>r(o,i))}}}mount(){this.removePointerDownListener=$e(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Vr=g.createContext(null);function um(){const t=g.useContext(Vr);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:s,register:n}=t,r=g.useId();g.useEffect(()=>n(r),[]);const o=g.useCallback(()=>s&&s(r),[r,s]);return!e&&s?[!1,o]:[!0]}const nc=g.createContext({}),rc=g.createContext({}),As={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function to(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Gt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(E.test(t))t=parseFloat(t);else return t;const s=to(t,e.target.x),n=to(t,e.target.y);return`${s}% ${n}%`}},dm={correct:(t,{treeScale:e,projectionDelta:s})=>{const n=t,r=Qe.parse(t);if(r.length>5)return n;const o=Qe.createTransformer(t),i=typeof r[0]!="number"?1:0,a=s.x.scale*e.x,l=s.y.scale*e.y;r[0+i]/=a,r[1+i]/=l;const c=z(a,l,.5);return typeof r[2+i]=="number"&&(r[2+i]/=c),typeof r[3+i]=="number"&&(r[3+i]/=c),o(r)}},Fs={};function hm(t){Object.assign(Fs,t)}const{schedule:Or,cancel:Xg}=rl(queueMicrotask,!1);class fm extends g.Component{componentDidMount(){const{visualElement:e,layoutGroup:s,switchLayoutGroup:n,layoutId:r}=this.props,{projection:o}=e;hm(pm),o&&(s.group&&s.group.add(o),n&&n.register&&r&&n.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),As.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:s,visualElement:n,drag:r,isPresent:o}=this.props,i=n.projection;return i&&(i.isPresent=o,r||e.layoutDependency!==s||s===void 0?i.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?i.promote():i.relegate()||L.postRender(()=>{const a=i.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Or.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:s,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ic(t){const[e,s]=um(),n=g.useContext(nc);return d.jsx(fm,{...t,layoutGroup:n,switchLayoutGroup:g.useContext(rc),isPresent:e,safeToRemove:s})}const pm={borderRadius:{...Gt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Gt,borderTopRightRadius:Gt,borderBottomLeftRadius:Gt,borderBottomRightRadius:Gt,boxShadow:dm},oc=["TopLeft","TopRight","BottomLeft","BottomRight"],mm=oc.length,so=t=>typeof t=="string"?parseFloat(t):t,no=t=>typeof t=="number"||E.test(t);function ym(t,e,s,n,r,o){r?(t.opacity=z(0,s.opacity!==void 0?s.opacity:1,gm(n)),t.opacityExit=z(e.opacity!==void 0?e.opacity:1,0,xm(n))):o&&(t.opacity=z(e.opacity!==void 0?e.opacity:1,s.opacity!==void 0?s.opacity:1,n));for(let i=0;i<mm;i++){const a=`border${oc[i]}Radius`;let l=ro(e,a),c=ro(s,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||no(l)===no(c)?(t[a]=Math.max(z(so(l),so(c),n),0),(we.test(c)||we.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||s.rotate)&&(t.rotate=z(e.rotate||0,s.rotate||0,n))}function ro(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const gm=ac(0,.5,dl),xm=ac(.5,.95,Q);function ac(t,e,s){return n=>n<t?0:n>e?1:s(ht(t,e,n))}function io(t,e){t.min=e.min,t.max=e.max}function le(t,e){io(t.x,e.x),io(t.y,e.y)}function oo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ao(t,e,s,n,r){return t-=e,t=Is(t,1/s,n),r!==void 0&&(t=Is(t,1/r,n)),t}function vm(t,e=0,s=1,n=.5,r,o=t,i=t){if(we.test(e)&&(e=parseFloat(e),e=z(i.min,i.max,e/100)-i.min),typeof e!="number")return;let a=z(o.min,o.max,n);t===o&&(a-=e),t.min=ao(t.min,e,s,a,r),t.max=ao(t.max,e,s,a,r)}function lo(t,e,[s,n,r],o,i){vm(t,e[s],e[n],e[r],e.scale,o,i)}const bm=["x","scaleX","originX"],wm=["y","scaleY","originY"];function co(t,e,s,n){lo(t.x,e,bm,s?s.x:void 0,n?n.x:void 0),lo(t.y,e,wm,s?s.y:void 0,n?n.y:void 0)}function uo(t){return t.translate===0&&t.scale===1}function lc(t){return uo(t.x)&&uo(t.y)}function ho(t,e){return t.min===e.min&&t.max===e.max}function Tm(t,e){return ho(t.x,e.x)&&ho(t.y,e.y)}function fo(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function cc(t,e){return fo(t.x,e.x)&&fo(t.y,e.y)}function po(t){return oe(t.x)/oe(t.y)}function mo(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Pm{constructor(){this.members=[]}add(e){Er(this.members,e),e.scheduleRender()}remove(e){if(Rr(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(e){const s=this.members.findIndex(r=>e===r);if(s===0)return!1;let n;for(let r=s;r>=0;r--){const o=this.members[r];if(o.isPresent!==!1){n=o;break}}return n?(this.promote(n),!0):!1}promote(e,s){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,s&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;r===!1&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:s,resumingFrom:n}=e;s.onExitComplete&&s.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Sm(t,e,s){let n="";const r=t.x.translate/e.x,o=t.y.translate/e.y,i=(s==null?void 0:s.z)||0;if((r||o||i)&&(n=`translate3d(${r}px, ${o}px, ${i}px) `),(e.x!==1||e.y!==1)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),s){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:f,skewX:m,skewY:v}=s;c&&(n=`perspective(${c}px) ${n}`),u&&(n+=`rotate(${u}deg) `),h&&(n+=`rotateX(${h}deg) `),f&&(n+=`rotateY(${f}deg) `),m&&(n+=`skewX(${m}deg) `),v&&(n+=`skewY(${v}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(n+=`scale(${a}, ${l})`),n||"none"}const Cm=(t,e)=>t.depth-e.depth;class jm{constructor(){this.children=[],this.isDirty=!1}add(e){Er(this.children,e),this.isDirty=!0}remove(e){Rr(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Cm),this.isDirty=!1,this.children.forEach(e)}}function ks(t){const e=J(t)?t.get():t;return mp(e)?e.toValue():e}function Am(t,e){const s=Te.now(),n=({timestamp:r})=>{const o=r-s;o>=e&&(me(n),t(o-e))};return L.read(n,!0),()=>me(n)}function km(t){return t instanceof SVGElement&&t.tagName!=="svg"}function Em(t,e,s){const n=J(t)?t:be(t);return n.start(kr("",n,e,s)),n.animation}const et={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Yt=typeof window<"u"&&window.MotionDebug!==void 0,dn=["","X","Y","Z"],Rm={visibility:"hidden"},yo=1e3;let Mm=0;function hn(t,e,s,n){const{latestValues:r}=e;r[t]&&(s[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function uc(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const s=Hl(e);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:r,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(s,"transform",L,!(r||o))}const{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&uc(n)}function dc({attachResizeListener:t,defaultParent:e,measureScroll:s,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(i={},a=e==null?void 0:e()){this.id=Mm++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Yt&&(et.totalNodes=et.resolvedTargetDeltas=et.recalculatedProjection=0),this.nodes.forEach(Vm),this.nodes.forEach(Bm),this.nodes.forEach(Um),this.nodes.forEach(Om),Yt&&window.MotionDebug.record(et)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new jm)}addEventListener(i,a){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new Mr),this.eventHandlers.get(i).add(a)}notifyListeners(i,...a){const l=this.eventHandlers.get(i);l&&l.notify(...a)}hasListeners(i){return this.eventHandlers.has(i)}mount(i,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=km(i),this.instance=i;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(i,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Am(f,250),As.hasAnimatedSinceResize&&(As.hasAnimatedSinceResize=!1,this.nodes.forEach(xo))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:m,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||u.getDefaultTransition()||Wm,{onLayoutAnimationStart:x,onLayoutAnimationComplete:b}=u.getProps(),w=!this.targetLayout||!cc(this.targetLayout,v)||m,T=!f&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||T||f&&(w||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,T);const A={...hr(y,"layout"),onPlay:x,onComplete:b};(u.shouldReduceMotion||this.options.layoutRoot)&&(A.delay=0,A.type=!1),this.startAnimation(A)}else f||xo(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,me(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(_m),this.animationId++)}getTransformTemplate(){const{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&uc(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(go);return}this.isUpdating||this.nodes.forEach(Im),this.isUpdating=!1,this.nodes.forEach(Fm),this.nodes.forEach(Nm),this.nodes.forEach(Dm),this.clearAllSnapshots();const a=Te.now();$.delta=Ne(0,1e3/60,a-$.timestamp),$.timestamp=a,$.isProcessing=!0,sn.update.process($),sn.preRender.process($),sn.render.process($),$.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Or.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Lm),this.sharedNodes.forEach(zm)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,L.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){L.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=W(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(a=!1),a){const l=n(this.instance);this.scroll={animationId:this.root.animationId,phase:i,isRoot:l,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!r)return;const i=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!lc(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;i&&(a||Je(this.latestValues)||u)&&(r(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return i&&(l=this.removeTransform(l)),Gm(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var i;const{visualElement:a}=this.options;if(!a)return W();const l=a.measureViewportBox();if(!(((i=this.scroll)===null||i===void 0?void 0:i.wasRoot)||this.path.some(qm))){const{scroll:u}=this.root;u&&(vt(l.x,u.offset.x),vt(l.y,u.offset.y))}return l}removeElementScroll(i){var a;const l=W();if(le(l,i),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:f}=u;u!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&le(l,i),vt(l.x,h.offset.x),vt(l.y,h.offset.y))}return l}applyTransform(i,a=!1){const l=W();le(l,i);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&bt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),Je(u.latestValues)&&bt(l,u.latestValues)}return Je(this.latestValues)&&bt(l,this.latestValues),l}removeTransform(i){const a=W();le(a,i);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!Je(c.latestValues))continue;$n(c.latestValues)&&c.updateSnapshot();const u=W(),h=c.measurePageBox();le(u,h),co(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return Je(this.latestValues)&&co(a,this.latestValues),a}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==$.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(i||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=$.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=W(),this.relativeTargetOrigin=W(),ts(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),le(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=W(),this.targetWithTransforms=W()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),$p(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):le(this.target,this.layout.layoutBox),ec(this.target,this.targetDelta)):le(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=W(),this.relativeTargetOrigin=W(),ts(this.relativeTargetOrigin,this.target,m.target),le(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Yt&&et.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||$n(this.parent.latestValues)||Jl(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var i;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((i=this.parent)===null||i===void 0)&&i.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===$.timestamp&&(c=!1),c)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;le(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;nm(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=W());const{target:v}=a;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(oo(this.prevProjectionDelta.x,this.projectionDelta.x),oo(this.prevProjectionDelta.y,this.projectionDelta.y)),es(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==m||!mo(this.projectionDelta.x,this.prevProjectionDelta.x)||!mo(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),Yt&&et.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),i){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=xt(),this.projectionDelta=xt(),this.projectionDeltaWithTransform=xt()}setAnimationOrigin(i,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=xt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=W(),m=l?l.source:void 0,v=this.layout?this.layout.source:void 0,y=m!==v,x=this.getStack(),b=!x||x.members.length<=1,w=!!(y&&!b&&this.options.crossfade===!0&&!this.path.some(Km));this.animationProgress=0;let T;this.mixTargetDelta=A=>{const C=A/1e3;vo(h.x,i.x,C),vo(h.y,i.y,C),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ts(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Hm(this.relativeTarget,this.relativeTargetOrigin,f,C),T&&Tm(this.relativeTarget,T)&&(this.isProjectionDirty=!1),T||(T=W()),le(T,this.relativeTarget)),y&&(this.animationValues=u,ym(u,c,this.latestValues,C,w,b)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(me(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=L.update(()=>{As.hasAnimatedSinceResize=!0,this.currentAnimation=Em(0,yo,{...i,onUpdate:a=>{this.mixTargetDelta(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(yo),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=i;if(!(!a||!l||!c)){if(this!==i&&this.layout&&c&&hc(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||W();const h=oe(this.layout.layoutBox.x);l.x.min=i.target.x.min,l.x.max=l.x.min+h;const f=oe(this.layout.layoutBox.y);l.y.min=i.target.y.min,l.y.max=l.y.min+f}le(a,l),bt(a,u),es(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(i,a){this.sharedNodes.has(i)||this.sharedNodes.set(i,new Pm),this.sharedNodes.get(i).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){var i;const{layoutId:a}=this.options;return a?((i=this.getStack())===null||i===void 0?void 0:i.lead)||this:this}getPrevLead(){var i;const{layoutId:a}=this.options;return a?(i=this.getStack())===null||i===void 0?void 0:i.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),i&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetSkewAndRotation(){const{visualElement:i}=this.options;if(!i)return;let a=!1;const{latestValues:l}=i;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&hn("z",i,c,this.animationValues);for(let u=0;u<dn.length;u++)hn(`rotate${dn[u]}`,i,c,this.animationValues),hn(`skew${dn[u]}`,i,c,this.animationValues);i.render();for(const u in c)i.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);i.scheduleRender()}getProjectionStyles(i){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Rm;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=ks(i==null?void 0:i.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=ks(i==null?void 0:i.pointerEvents)||""),this.hasProjected&&!Je(this.latestValues)&&(y.transform=u?u({},""):"none",this.hasProjected=!1),y}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),c.transform=Sm(this.projectionDeltaWithTransform,this.treeScale,f),u&&(c.transform=u(f,c.transform));const{x:m,y:v}=this.projectionDelta;c.transformOrigin=`${m.origin*100}% ${v.origin*100}% 0`,h.animationValues?c.opacity=h===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const y in Fs){if(f[y]===void 0)continue;const{correct:x,applyTo:b}=Fs[y],w=c.transform==="none"?f[y]:x(f[y],h);if(b){const T=b.length;for(let A=0;A<T;A++)c[b[A]]=w}else c[y]=w}return this.options.layoutId&&(c.pointerEvents=h===this?ks(i==null?void 0:i.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>{var a;return(a=i.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(go),this.root.sharedNodes.clear()}}}function Nm(t){t.updateLayout()}function Dm(t){var e;const s=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&s&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:r}=t.layout,{animationType:o}=t.options,i=s.source!==t.layout.source;o==="size"?ce(h=>{const f=i?s.measuredBox[h]:s.layoutBox[h],m=oe(f);f.min=n[h].min,f.max=f.min+m}):hc(o,s.layoutBox,n)&&ce(h=>{const f=i?s.measuredBox[h]:s.layoutBox[h],m=oe(n[h]);f.max=f.min+m,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+m)});const a=xt();es(a,n,s.layoutBox);const l=xt();i?es(l,t.applyTransform(r,!0),s.measuredBox):es(l,n,s.layoutBox);const c=!lc(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:m}=h;if(f&&m){const v=W();ts(v,s.layoutBox,f.layoutBox);const y=W();ts(y,n,m.layoutBox),cc(v,y)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=y,t.relativeTargetOrigin=v,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:s,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:n}=t.options;n&&n()}t.options.transition=void 0}function Vm(t){Yt&&et.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Om(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Lm(t){t.clearSnapshot()}function go(t){t.clearMeasurements()}function Im(t){t.isLayoutDirty=!1}function Fm(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function xo(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Bm(t){t.resolveTargetDelta()}function Um(t){t.calcProjection()}function _m(t){t.resetSkewAndRotation()}function zm(t){t.removeLeadSnapshot()}function vo(t,e,s){t.translate=z(e.translate,0,s),t.scale=z(e.scale,1,s),t.origin=e.origin,t.originPoint=e.originPoint}function bo(t,e,s,n){t.min=z(e.min,s.min,n),t.max=z(e.max,s.max,n)}function Hm(t,e,s,n){bo(t.x,e.x,s.x,n),bo(t.y,e.y,s.y,n)}function Km(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Wm={duration:.45,ease:[.4,0,.1,1]},wo=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),To=wo("applewebkit/")&&!wo("chrome/")?Math.round:Q;function Po(t){t.min=To(t.min),t.max=To(t.max)}function Gm(t){Po(t.x),Po(t.y)}function hc(t,e,s){return t==="position"||t==="preserve-aspect"&&!qp(po(e),po(s),.2)}function qm(t){var e;return t!==t.root&&((e=t.scroll)===null||e===void 0?void 0:e.wasRoot)}const $m=dc({attachResizeListener:(t,e)=>ke(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),fn={current:void 0},fc=dc({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!fn.current){const t=new $m({});t.mount(window),t.setOptions({layoutScroll:!0}),fn.current=t}return fn.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Qm={pan:{Feature:cm},drag:{Feature:lm,ProjectionNode:fc,MeasureLayout:ic}};function So(t,e,s){const{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover",s);const r=n[s?"onHoverStart":"onHoverEnd"];r&&L.postRender(()=>r(e,ps(e)))}class Ym extends Ye{mount(){const{current:e,props:s}=this.node;e&&(this.unmount=Ip(e,n=>(So(this.node,n,!0),r=>So(this.node,r,!1)),{passive:!s.onHoverStart&&!s.onHoverEnd}))}unmount(){}}class Xm extends Ye{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=qe(ke(this.node.current,"focus",()=>this.onFocus()),ke(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const pc=(t,e)=>e?t===e?!0:pc(t,e.parentElement):!1;function pn(t,e){if(!e)return;const s=new PointerEvent("pointer"+t);e(s,ps(s))}class Zm extends Ye{constructor(){super(...arguments),this.removeStartListeners=Q,this.removeEndListeners=Q,this.removeAccessibleListeners=Q,this.startPointerPress=(e,s)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),o=$e(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:u,globalTapTarget:h}=this.node.getProps(),f=!h&&!pc(this.node.current,a.target)?u:c;f&&L.update(()=>f(a,l))},{passive:!(n.onTap||n.onPointerUp)}),i=$e(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=qe(o,i),this.startPress(e,s)},this.startAccessiblePress=()=>{const e=o=>{if(o.key!=="Enter"||this.isPressing)return;const i=a=>{a.key!=="Enter"||!this.checkPressEnd()||pn("up",(l,c)=>{const{onTap:u}=this.node.getProps();u&&L.postRender(()=>u(l,c))})};this.removeEndListeners(),this.removeEndListeners=ke(this.node.current,"keyup",i),pn("down",(a,l)=>{this.startPress(a,l)})},s=ke(this.node.current,"keydown",e),n=()=>{this.isPressing&&pn("cancel",(o,i)=>this.cancelPress(o,i))},r=ke(this.node.current,"blur",n);this.removeAccessibleListeners=qe(s,r)}}startPress(e,s){this.isPressing=!0;const{onTapStart:n,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&L.postRender(()=>n(e,s))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Gl()}cancelPress(e,s){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&L.postRender(()=>n(e,s))}mount(){const e=this.node.getProps(),s=$e(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),n=ke(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=qe(s,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Yn=new WeakMap,mn=new WeakMap,Jm=t=>{const e=Yn.get(t.target);e&&e(t)},ey=t=>{t.forEach(Jm)};function ty({root:t,...e}){const s=t||document;mn.has(s)||mn.set(s,{});const n=mn.get(s),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(ey,{root:t,...e})),n[r]}function sy(t,e,s){const n=ty(e);return Yn.set(t,s),n.observe(t),()=>{Yn.delete(t),n.unobserve(t)}}const ny={some:0,all:1};class ry extends Ye{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:s,margin:n,amount:r="some",once:o}=e,i={root:s?s.current:void 0,rootMargin:n,threshold:typeof r=="number"?r:ny[r]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(l)};return sy(this.node.current,i,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:s}=this.node;["amount","margin","root"].some(iy(e,s))&&this.startObserver()}unmount(){}}function iy({viewport:t={}},{viewport:e={}}={}){return s=>t[s]!==e[s]}const oy={inView:{Feature:ry},tap:{Feature:Zm},focus:{Feature:Xm},hover:{Feature:Ym}},ay={layout:{ProjectionNode:fc,MeasureLayout:ic}},Lr=g.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Gs=g.createContext({}),Ir=typeof window<"u",Fr=Ir?g.useLayoutEffect:g.useEffect,mc=g.createContext({strict:!1});function ly(t,e,s,n,r){var o,i;const{visualElement:a}=g.useContext(Gs),l=g.useContext(mc),c=g.useContext(Vr),u=g.useContext(Lr).reducedMotion,h=g.useRef();n=n||l.renderer,!h.current&&n&&(h.current=n(t,{visualState:e,parent:a,props:s,presenceContext:c,blockInitialAnimation:c?c.initial===!1:!1,reducedMotionConfig:u}));const f=h.current,m=g.useContext(rc);f&&!f.projection&&r&&(f.type==="html"||f.type==="svg")&&cy(h.current,s,r,m);const v=g.useRef(!1);g.useInsertionEffect(()=>{f&&v.current&&f.update(s,c)});const y=s[zl],x=g.useRef(!!y&&!(!((o=window.MotionHandoffIsComplete)===null||o===void 0)&&o.call(window,y))&&((i=window.MotionHasOptimisedAnimation)===null||i===void 0?void 0:i.call(window,y)));return Fr(()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Or.render(f.render),x.current&&f.animationState&&f.animationState.animateChanges())}),g.useEffect(()=>{f&&(!x.current&&f.animationState&&f.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var b;(b=window.MotionHandoffMarkAsComplete)===null||b===void 0||b.call(window,y)}),x.current=!1))}),f}function cy(t,e,s,n){const{layoutId:r,layout:o,drag:i,dragConstraints:a,layoutScroll:l,layoutRoot:c}=e;t.projection=new s(t.latestValues,e["data-framer-portal-id"]?void 0:yc(t.parent)),t.projection.setOptions({layoutId:r,layout:o,alwaysMeasureLayout:!!i||a&&gt(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:c})}function yc(t){if(t)return t.options.allowProjection!==!1?t.projection:yc(t.parent)}function uy(t,e,s){return g.useCallback(n=>{n&&t.mount&&t.mount(n),e&&(n?e.mount(n):e.unmount()),s&&(typeof s=="function"?s(n):gt(s)&&(s.current=n))},[e])}function qs(t){return Hs(t.animate)||dr.some(e=>rs(t[e]))}function gc(t){return!!(qs(t)||t.variants)}function dy(t,e){if(qs(t)){const{initial:s,animate:n}=t;return{initial:s===!1||rs(s)?s:void 0,animate:rs(n)?n:void 0}}return t.inherit!==!1?e:{}}function hy(t){const{initial:e,animate:s}=dy(t,g.useContext(Gs));return g.useMemo(()=>({initial:e,animate:s}),[Co(e),Co(s)])}function Co(t){return Array.isArray(t)?t.join(" "):t}const jo={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Bt={};for(const t in jo)Bt[t]={isEnabled:e=>jo[t].some(s=>!!e[s])};function fy(t){for(const e in t)Bt[e]={...Bt[e],...t[e]}}const py=Symbol.for("motionComponentSymbol");function my({preloadedFeatures:t,createVisualElement:e,useRender:s,useVisualState:n,Component:r}){t&&fy(t);function o(a,l){let c;const u={...g.useContext(Lr),...a,layoutId:yy(a)},{isStatic:h}=u,f=hy(a),m=n(a,h);if(!h&&Ir){gy();const v=xy(u);c=v.MeasureLayout,f.visualElement=ly(r,m,u,e,v.ProjectionNode)}return d.jsxs(Gs.Provider,{value:f,children:[c&&f.visualElement?d.jsx(c,{visualElement:f.visualElement,...u}):null,s(r,a,uy(m,f.visualElement,l),m,h,f.visualElement)]})}const i=g.forwardRef(o);return i[py]=r,i}function yy({layoutId:t}){const e=g.useContext(nc).id;return e&&t!==void 0?e+"-"+t:t}function gy(t,e){g.useContext(mc).strict}function xy(t){const{drag:e,layout:s}=Bt;if(!e&&!s)return{};const n={...e,...s};return{MeasureLayout:e!=null&&e.isEnabled(t)||s!=null&&s.isEnabled(t)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}const vy=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Br(t){return typeof t!="string"||t.includes("-")?!1:!!(vy.indexOf(t)>-1||/[A-Z]/u.test(t))}function xc(t,{style:e,vars:s},n,r){Object.assign(t.style,e,r&&r.getProjectionStyles(n));for(const o in s)t.style.setProperty(o,s[o])}const vc=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function bc(t,e,s,n){xc(t,e,void 0,n);for(const r in e.attrs)t.setAttribute(vc.has(r)?r:Nr(r),e.attrs[r])}function wc(t,{layout:e,layoutId:s}){return ft.has(t)||t.startsWith("origin")||(e||s!==void 0)&&(!!Fs[t]||t==="opacity")}function Ur(t,e,s){var n;const{style:r}=t,o={};for(const i in r)(J(r[i])||e.style&&J(e.style[i])||wc(i,t)||((n=s==null?void 0:s.getValue(i))===null||n===void 0?void 0:n.liveStyle)!==void 0)&&(o[i]=r[i]);return o}function Tc(t,e,s){const n=Ur(t,e,s);for(const r in t)if(J(t[r])||J(e[r])){const o=ds.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[o]=t[r]}return n}function $s(t){const e=g.useRef(null);return e.current===null&&(e.current=t()),e.current}function by({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:s},n,r,o){const i={latestValues:wy(n,r,o,t),renderState:e()};return s&&(i.mount=a=>s(n,a,i)),i}const Pc=t=>(e,s)=>{const n=g.useContext(Gs),r=g.useContext(Vr),o=()=>by(t,e,n,r);return s?o():$s(o)};function wy(t,e,s,n){const r={},o=n(t,{});for(const f in o)r[f]=ks(o[f]);let{initial:i,animate:a}=t;const l=qs(t),c=gc(t);e&&c&&!l&&t.inherit!==!1&&(i===void 0&&(i=e.initial),a===void 0&&(a=e.animate));let u=s?s.initial===!1:!1;u=u||i===!1;const h=u?a:i;if(h&&typeof h!="boolean"&&!Hs(h)){const f=Array.isArray(h)?h:[h];for(let m=0;m<f.length;m++){const v=cr(t,f[m]);if(v){const{transitionEnd:y,transition:x,...b}=v;for(const w in b){let T=b[w];if(Array.isArray(T)){const A=u?T.length-1:0;T=T[A]}T!==null&&(r[w]=T)}for(const w in y)r[w]=y[w]}}}return r}const _r=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Sc=()=>({..._r(),attrs:{}}),Cc=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Ty={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Py=ds.length;function Sy(t,e,s){let n="",r=!0;for(let o=0;o<Py;o++){const i=ds[o],a=t[i];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(i.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||s){const c=Cc(a,vr[i]);if(!l){r=!1;const u=Ty[i]||i;n+=`${u}(${c}) `}s&&(e[i]=c)}}return n=n.trim(),s?n=s(e,r?"":n):r&&(n="none"),n}function zr(t,e,s){const{style:n,vars:r,transformOrigin:o}=t;let i=!1,a=!1;for(const l in e){const c=e[l];if(ft.has(l)){i=!0;continue}else if(yl(l)){r[l]=c;continue}else{const u=Cc(c,vr[l]);l.startsWith("origin")?(a=!0,o[l]=u):n[l]=u}}if(e.transform||(i||s?n.transform=Sy(e,t.transform,s):n.transform&&(n.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;n.transformOrigin=`${l} ${c} ${u}`}}function Ao(t,e,s){return typeof t=="string"?t:E.transform(e+s*t)}function Cy(t,e,s){const n=Ao(e,t.x,t.width),r=Ao(s,t.y,t.height);return`${n} ${r}`}const jy={offset:"stroke-dashoffset",array:"stroke-dasharray"},Ay={offset:"strokeDashoffset",array:"strokeDasharray"};function ky(t,e,s=1,n=0,r=!0){t.pathLength=1;const o=r?jy:Ay;t[o.offset]=E.transform(-n);const i=E.transform(e),a=E.transform(s);t[o.array]=`${i} ${a}`}function Hr(t,{attrX:e,attrY:s,attrScale:n,originX:r,originY:o,pathLength:i,pathSpacing:a=1,pathOffset:l=0,...c},u,h){if(zr(t,c,h),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:f,style:m,dimensions:v}=t;f.transform&&(v&&(m.transform=f.transform),delete f.transform),v&&(r!==void 0||o!==void 0||m.transform)&&(m.transformOrigin=Cy(v,r!==void 0?r:.5,o!==void 0?o:.5)),e!==void 0&&(f.x=e),s!==void 0&&(f.y=s),n!==void 0&&(f.scale=n),i!==void 0&&ky(f,i,a,l,!1)}const Kr=t=>typeof t=="string"&&t.toLowerCase()==="svg",Ey={useVisualState:Pc({scrapeMotionValuesFromProps:Tc,createRenderState:Sc,onMount:(t,e,{renderState:s,latestValues:n})=>{L.read(()=>{try{s.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{s.dimensions={x:0,y:0,width:0,height:0}}}),L.render(()=>{Hr(s,n,Kr(e.tagName),t.transformTemplate),bc(e,s)})}})},Ry={useVisualState:Pc({scrapeMotionValuesFromProps:Ur,createRenderState:_r})};function jc(t,e,s){for(const n in e)!J(e[n])&&!wc(n,s)&&(t[n]=e[n])}function My({transformTemplate:t},e){return g.useMemo(()=>{const s=_r();return zr(s,e,t),Object.assign({},s.vars,s.style)},[e])}function Ny(t,e){const s=t.style||{},n={};return jc(n,s,t),Object.assign(n,My(t,e)),n}function Dy(t,e){const s={},n=Ny(t,e);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=n,s}const Vy=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Bs(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Vy.has(t)}let Ac=t=>!Bs(t);function Oy(t){t&&(Ac=e=>e.startsWith("on")?!Bs(e):t(e))}try{Oy(require("@emotion/is-prop-valid").default)}catch{}function Ly(t,e,s){const n={};for(const r in t)r==="values"&&typeof t.values=="object"||(Ac(r)||s===!0&&Bs(r)||!e&&!Bs(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}function Iy(t,e,s,n){const r=g.useMemo(()=>{const o=Sc();return Hr(o,e,Kr(n),t.transformTemplate),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};jc(o,t.style,t),r.style={...o,...r.style}}return r}function Fy(t=!1){return(s,n,r,{latestValues:o},i)=>{const l=(Br(s)?Iy:Dy)(n,o,i,s),c=Ly(n,typeof s=="string",t),u=s!==g.Fragment?{...c,...l,ref:r}:{},{children:h}=n,f=g.useMemo(()=>J(h)?h.get():h,[h]);return g.createElement(s,{...u,children:f})}}function By(t,e){return function(n,{forwardMotionProps:r}={forwardMotionProps:!1}){const i={...Br(n)?Ey:Ry,preloadedFeatures:t,useRender:Fy(r),createVisualElement:e,Component:n};return my(i)}}const Xn={current:null},kc={current:!1};function Uy(){if(kc.current=!0,!!Ir)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Xn.current=t.matches;t.addListener(e),e()}else Xn.current=!1}function _y(t,e,s){for(const n in e){const r=e[n],o=s[n];if(J(r))t.addValue(n,r);else if(J(o))t.addValue(n,be(r,{owner:t}));else if(o!==r)if(t.hasValue(n)){const i=t.getValue(n);i.liveStyle===!0?i.jump(r):i.hasAnimated||i.set(r)}else{const i=t.getStaticValue(n);t.addValue(n,be(i!==void 0?i:r,{owner:t}))}}for(const n in s)e[n]===void 0&&t.removeValue(n);return e}const ko=new WeakMap,zy=[...vl,Z,Qe],Hy=t=>zy.find(xl(t)),Eo=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Ky{scrapeMotionValuesFromProps(e,s,n){return{}}constructor({parent:e,props:s,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:o,visualState:i},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=yr,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=Te.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,L.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=i;this.latestValues=l,this.baseTarget={...l},this.initialValues=s.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=s,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=qs(s),this.isVariantNode=gc(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(s,{},this);for(const f in h){const m=h[f];l[f]!==void 0&&J(m)&&m.set(l[f],!1)}}mount(e){this.current=e,ko.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,n)=>this.bindToMotionValue(n,s)),kc.current||Uy(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Xn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){ko.delete(this.current),this.projection&&this.projection.unmount(),me(this.notifyUpdate),me(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const s=this.features[e];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(e,s){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const n=ft.has(e),r=s.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&L.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=s.on("renderRequest",this.scheduleRender);let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,s)),this.valueSubscriptions.set(e,()=>{r(),o(),i&&i(),s.owner&&s.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in Bt){const s=Bt[e];if(!s)continue;const{isEnabled:n,Feature:r}=s;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):W()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,s){this.latestValues[e]=s}update(e,s){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let n=0;n<Eo.length;n++){const r=Eo[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const o="on"+r,i=e[o];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=_y(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(e),()=>s.variantChildren.delete(e)}addValue(e,s){const n=this.values.get(e);s!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,s),this.values.set(e,s),this.latestValues[e]=s.get())}removeValue(e){this.values.delete(e);const s=this.valueSubscriptions.get(e);s&&(s(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,s){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return n===void 0&&s!==void 0&&(n=be(s===null?void 0:s,{owner:this}),this.addValue(e,n)),n}readValue(e,s){var n;let r=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(n=this.getBaseTargetFromProps(this.props,e))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,e,this.options);return r!=null&&(typeof r=="string"&&(pl(r)||fl(r))?r=parseFloat(r):!Hy(r)&&Qe.test(s)&&(r=Al(e,s)),this.setBaseTarget(e,J(r)?r.get():r)),J(r)?r.get():r}setBaseTarget(e,s){this.baseTarget[e]=s}getBaseTarget(e){var s;const{initial:n}=this.props;let r;if(typeof n=="string"||typeof n=="object"){const i=cr(this.props,n,(s=this.presenceContext)===null||s===void 0?void 0:s.custom);i&&(r=i[e])}if(n&&r!==void 0)return r;const o=this.getBaseTargetFromProps(this.props,e);return o!==void 0&&!J(o)?o:this.initialValues[e]!==void 0&&r===void 0?void 0:this.baseTarget[e]}on(e,s){return this.events[e]||(this.events[e]=new Mr),this.events[e].add(s)}notify(e,...s){this.events[e]&&this.events[e].notify(...s)}}class Ec extends Ky{constructor(){super(...arguments),this.KeyframeResolver=kl}sortInstanceNodePosition(e,s){return e.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(e,s){return e.style?e.style[s]:void 0}removeValueFromRenderState(e,{vars:s,style:n}){delete s[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;J(e)&&(this.childSubscription=e.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function Wy(t){return window.getComputedStyle(t)}class Gy extends Ec{constructor(){super(...arguments),this.type="html",this.renderInstance=xc}readValueFromInstance(e,s){if(ft.has(s)){const n=br(s);return n&&n.default||0}else{const n=Wy(e),r=(yl(s)?n.getPropertyValue(s):n[s])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:s}){return tc(e,s)}build(e,s,n){zr(e,s,n.transformTemplate)}scrapeMotionValuesFromProps(e,s,n){return Ur(e,s,n)}}class qy extends Ec{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=W}getBaseTargetFromProps(e,s){return e[s]}readValueFromInstance(e,s){if(ft.has(s)){const n=br(s);return n&&n.default||0}return s=vc.has(s)?s:Nr(s),e.getAttribute(s)}scrapeMotionValuesFromProps(e,s,n){return Tc(e,s,n)}build(e,s,n){Hr(e,s,this.isSVGTag,n.transformTemplate)}renderInstance(e,s,n,r){bc(e,s,n,r)}mount(e){this.isSVGTag=Kr(e.tagName),super.mount(e)}}const $y=(t,e)=>Br(t)?new qy(e):new Gy(e,{allowProjection:t!==g.Fragment}),Qy=By({...Lp,...oy,...Qm,...ay},$y),P=Oh(Qy);function Yy(t){const e=$s(()=>be(t)),{isStatic:s}=g.useContext(Lr);if(s){const[,n]=g.useState(t);g.useEffect(()=>e.on("change",n),[])}return e}function Rc(t,e){const s=Yy(e()),n=()=>s.set(e());return n(),Fr(()=>{const r=()=>L.preRender(n,!1,!0),o=t.map(i=>i.on("change",r));return()=>{o.forEach(i=>i()),me(n)}}),s}const Xy=t=>t&&typeof t=="object"&&t.mix,Zy=t=>Xy(t)?t.mix:void 0;function Jy(...t){const e=!Array.isArray(t[0]),s=e?0:-1,n=t[0+s],r=t[1+s],o=t[2+s],i=t[3+s],a=Cr(r,o,{mixer:Zy(o[0]),...i});return e?a(n):a}function eg(t){Jt.current=[],t();const e=Rc(Jt.current,t);return Jt.current=void 0,e}function Ro(t,e,s,n){if(typeof t=="function")return eg(t);const r=typeof e=="function"?e:Jy(e,s,n);return Array.isArray(t)?Mo(t,r):Mo([t],([o])=>r(o))}function Mo(t,e){const s=$s(()=>[]);return Rc(t,()=>{s.length=0;const n=t.length;for(let r=0;r<n;r++)s[r]=t[r].get();return e(s)})}const Es=new WeakMap;let Ie;function tg(t,e){if(e){const{inlineSize:s,blockSize:n}=e[0];return{width:s,height:n}}else return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}function sg({target:t,contentRect:e,borderBoxSize:s}){var n;(n=Es.get(t))===null||n===void 0||n.forEach(r=>{r({target:t,contentSize:e,get size(){return tg(t,s)}})})}function ng(t){t.forEach(sg)}function rg(){typeof ResizeObserver>"u"||(Ie=new ResizeObserver(ng))}function ig(t,e){Ie||rg();const s=Dr(t);return s.forEach(n=>{let r=Es.get(n);r||(r=new Set,Es.set(n,r)),r.add(e),Ie==null||Ie.observe(n)}),()=>{s.forEach(n=>{const r=Es.get(n);r==null||r.delete(e),r!=null&&r.size||Ie==null||Ie.unobserve(n)})}}const Rs=new Set;let ss;function og(){ss=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Rs.forEach(s=>s(e))},window.addEventListener("resize",ss)}function ag(t){return Rs.add(t),ss||og(),()=>{Rs.delete(t),!Rs.size&&ss&&(ss=void 0)}}function lg(t,e){return typeof t=="function"?ag(t):ig(t,e)}const cg=50,No=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),ug=()=>({time:0,x:No(),y:No()}),dg={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Do(t,e,s,n){const r=s[e],{length:o,position:i}=dg[e],a=r.current,l=s.time;r.current=t[`scroll${i}`],r.scrollLength=t[`scroll${o}`]-t[`client${o}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=ht(0,r.scrollLength,r.current);const c=n-l;r.velocity=c>cg?0:Tr(r.current-a,c)}function hg(t,e,s){Do(t,"x",e,s),Do(t,"y",e,s),e.time=s}function fg(t,e){const s={x:0,y:0};let n=t;for(;n&&n!==e;)if(n instanceof HTMLElement)s.x+=n.offsetLeft,s.y+=n.offsetTop,n=n.offsetParent;else if(n.tagName==="svg"){const r=n.getBoundingClientRect();n=n.parentElement;const o=n.getBoundingClientRect();s.x+=r.left-o.left,s.y+=r.top-o.top}else if(n instanceof SVGGraphicsElement){const{x:r,y:o}=n.getBBox();s.x+=r,s.y+=o;let i=null,a=n.parentNode;for(;!i;)a.tagName==="svg"&&(i=a),a=n.parentNode;n=i}else break;return s}const pg={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Zn={start:0,center:.5,end:1};function Vo(t,e,s=0){let n=0;if(t in Zn&&(t=Zn[t]),typeof t=="string"){const r=parseFloat(t);t.endsWith("px")?n=r:t.endsWith("%")?t=r/100:t.endsWith("vw")?n=r/100*document.documentElement.clientWidth:t.endsWith("vh")?n=r/100*document.documentElement.clientHeight:t=r}return typeof t=="number"&&(n=e*t),s+n}const mg=[0,0];function yg(t,e,s,n){let r=Array.isArray(t)?t:mg,o=0,i=0;return typeof t=="number"?r=[t,t]:typeof t=="string"&&(t=t.trim(),t.includes(" ")?r=t.split(" "):r=[t,Zn[t]?t:"0"]),o=Vo(r[0],s,n),i=Vo(r[1],e),o-i}const gg={x:0,y:0};function xg(t){return"getBBox"in t&&t.tagName!=="svg"?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}function vg(t,e,s){const{offset:n=pg.All}=s,{target:r=t,axis:o="y"}=s,i=o==="y"?"height":"width",a=r!==t?fg(r,t):gg,l=r===t?{width:t.scrollWidth,height:t.scrollHeight}:xg(r),c={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let u=!e[o].interpolate;const h=n.length;for(let f=0;f<h;f++){const m=yg(n[f],c[i],l[i],a[o]);!u&&m!==e[o].interpolatorOffsets[f]&&(u=!0),e[o].offset[f]=m}u&&(e[o].interpolate=Cr(e[o].offset,Il(n)),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=e[o].interpolate(e[o].current)}function bg(t,e=t,s){if(s.x.targetOffset=0,s.y.targetOffset=0,e!==t){let n=e;for(;n&&n!==t;)s.x.targetOffset+=n.offsetLeft,s.y.targetOffset+=n.offsetTop,n=n.offsetParent}s.x.targetLength=e===t?e.scrollWidth:e.clientWidth,s.y.targetLength=e===t?e.scrollHeight:e.clientHeight,s.x.containerLength=t.clientWidth,s.y.containerLength=t.clientHeight}function wg(t,e,s,n={}){return{measure:()=>bg(t,n.target,s),update:r=>{hg(t,s,r),(n.offset||n.target)&&vg(t,s,n)},notify:()=>e(s)}}const qt=new WeakMap,Oo=new WeakMap,yn=new WeakMap,Lo=t=>t===document.documentElement?window:t;function Wr(t,{container:e=document.documentElement,...s}={}){let n=yn.get(e);n||(n=new Set,yn.set(e,n));const r=ug(),o=wg(e,t,r,s);if(n.add(o),!qt.has(e)){const a=()=>{for(const f of n)f.measure()},l=()=>{for(const f of n)f.update($.timestamp)},c=()=>{for(const f of n)f.notify()},u=()=>{L.read(a,!1,!0),L.read(l,!1,!0),L.update(c,!1,!0)};qt.set(e,u);const h=Lo(e);window.addEventListener("resize",u,{passive:!0}),e!==document.documentElement&&Oo.set(e,lg(e,u)),h.addEventListener("scroll",u,{passive:!0})}const i=qt.get(e);return L.read(i,!1,!0),()=>{var a;me(i);const l=yn.get(e);if(!l||(l.delete(o),l.size))return;const c=qt.get(e);qt.delete(e),c&&(Lo(e).removeEventListener("scroll",c),(a=Oo.get(e))===null||a===void 0||a(),window.removeEventListener("resize",c))}}function Mc(t,e){let s;const n=()=>{const{currentTime:r}=e,i=(r===null?0:r.value)/100;s!==i&&t(i),s=i};return L.update(n,!0),()=>me(n)}function Tg({source:t,container:e,axis:s="y"}){t&&(e=t);const n={value:0},r=Wr(o=>{n.value=o[s].progress*100},{container:e,axis:s});return{currentTime:n,cancel:r}}const gn=new Map;function Nc({source:t,container:e=document.documentElement,axis:s="y"}={}){t&&(e=t),gn.has(e)||gn.set(e,{});const n=gn.get(e);return n[s]||(n[s]=_l()?new ScrollTimeline({source:e,axis:s}):Tg({source:e,axis:s})),n[s]}function Pg(t){return t.length===2}function Dc(t){return t&&(t.target||t.offset)}function Sg(t,e){return Pg(t)||Dc(e)?Wr(s=>{t(s[e.axis].progress,s)},e):Mc(t,Nc(e))}function Cg(t,e){if(t.flatten(),Dc(e))return t.pause(),Wr(s=>{t.time=t.duration*s[e.axis].progress},e);{const s=Nc(e);return t.attachTimeline?t.attachTimeline(s,n=>(n.pause(),Mc(r=>{n.time=n.duration*r},s))):Q}}function jg(t,{axis:e="y",...s}={}){const n={axis:e,...s};return typeof t=="function"?Sg(t,n):Cg(t,n)}function Io(t,e){Hh(!!(!e||e.current))}const Ag=()=>({scrollX:be(0),scrollY:be(0),scrollXProgress:be(0),scrollYProgress:be(0)});function kg({container:t,target:e,layoutEffect:s=!0,...n}={}){const r=$s(Ag);return(s?Fr:g.useEffect)(()=>(Io("target",e),Io("container",t),jg((i,{x:a,y:l})=>{r.scrollX.set(a.current),r.scrollXProgress.set(a.progress),r.scrollY.set(l.current),r.scrollYProgress.set(l.progress)},{...n,container:(t==null?void 0:t.current)||void 0,target:(e==null?void 0:e.current)||void 0})),[t,e,JSON.stringify(n.offset)]),r}const Eg={some:0,all:1};function Rg(t,e,{root:s,margin:n,amount:r="some"}={}){const o=Dr(t),i=new WeakMap,a=c=>{c.forEach(u=>{const h=i.get(u.target);if(u.isIntersecting!==!!h)if(u.isIntersecting){const f=e(u);typeof f=="function"?i.set(u.target,f):l.unobserve(u.target)}else h&&(h(u),i.delete(u.target))})},l=new IntersectionObserver(a,{root:s,rootMargin:n,threshold:typeof r=="number"?r:Eg[r]});return o.forEach(c=>l.observe(c)),()=>l.disconnect()}function ms(t,{root:e,margin:s,amount:n,once:r=!1}={}){const[o,i]=g.useState(!1);return g.useEffect(()=>{if(!t.current||r&&o)return;const a=()=>(i(!0),r?void 0:()=>i(!1)),l={root:e&&e.current||void 0,margin:s,amount:n};return Rg(t.current,a,l)},[e,t,s,r,n]),o}const Mg="/assets/BullBuster_1752063257019-D-NwXa2E.png";function Ng(){const[t,e]=g.useState(!1),[s,n]=g.useState(!1);g.useEffect(()=>{const o=()=>{e(window.scrollY>50)};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)},[]);const r=o=>{const i=document.getElementById(o);i&&(i.scrollIntoView({behavior:"smooth",block:"start"}),n(!1))};return d.jsx(P.nav,{initial:{y:-100},animate:{y:0},className:`fixed top-0 w-full z-50 transition-all duration-300 ${t?"glass-navbar shadow-lg":"glass-navbar"}`,children:d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs("div",{className:"flex items-center justify-between h-16 sm:h-20",children:[d.jsxs(P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-2 sm:space-x-3",children:[d.jsx("img",{src:Mg,alt:"BullBuster Logo",className:"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 object-contain"}),d.jsx("span",{className:"text-lg sm:text-xl lg:text-2xl font-black text-brand-yellow",children:"BullBuster"})]}),d.jsx("div",{className:"hidden md:flex items-center space-x-6 lg:space-x-8",children:["home","about","menu","gallery","track","contact"].map(o=>d.jsx("button",{onClick:()=>r(o),className:"text-white hover:text-brand-yellow transition-colors duration-300 font-medium capitalize text-sm lg:text-base",children:o==="track"?"Track Order":o},o))}),d.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[d.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>r("menu"),className:"hidden sm:block bg-brand-yellow text-brand-black px-4 lg:px-6 py-2 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 shadow-lg text-sm lg:text-base",children:"Order Now"}),d.jsx("button",{className:"md:hidden text-white p-2 rounded-lg hover:bg-white/10 transition-colors",onClick:()=>n(!s),children:s?d.jsx(La,{size:24}):d.jsx(wd,{size:24})})]})]}),s&&d.jsx(P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden border-t border-brand-yellow/30 bg-brand-black-soft/95 backdrop-blur-xl",children:d.jsxs("div",{className:"px-4 py-6 space-y-4",children:[["home","about","menu","gallery","track","contact"].map(o=>d.jsx("button",{onClick:()=>r(o),className:"block w-full text-left text-white hover:text-brand-yellow transition-colors duration-300 font-medium capitalize py-2 text-lg",children:o==="track"?"Track Order":o},o)),d.jsx(P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>r("menu"),className:"w-full bg-brand-yellow text-brand-black py-3 rounded-lg font-bold mt-4 hover:bg-brand-yellow-light transition-all duration-300",children:"Order Now"})]})})]})})}function Dg(){const t=g.useRef(null),{scrollYProgress:e}=kg({target:t,offset:["start start","end start"]}),s=Ro(e,[0,1],["0%","50%"]),n=Ro(e,[0,1],[1,0]),r=o=>{const i=document.getElementById(o);i&&i.scrollIntoView({behavior:"smooth",block:"start"})};return d.jsxs("section",{ref:t,id:"home",className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[d.jsxs(P.div,{style:{y:s},className:"absolute inset-0 parallax-bg",children:[d.jsx("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080')"}}),d.jsx("div",{className:"absolute inset-0 hero-overlay"}),d.jsx(P.div,{animate:{background:["radial-gradient(circle at 20% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 50%)","radial-gradient(circle at 80% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 50%)","radial-gradient(circle at 20% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 50%)"]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute inset-0"})]}),d.jsx(P.div,{animate:{y:[0,-30,0],rotate:[0,5,-5,0],scale:[1,1.1,1]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute top-20 left-10 text-6xl opacity-20",children:"🍕"}),d.jsx(P.div,{animate:{y:[0,-25,0],rotate:[0,-5,5,0],scale:[1,1.05,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:-2},className:"absolute top-40 right-20 text-5xl opacity-25",children:"🍗"}),d.jsx(P.div,{animate:{y:[0,-35,0],rotate:[0,10,-10,0],scale:[1,1.15,1]},transition:{duration:9,repeat:1/0,ease:"easeInOut",delay:-4},className:"absolute bottom-32 left-20 text-7xl opacity-30",children:"🍔"}),d.jsx(P.div,{animate:{rotate:360},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 right-1/4 w-32 h-32 border-2 border-brand-yellow/10 rounded-full"}),d.jsx(P.div,{animate:{rotate:-360},transition:{duration:25,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 left-1/4 w-24 h-24 border-2 border-brand-yellow/15 rounded-lg"}),d.jsxs(P.div,{style:{opacity:n},className:"relative z-10 text-center text-white px-6 max-w-7xl mx-auto",children:[d.jsxs(P.div,{initial:{y:-30,opacity:0},animate:{y:0,opacity:1},transition:{duration:.6,delay:.2},className:"flex justify-center items-center space-x-8 mb-8 text-sm",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Ad,{className:"w-4 h-4 text-brand-yellow"}),d.jsx("span",{children:"4.9/5 Rating"})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Da,{className:"w-4 h-4 text-brand-yellow"}),d.jsx("span",{children:"Best in Lahore"})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Va,{className:"w-4 h-4 text-brand-yellow"}),d.jsx("span",{children:"15 Min Delivery"})]})]}),d.jsxs(P.h1,{initial:{y:100,opacity:0,scale:.8},animate:{y:0,opacity:1,scale:1},transition:{duration:1.2,ease:"easeOut",delay:.4,type:"spring",stiffness:80},className:"text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl font-black mb-4 sm:mb-6 leading-none",children:[d.jsx("span",{className:"gradient-text",children:"BULL"}),d.jsx("span",{className:"text-white",children:"BUSTER"})]}),d.jsxs(P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.6},className:"mb-8",children:[d.jsxs("p",{className:"text-lg sm:text-xl md:text-2xl lg:text-3xl font-light max-w-4xl mx-auto leading-relaxed px-4",children:["Experience the"," ",d.jsx("span",{className:"text-brand-yellow font-semibold",children:"finest fast food"})," ","in Lahore."]}),d.jsx("p",{className:"text-base sm:text-lg md:text-xl text-gray-300 mt-3 sm:mt-4 max-w-3xl mx-auto px-4",children:"Premium burgers, legendary taste, unforgettable moments."})]}),d.jsxs(P.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5,ease:"easeOut",delay:.8},className:"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center px-4",children:[d.jsxs(P.button,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{duration:.8,delay:1,type:"spring"},whileHover:{scale:1.08,boxShadow:"0 25px 50px rgba(255, 193, 7, 0.4)",y:-8,transition:{duration:.3}},whileTap:{scale:.95},onClick:()=>r("menu"),className:"group bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-6 sm:px-8 lg:px-10 py-3 sm:py-4 lg:py-5 rounded-xl sm:rounded-2xl text-base sm:text-lg font-bold transition-all duration-300 shadow-2xl flex items-center gap-2 sm:gap-3 hover-glow w-full sm:w-auto justify-center",children:[d.jsx(Oa,{className:"w-6 h-6 group-hover:rotate-12 transition-transform duration-300"}),d.jsx("span",{children:"Explore Menu"})]}),d.jsxs(P.button,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{duration:.8,delay:1.2,type:"spring"},whileHover:{scale:1.08,backgroundColor:"rgba(255, 193, 7, 1)",color:"rgba(0, 0, 0, 1)",y:-8,boxShadow:"0 20px 40px rgba(255, 193, 7, 0.3)",transition:{duration:.3}},whileTap:{scale:.95},onClick:()=>r("about"),className:"group border-2 border-brand-yellow text-brand-yellow px-6 sm:px-8 lg:px-10 py-3 sm:py-4 lg:py-5 rounded-xl sm:rounded-2xl text-base sm:text-lg font-bold transition-all duration-300 flex items-center gap-2 sm:gap-3 backdrop-blur-sm bg-white/5 w-full sm:w-auto justify-center",children:[d.jsx(Pd,{className:"w-6 h-6 group-hover:scale-110 transition-transform duration-300"}),d.jsx("span",{children:"Our Story"})]})]})]}),d.jsx(P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2,duration:.6},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer group",onClick:()=>r("about"),children:d.jsxs(P.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"flex flex-col items-center space-y-2",children:[d.jsx("span",{className:"text-brand-yellow text-sm font-medium opacity-80 group-hover:opacity-100 transition-opacity",children:"Scroll Down"}),d.jsx(P.div,{whileHover:{scale:1.2},className:"p-3 rounded-full border-2 border-brand-yellow/30 group-hover:border-brand-yellow/60 transition-colors",children:d.jsx(md,{className:"text-brand-yellow text-xl"})})]})})]})}function Vg(){const t=g.useRef(null),e=ms(t,{once:!0,amount:.3});return d.jsx("section",{id:"about",ref:t,className:"py-20 bg-brand-black relative overflow-hidden",children:d.jsx("div",{className:"max-w-7xl mx-auto px-6",children:d.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[d.jsxs(P.div,{initial:{x:-50,opacity:0},animate:e?{x:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut"},children:[d.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Our Story"}),d.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Crafting Perfection Since Day One"}),d.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-8",children:"Born in the heart of Lahore, BullBuster represents a revolution in fast food excellence. We combine traditional Pakistani hospitality with international culinary standards to create an unforgettable dining experience."}),d.jsxs("div",{className:"grid grid-cols-2 gap-8 mb-8",children:[d.jsxs(P.div,{initial:{scale:.8,opacity:0},animate:e?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.2},className:"text-center",children:[d.jsx("div",{className:"text-3xl font-black text-brand-yellow mb-2",children:"50K+"}),d.jsx("div",{className:"text-gray-300 font-medium",children:"Happy Customers"})]}),d.jsxs(P.div,{initial:{scale:.8,opacity:0},animate:e?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.4},className:"text-center",children:[d.jsx("div",{className:"text-3xl font-black text-brand-yellow mb-2",children:"24/7"}),d.jsx("div",{className:"text-gray-300 font-medium",children:"Service Available"})]})]}),d.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300",children:"Learn More About Us"})]}),d.jsxs(P.div,{initial:{x:50,opacity:0},animate:e?{x:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut",delay:.3},className:"relative",children:[d.jsx("img",{src:"https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",alt:"Luxury restaurant interior with warm ambiance",className:"rounded-2xl shadow-2xl w-full h-96 object-cover"}),d.jsx(P.div,{initial:{scale:0,rotate:-180},animate:e?{scale:1,rotate:0}:{},transition:{duration:.6,delay:.6},className:"absolute -top-8 -left-8 w-24 h-24 bg-brand-yellow rounded-full flex items-center justify-center shadow-lg",children:d.jsx(Da,{className:"text-brand-black text-2xl"})})]})]})})})}const Og=[{id:1,name:"Bull Signature Burger",description:"Double beef patty, special sauce, fresh vegetables, premium bun",price:89900,category:"Burgers",image:"https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:2,name:"Crispy Chicken Deluxe",description:"Tender chicken breast, crispy coating, mayo, lettuce",price:79900,category:"Burgers",image:"https://images.unsplash.com/photo-1553979459-d2229ba7433a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:3,name:"Loaded Bull Fries",description:"Crispy fries, melted cheese, jalapenos, special sauce",price:49900,category:"Sides",image:"https://images.unsplash.com/photo-1518013431117-eb1465fa5752?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:4,name:"Chocolate Thunder Shake",description:"Rich chocolate, vanilla ice cream, whipped cream, cherry",price:39900,category:"Drinks",image:"https://images.unsplash.com/photo-1541518763669-27fef04b14ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:5,name:"Spicy Wings Combo",description:"8 pieces of spicy buffalo wings with ranch dip",price:69900,category:"Appetizers",image:"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:6,name:"Classic Fish & Chips",description:"Beer-battered fish with golden fries and tartar sauce",price:99900,category:"Mains",image:"https://images.unsplash.com/photo-1544982503-9f984c14501a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0}];function Lg(){const t=g.useRef(null),e=ms(t,{once:!0,amount:.2}),[s,n]=g.useState("All"),r=Og,o=["All",...Array.from(new Set(r.map(l=>l.category)))],i=s==="All"?r:r.filter(l=>l.category===s),a=l=>`Rs. ${(l/100).toFixed(0)}`;return d.jsx("section",{id:"menu",ref:t,className:"py-20 bg-gradient-to-br from-brand-black via-brand-black-soft to-brand-black",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs(P.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut"},className:"text-center mb-16",children:[d.jsx(P.span,{initial:{scale:.8,opacity:0},animate:e?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.2},className:"inline-block text-brand-yellow font-bold text-lg uppercase tracking-wider bg-brand-yellow/10 px-6 py-2 rounded-full border border-brand-yellow/20",children:"Our Menu"}),d.jsxs(P.h2,{initial:{y:30,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.3},className:"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-white mt-4 sm:mt-6 mb-4 sm:mb-6",children:[d.jsx("span",{className:"gradient-text",children:"Delicious"})," Selections"]}),d.jsxs(P.p,{initial:{y:20,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.4},className:"text-gray-300 text-lg md:text-xl max-w-3xl mx-auto leading-relaxed",children:["From signature burgers to crispy delights, every bite tells a story of quality and passion.",d.jsxs("span",{className:"text-brand-yellow font-semibold",children:[" ","Crafted with love, served with pride."]})]})]}),d.jsx(P.div,{initial:{y:30,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.5},className:"flex justify-center mb-16",children:d.jsx("div",{className:"flex flex-wrap gap-3 bg-brand-black-soft/80 backdrop-blur-xl rounded-2xl p-3 shadow-2xl border border-brand-yellow/20 glow-effect",children:o.map((l,c)=>d.jsxs(P.button,{initial:{opacity:0,scale:.8},animate:e?{opacity:1,scale:1}:{},transition:{duration:.3,delay:.6+c*.1},whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},onClick:()=>n(l),className:`relative px-6 py-3 rounded-xl font-bold transition-all duration-300 ${s===l?"bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black shadow-lg":"hover:bg-brand-yellow/10 text-white hover:text-brand-yellow"}`,children:[l,s===l&&d.jsx(P.div,{layoutId:"activeFilter",className:"absolute inset-0 bg-gradient-to-r from-brand-yellow to-brand-yellow-light rounded-xl -z-10",initial:!1,transition:{type:"spring",stiffness:380,damping:30}})]},l))})}),d.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8",children:i.map((l,c)=>d.jsxs(P.div,{initial:{y:80,opacity:0,scale:.8},animate:e?{y:0,opacity:1,scale:1}:{},transition:{duration:.8,delay:.15*c,ease:"easeOut",type:"spring",stiffness:100},whileHover:{y:-15,scale:1.03,boxShadow:"0 30px 60px rgba(255, 193, 7, 0.2)",transition:{duration:.3}},whileTap:{scale:.98},className:"group bg-brand-black-soft/60 backdrop-blur-xl border border-brand-yellow/20 rounded-3xl shadow-xl overflow-hidden hover:border-brand-yellow/40 transition-all duration-500 hover-glow",children:[d.jsxs("div",{className:"relative overflow-hidden rounded-t-3xl",children:[d.jsx(P.img,{src:l.image,alt:l.name,className:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110",whileHover:{scale:1.1}}),d.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),!l.available&&d.jsx(P.div,{initial:{opacity:0},animate:{opacity:1},className:"absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center",children:d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-4xl mb-2",children:"😔"}),d.jsx("span",{className:"text-white font-bold text-lg",children:"Currently Unavailable"})]})}),l.available&&d.jsx(P.div,{initial:{opacity:0,y:20},whileHover:{opacity:1,y:0},className:"absolute top-4 right-4 bg-brand-yellow text-brand-black px-3 py-1 rounded-full text-sm font-bold",children:"Available"})]}),d.jsxs("div",{className:"p-4 sm:p-6",children:[d.jsx(P.h3,{className:"text-lg sm:text-xl font-black text-white mb-2 sm:mb-3 group-hover:text-brand-yellow transition-colors duration-300",children:l.name}),d.jsx("p",{className:"text-sm sm:text-base text-gray-300 mb-4 sm:mb-6 leading-relaxed",children:l.description}),d.jsxs("div",{className:"flex justify-between items-center",children:[d.jsx(P.span,{className:"text-xl sm:text-2xl lg:text-3xl font-black gradient-text",whileHover:{scale:1.1},children:a(l.price)}),d.jsxs(P.button,{whileHover:{scale:1.1,boxShadow:"0 10px 20px rgba(255, 193, 7, 0.3)"},whileTap:{scale:.9},disabled:!l.available,className:"bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-4 sm:px-6 py-2 sm:py-3 rounded-full font-bold hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1 sm:gap-2 text-sm sm:text-base",children:[d.jsx(Sd,{className:"w-4 h-4 sm:w-5 sm:h-5"}),d.jsx("span",{children:"Add"})]})]})]})]},l.id))}),d.jsx(P.div,{initial:{y:30,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.5},className:"text-center mt-12",children:d.jsx(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300",children:"View Full Menu"})})]})})}const Fo=[{id:1,url:"https://images.unsplash.com/photo-1512152272829-e3139592d56f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Signature Bull Burger",category:"food",likes:234},{id:2,url:"https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Restaurant Interior",category:"ambiance",likes:189},{id:3,url:"https://images.unsplash.com/photo-1551782450-17144efb9c50?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Gourmet Presentation",category:"food",likes:312},{id:4,url:"https://images.unsplash.com/photo-1565299507177-b0ac66763828?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Chef's Special",category:"food",likes:267},{id:5,url:"https://images.unsplash.com/photo-1544148103-0773bf10d330?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Dining Experience",category:"ambiance",likes:156},{id:6,url:"https://images.unsplash.com/photo-1586190848861-99aa4a171e90?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Premium Burger",category:"food",likes:298}];function Ig(){const t=g.useRef(null),e=ms(t,{once:!0,amount:.2}),[s,n]=g.useState("all"),[r,o]=g.useState([]),i=s==="all"?Fo:Fo.filter(l=>l.category===s),a=l=>{o(c=>c.includes(l)?c.filter(u=>u!==l):[...c,l])};return d.jsx("section",{id:"gallery",ref:t,className:"py-20 bg-gradient-to-b from-brand-black to-brand-black-soft",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[d.jsxs(P.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-12",children:[d.jsxs(P.div,{initial:{scale:.8,opacity:0},animate:e?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.2},className:"inline-flex items-center space-x-2 text-brand-yellow font-bold text-lg uppercase tracking-wider bg-brand-yellow/10 px-6 py-2 rounded-full border border-brand-yellow/20 mb-6",children:[d.jsx(hi,{className:"w-5 h-5"}),d.jsx("span",{children:"Gallery"})]}),d.jsxs(P.h2,{initial:{y:30,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.3},className:"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-white mb-4",children:[d.jsx("span",{className:"gradient-text",children:"Visual"})," Feast"]}),d.jsx(P.p,{initial:{y:20,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.4},className:"text-base sm:text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Every dish is a masterpiece, every moment is memorable. Follow our culinary journey."})]}),d.jsx(P.div,{initial:{y:30,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.5},className:"flex justify-center mb-12",children:d.jsx("div",{className:"flex flex-wrap gap-3 bg-brand-black-soft/80 backdrop-blur-xl rounded-2xl p-3 shadow-2xl border border-brand-yellow/20",children:[{id:"all",label:"All",icon:hi},{id:"food",label:"Food",icon:fi},{id:"ambiance",label:"Ambiance",icon:Cs}].map((l,c)=>{const u=l.icon;return d.jsxs(P.button,{initial:{opacity:0,scale:.8},animate:e?{opacity:1,scale:1}:{},transition:{duration:.3,delay:.6+c*.1},whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},onClick:()=>n(l.id),className:`flex items-center space-x-2 px-4 sm:px-6 py-2 sm:py-3 rounded-xl font-bold transition-all duration-300 ${s===l.id?"bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black shadow-lg":"hover:bg-brand-yellow/10 text-white hover:text-brand-yellow"}`,children:[d.jsx(u,{className:"w-4 h-4"}),d.jsx("span",{className:"text-sm sm:text-base",children:l.label})]},l.id)})})}),d.jsx(P.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.8,delay:.7},className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:i.map((l,c)=>d.jsxs(P.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.8+c*.1},whileHover:{y:-8,scale:1.02},className:"group relative overflow-hidden rounded-2xl bg-brand-black-soft shadow-xl hover:shadow-2xl transition-all duration-500",children:[d.jsxs("div",{className:"aspect-square overflow-hidden",children:[d.jsx("img",{src:l.url,alt:l.alt,className:"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"}),d.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),d.jsxs("div",{className:"absolute inset-0 flex flex-col justify-end p-4 sm:p-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[d.jsx("h3",{className:"text-white font-bold text-lg mb-2",children:l.alt}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Cs,{className:"w-4 h-4 text-brand-yellow"}),d.jsx("span",{className:"text-white text-sm",children:"@bullbuster"})]}),d.jsxs(P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>a(l.id),className:"flex items-center space-x-1 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1",children:[d.jsx(fi,{className:`w-4 h-4 ${r.includes(l.id)?"text-red-500 fill-red-500":"text-white"}`}),d.jsx("span",{className:"text-white text-sm",children:r.includes(l.id)?l.likes+1:l.likes})]})]})]})]},l.id))}),d.jsx(P.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:1.2},className:"text-center mt-16",children:d.jsxs(P.button,{whileHover:{scale:1.05,boxShadow:"0 20px 40px rgba(255, 193, 7, 0.3)"},whileTap:{scale:.95},className:"inline-flex items-center space-x-3 bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-300",children:[d.jsx(Cs,{className:"w-6 h-6"}),d.jsx("span",{children:"Follow @BullBuster"})]})})]})})}const xn=[{id:"confirmed",label:"Order Confirmed",description:"Your order has been received and confirmed",icon:pd},{id:"preparing",label:"Preparing",description:"Our chefs are preparing your delicious meal",icon:Oa},{id:"out_for_delivery",label:"Out for Delivery",description:"Your order is on its way to you",icon:fd},{id:"delivered",label:"Delivered",description:"Order delivered successfully",icon:xd}];function Fg(){const t=g.useRef(null),e=ms(t,{once:!0,amount:.2}),[s,n]=g.useState(""),[r,o]=g.useState(""),{data:i,isLoading:a,error:l}=Ju({queryKey:["/api/orders",r],enabled:!!r}),c=()=>{s.trim()&&o(s.trim())},u=f=>{if(!i)return"pending";const m=xn.findIndex(y=>y.id===i.status),v=xn.findIndex(y=>y.id===f);return v<m?"completed":v===m?"active":"pending"},h=f=>new Date(f).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0});return d.jsx("section",{id:"track",ref:t,className:"py-20 bg-brand-dark-gray",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[d.jsxs(P.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[d.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Track Your Order"}),d.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Real-Time Updates"}),d.jsx("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"Stay informed with live tracking and precise delivery estimates."})]}),d.jsxs("div",{className:"max-w-4xl mx-auto",children:[d.jsx(P.div,{initial:{y:30,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6,delay:.2},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl p-8 shadow-lg mb-12",children:d.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[d.jsx("input",{type:"text",placeholder:"Enter your order ID (e.g., BB123456789)",value:s,onChange:f=>n(f.target.value),className:"flex-1 px-6 py-4 border border-brand-yellow/30 bg-black/20 text-white placeholder-gray-400 rounded-full focus:border-brand-yellow focus:outline-none transition-colors duration-300",onKeyPress:f=>f.key==="Enter"&&c()}),d.jsxs(P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:c,disabled:a,className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 flex items-center gap-2 disabled:opacity-50",children:[d.jsx(pi,{className:"w-5 h-5"}),a?"Searching...":"Track Order"]})]})}),l&&d.jsx(P.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl p-8 shadow-lg text-center",children:d.jsxs("div",{className:"text-red-500 mb-4",children:[d.jsx(pi,{className:"w-12 h-12 mx-auto mb-2"}),d.jsx("h3",{className:"text-xl font-bold text-white",children:"Order Not Found"}),d.jsx("p",{className:"text-gray-300 mt-2",children:"Please check your order number and try again."})]})}),i&&d.jsxs(P.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},transition:{duration:.6},className:"bg-black/40 backdrop-blur-sm border border-brand-yellow/20 rounded-2xl p-8 shadow-lg",children:[d.jsxs("div",{className:"mb-8",children:[d.jsxs("h3",{className:"text-2xl font-black text-white mb-2",children:["Order #",i.orderNumber]}),d.jsxs("p",{className:"text-gray-300",children:["Customer: ",i.customerName]}),d.jsxs("p",{className:"text-gray-300",children:["Total: Rs. ",(i.total/100).toFixed(0)]}),d.jsx("p",{className:"text-gray-300",children:"Estimated delivery: 25-30 minutes"})]}),d.jsx("div",{className:"space-y-6",children:xn.map((f,m)=>{const v=u(f.id),y=f.icon;return d.jsxs(P.div,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{duration:.4,delay:.1*m},className:"flex items-center space-x-4",children:[d.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center font-bold transition-all duration-300 ${v==="completed"?"bg-brand-yellow text-brand-black":v==="active"?"bg-brand-yellow text-brand-black animate-pulse":"bg-gray-200 text-gray-400"}`,children:d.jsx(y,{className:"w-6 h-6"})}),d.jsxs("div",{className:"flex-1",children:[d.jsx("h4",{className:`font-semibold ${v==="pending"?"text-gray-500":"text-white"}`,children:f.label}),d.jsx("p",{className:`text-sm ${v==="pending"?"text-gray-500":"text-gray-300"}`,children:f.description}),v!=="pending"&&d.jsx("span",{className:"text-xs text-gray-400",children:v==="active"?"In Progress":h(i.updatedAt)})]})]},f.id)})})]})]})]})})}function Bg(){const t=g.useRef(null),e=ms(t,{once:!0,amount:.2}),{toast:s}=Ea();or();const[n,r]=g.useState({name:"",email:"",subject:"",message:""}),o=ed({mutationFn:async l=>(await td("POST","/api/contact",l)).json(),onSuccess:()=>{s({title:"Message Sent!",description:"Thank you for contacting us. We'll get back to you soon."}),r({name:"",email:"",subject:"",message:""})},onError:()=>{s({title:"Error",description:"Failed to send message. Please try again.",variant:"destructive"})}}),i=l=>{l.preventDefault(),o.mutate(n)},a=l=>{r(c=>({...c,[l.target.name]:l.target.value}))};return d.jsxs("section",{id:"contact",ref:t,className:"py-20 bg-brand-black text-white relative overflow-hidden",children:[d.jsxs("div",{className:"absolute inset-0 opacity-10",children:[d.jsx("div",{className:"text-9xl absolute top-20 left-20",children:"🍔"}),d.jsx("div",{className:"text-7xl absolute bottom-32 right-32",children:"🍕"})]}),d.jsxs("div",{className:"max-w-7xl mx-auto px-6 relative z-10",children:[d.jsxs(P.div,{initial:{y:50,opacity:0},animate:e?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[d.jsx("span",{className:"text-brand-yellow font-semibold text-lg uppercase tracking-wider",children:"Contact Us"}),d.jsx("h2",{className:"text-5xl font-black text-white mt-4 mb-6",children:"Get In Touch"}),d.jsx("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"Visit our locations or reach out to us for any inquiries."})]}),d.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16",children:[d.jsxs(P.div,{initial:{x:-50,opacity:0},animate:e?{x:0,opacity:1}:{},transition:{duration:.6,delay:.2},className:"space-y-8",children:[d.jsxs("div",{className:"flex items-start space-x-4",children:[d.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:d.jsx(bd,{className:"text-brand-black"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Main Location"}),d.jsx("p",{className:"text-gray-300",children:"MM Alam Road, Gulberg III, Lahore, Pakistan"})]})]}),d.jsxs("div",{className:"flex items-start space-x-4",children:[d.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:d.jsx(Td,{className:"text-brand-black"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Phone"}),d.jsx("p",{className:"text-gray-300",children:"+92 42 1234 5678"})]})]}),d.jsxs("div",{className:"flex items-start space-x-4",children:[d.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:d.jsx(Va,{className:"text-brand-black"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Hours"}),d.jsx("p",{className:"text-gray-300",children:"Open 24/7 for your convenience"})]})]}),d.jsxs("div",{className:"flex items-start space-x-4",children:[d.jsx("div",{className:"w-12 h-12 bg-brand-yellow rounded-full flex items-center justify-center",children:d.jsx(vd,{className:"text-brand-black"})}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Email"}),d.jsx("p",{className:"text-gray-300",children:"<EMAIL>"})]})]})]}),d.jsxs(P.div,{initial:{x:50,opacity:0},animate:e?{x:0,opacity:1}:{},transition:{duration:.6,delay:.4},className:"glass-effect rounded-2xl p-8",children:[d.jsx("h3",{className:"text-2xl font-bold text-white mb-6",children:"Send us a Message"}),d.jsxs("form",{onSubmit:i,className:"space-y-6",children:[d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[d.jsx("input",{type:"text",name:"name",placeholder:"Your Name",value:n.name,onChange:a,required:!0,className:"bg-white/20 border border-white/30 rounded-full px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),d.jsx("input",{type:"email",name:"email",placeholder:"Your Email",value:n.email,onChange:a,required:!0,className:"bg-white/20 border border-white/30 rounded-full px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"})]}),d.jsx("input",{type:"text",name:"subject",placeholder:"Subject",value:n.subject,onChange:a,required:!0,className:"w-full bg-white/20 border border-white/30 rounded-full px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),d.jsx("textarea",{name:"message",placeholder:"Your Message",rows:5,value:n.message,onChange:a,required:!0,className:"w-full bg-white/20 border border-white/30 rounded-2xl px-6 py-4 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300 resize-none"}),d.jsx(P.button,{type:"submit",disabled:o.isPending,whileHover:{scale:1.05},whileTap:{scale:.95},className:"w-full bg-brand-yellow text-brand-black py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 disabled:opacity-50",children:o.isPending?"Sending...":"Send Message"})]})]})]})]})]})}function Ug(){const[t,e]=g.useState(""),s=r=>{r.preventDefault(),console.log("Newsletter subscription:",t),e("")},n=r=>{const o=document.getElementById(r);o&&o.scrollIntoView({behavior:"smooth",block:"start"})};return d.jsx("footer",{className:"bg-brand-black-soft text-white py-16",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-6",children:[d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:[d.jsxs(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6},viewport:{once:!0},children:[d.jsxs("div",{className:"flex items-center space-x-2 mb-6",children:[d.jsx(Cd,{className:"text-3xl text-brand-yellow"}),d.jsx("span",{className:"text-2xl font-black text-white",children:"BullBuster"})]}),d.jsx("p",{className:"text-gray-400 mb-6",children:"Premium fast food experience in the heart of Lahore, serving quality meals with passion and excellence."}),d.jsxs("div",{className:"flex space-x-4",children:[d.jsx(P.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:d.jsx(gd,{className:"w-5 h-5"})}),d.jsx(P.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:d.jsx(Cs,{className:"w-5 h-5"})}),d.jsx(P.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:d.jsx(kd,{className:"w-5 h-5"})})]})]}),d.jsxs(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.1},viewport:{once:!0},children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Quick Links"}),d.jsx("ul",{className:"space-y-3",children:["home","about","menu","gallery","contact"].map(r=>d.jsx("li",{children:d.jsx("button",{onClick:()=>n(r),className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300 capitalize",children:r==="home"?"Home":r==="about"?"About Us":r.charAt(0).toUpperCase()+r.slice(1)})},r))})]}),d.jsxs(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Services"}),d.jsxs("ul",{className:"space-y-3",children:[d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Dine In"})}),d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Takeaway"})}),d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Home Delivery"})}),d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Catering"})}),d.jsx("li",{children:d.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Party Orders"})})]})]}),d.jsxs(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.3},viewport:{once:!0},children:[d.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Newsletter"}),d.jsx("p",{className:"text-gray-400 mb-4",children:"Subscribe to get special offers and updates."}),d.jsxs("form",{onSubmit:s,className:"flex",children:[d.jsx("input",{type:"email",placeholder:"Your email",value:t,onChange:r=>e(r.target.value),required:!0,className:"flex-1 bg-white/20 border border-white/30 rounded-l-full px-4 py-3 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),d.jsx(P.button,{type:"submit",whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-6 py-3 rounded-r-full hover:bg-brand-yellow-light transition-colors duration-300",children:d.jsx(jd,{className:"w-5 h-5"})})]})]})]}),d.jsx(P.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"border-t border-gray-700 pt-8 text-center",children:d.jsx("p",{className:"text-gray-400",children:"© 2024 BullBsuter. All rights reserved. | Privacy Policy | Terms of Service"})})]})})}function _g(){return d.jsxs("div",{className:"min-h-screen bg-brand-black",children:[d.jsx(Ng,{}),d.jsx(Dg,{}),d.jsx(Vg,{}),d.jsx(Lg,{}),d.jsx(Ig,{}),d.jsx(Fg,{}),d.jsx(Bg,{}),d.jsx(Ug,{})]})}function zg(){return d.jsxs(wu,{children:[d.jsx(Jr,{path:"/",component:_g}),d.jsx(Jr,{component:Vh})]})}function Hg(){return d.jsx(_u,{client:nd,children:d.jsxs(kh,{children:[d.jsx(uh,{}),d.jsx(zg,{})]})})}na(document.getElementById("root")).render(d.jsx(Hg,{}));
