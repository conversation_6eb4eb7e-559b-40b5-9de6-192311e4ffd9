import{j as u,P as Br,c as Zo,a as Fr,u as zr,A as Jo,b as je,d as Qo,D as ea,C as ta,e as na,f as sa}from"./ui-CUOK5ZcK.js";import{a as ra,r as m,R as ia}from"./vendor-DbAb9B2p.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();var _r,os=ra;_r=os.createRoot,os.hydrateRoot;function oa(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var n,s,r,o,i=[],l="",a=e.split("/");for(a[0]||a.shift();r=a.shift();)n=r[0],n==="*"?(i.push(n),l+=r[1]==="?"?"(?:/(.*))?":"/(.*)"):n===":"?(s=r.indexOf("?",1),o=r.indexOf(".",1),i.push(r.substring(1,~s?s:~o?o:r.length)),l+=~s&&!~o?"(?:/([^/]+?))?":"/([^/]+?)",~o&&(l+=(~s?"?":"")+"\\"+r.substring(o))):l+="/"+r;return{keys:i,pattern:new RegExp("^"+l+(t?"(?=$|/)":"/?$"),"i")}}var Hr={exports:{}},Ur={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Me=m;function aa(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var la=typeof Object.is=="function"?Object.is:aa,ca=Me.useState,ua=Me.useEffect,da=Me.useLayoutEffect,ha=Me.useDebugValue;function fa(e,t){var n=t(),s=ca({inst:{value:n,getSnapshot:t}}),r=s[0].inst,o=s[1];return da(function(){r.value=n,r.getSnapshot=t,Et(r)&&o({inst:r})},[e,n,t]),ua(function(){return Et(r)&&o({inst:r}),e(function(){Et(r)&&o({inst:r})})},[e]),ha(n),n}function Et(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!la(e,n)}catch{return!0}}function pa(e,t){return t()}var ma=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?pa:fa;Ur.useSyncExternalStore=Me.useSyncExternalStore!==void 0?Me.useSyncExternalStore:ma;Hr.exports=Ur;var ga=Hr.exports;const ya=ia.useInsertionEffect,xa=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ba=xa?m.useLayoutEffect:m.useEffect,va=ya||ba,Wr=e=>{const t=m.useRef([e,(...n)=>t[0](...n)]).current;return va(()=>{t[0]=e}),t[1]},wa="popstate",yn="pushState",xn="replaceState",Ta="hashchange",as=[wa,yn,xn,Ta],Pa=e=>{for(const t of as)addEventListener(t,e);return()=>{for(const t of as)removeEventListener(t,e)}},Gr=(e,t)=>ga.useSyncExternalStore(Pa,e,t),Sa=()=>location.search,ja=({ssrSearch:e=""}={})=>Gr(Sa,()=>e),ls=()=>location.pathname,Ca=({ssrPath:e}={})=>Gr(ls,e?()=>e:ls),Aa=(e,{replace:t=!1,state:n=null}={})=>history[t?xn:yn](n,"",e),ka=(e={})=>[Ca(e),Aa],cs=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[cs]>"u"){for(const e of[yn,xn]){const t=history[e];history[e]=function(){const n=t.apply(this,arguments),s=new Event(e);return s.arguments=arguments,dispatchEvent(s),n}}Object.defineProperty(window,cs,{value:!0})}const Na=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",$r=(e="")=>e==="/"?"":e,Va=(e,t)=>e[0]==="~"?e.slice(1):$r(t)+e,Ea=(e="",t)=>Na(us($r(e)),us(t)),us=e=>{try{return decodeURI(e)}catch{return e}},Kr={hook:ka,searchHook:ja,parser:oa,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},qr=m.createContext(Kr),wt=()=>m.useContext(qr),Yr={},Xr=m.createContext(Yr),Ma=()=>m.useContext(Xr),bn=e=>{const[t,n]=e.hook(e);return[Ea(e.base,t),Wr((s,r)=>n(Va(s,e.base),r))]},Zr=(e,t,n,s)=>{const{pattern:r,keys:o}=t instanceof RegExp?{keys:!1,pattern:t}:e(t||"*",s),i=r.exec(n)||[],[l,...a]=i;return l!==void 0?[!0,(()=>{const c=o!==!1?Object.fromEntries(o.map((h,f)=>[h,a[f]])):i.groups;let d={...a};return c&&Object.assign(d,c),d})(),...s?[l]:[]]:[!1,null]},Ra=({children:e,...t})=>{var d,h;const n=wt(),s=t.hook?Kr:n;let r=s;const[o,i]=((d=t.ssrPath)==null?void 0:d.split("?"))??[];i&&(t.ssrSearch=i,t.ssrPath=o),t.hrefs=t.hrefs??((h=t.hook)==null?void 0:h.hrefs);let l=m.useRef({}),a=l.current,c=a;for(let f in s){const p=f==="base"?s[f]+(t[f]||""):t[f]||s[f];a===c&&p!==c[f]&&(l.current=c={...c}),c[f]=p,p!==s[f]&&(r=c)}return m.createElement(qr.Provider,{value:r,children:e})},ds=({children:e,component:t},n)=>t?m.createElement(t,{params:n}):typeof e=="function"?e(n):e,Da=e=>{let t=m.useRef(Yr),n=t.current;for(const s in e)e[s]!==n[s]&&(n=e);return Object.keys(e).length===0&&(n=e),t.current=n},hs=({path:e,nest:t,match:n,...s})=>{const r=wt(),[o]=bn(r),[i,l,a]=n??Zr(r.parser,e,o,t),c=Da({...Ma(),...l});if(!i)return null;const d=a?m.createElement(Ra,{base:a},ds(s,c)):ds(s,c);return m.createElement(Xr.Provider,{value:c,children:d})};m.forwardRef((e,t)=>{const n=wt(),[s,r]=bn(n),{to:o="",href:i=o,onClick:l,asChild:a,children:c,className:d,replace:h,state:f,...p}=e,x=Wr(y=>{y.ctrlKey||y.metaKey||y.altKey||y.shiftKey||y.button!==0||(l==null||l(y),y.defaultPrevented||(y.preventDefault(),r(i,e)))}),g=n.hrefs(i[0]==="~"?i.slice(1):n.base+i,n);return a&&m.isValidElement(c)?m.cloneElement(c,{onClick:x,href:g}):m.createElement("a",{...p,onClick:x,href:g,className:d!=null&&d.call?d(s===i):d,children:c,ref:t})});const Jr=e=>Array.isArray(e)?e.flatMap(t=>Jr(t&&t.type===m.Fragment?t.props.children:t)):[e],La=({children:e,location:t})=>{const n=wt(),[s]=bn(n);for(const r of Jr(e)){let o=0;if(m.isValidElement(r)&&(o=Zr(n.parser,r.props.path,t||s,r.props.nest))[0])return m.cloneElement(r,{match:o})}return null};var Ia="VisuallyHidden",Qr=m.forwardRef((e,t)=>u.jsx(Br.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Qr.displayName=Ia;var Oa=Qr,[Tt,bp]=Zo("Tooltip",[Fr]),vn=Fr(),ei="TooltipProvider",Ba=700,fs="tooltip.open",[Fa,ti]=Tt(ei),ni=e=>{const{__scopeTooltip:t,delayDuration:n=Ba,skipDelayDuration:s=300,disableHoverableContent:r=!1,children:o}=e,i=m.useRef(!0),l=m.useRef(!1),a=m.useRef(0);return m.useEffect(()=>{const c=a.current;return()=>window.clearTimeout(c)},[]),u.jsx(Fa,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:m.useCallback(()=>{window.clearTimeout(a.current),i.current=!1},[]),onClose:m.useCallback(()=>{window.clearTimeout(a.current),a.current=window.setTimeout(()=>i.current=!0,s)},[s]),isPointerInTransitRef:l,onPointerInTransitChange:m.useCallback(c=>{l.current=c},[]),disableHoverableContent:r,children:o})};ni.displayName=ei;var si="Tooltip",[vp,Pt]=Tt(si),Yt="TooltipTrigger",za=m.forwardRef((e,t)=>{const{__scopeTooltip:n,...s}=e,r=Pt(Yt,n),o=ti(Yt,n),i=vn(n),l=m.useRef(null),a=zr(t,l,r.onTriggerChange),c=m.useRef(!1),d=m.useRef(!1),h=m.useCallback(()=>c.current=!1,[]);return m.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),u.jsx(Jo,{asChild:!0,...i,children:u.jsx(Br.button,{"aria-describedby":r.open?r.contentId:void 0,"data-state":r.stateAttribute,...s,ref:a,onPointerMove:je(e.onPointerMove,f=>{f.pointerType!=="touch"&&!d.current&&!o.isPointerInTransitRef.current&&(r.onTriggerEnter(),d.current=!0)}),onPointerLeave:je(e.onPointerLeave,()=>{r.onTriggerLeave(),d.current=!1}),onPointerDown:je(e.onPointerDown,()=>{r.open&&r.onClose(),c.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:je(e.onFocus,()=>{c.current||r.onOpen()}),onBlur:je(e.onBlur,r.onClose),onClick:je(e.onClick,r.onClose)})})});za.displayName=Yt;var _a="TooltipPortal",[wp,Ha]=Tt(_a,{forceMount:void 0}),Re="TooltipContent",ri=m.forwardRef((e,t)=>{const n=Ha(Re,e.__scopeTooltip),{forceMount:s=n.forceMount,side:r="top",...o}=e,i=Pt(Re,e.__scopeTooltip);return u.jsx(Qo,{present:s||i.open,children:i.disableHoverableContent?u.jsx(ii,{side:r,...o,ref:t}):u.jsx(Ua,{side:r,...o,ref:t})})}),Ua=m.forwardRef((e,t)=>{const n=Pt(Re,e.__scopeTooltip),s=ti(Re,e.__scopeTooltip),r=m.useRef(null),o=zr(t,r),[i,l]=m.useState(null),{trigger:a,onClose:c}=n,d=r.current,{onPointerInTransitChange:h}=s,f=m.useCallback(()=>{l(null),h(!1)},[h]),p=m.useCallback((x,g)=>{const y=x.currentTarget,b={x:x.clientX,y:x.clientY},w=qa(b,y.getBoundingClientRect()),T=Ya(b,w),A=Xa(g.getBoundingClientRect()),P=Ja([...T,...A]);l(P),h(!0)},[h]);return m.useEffect(()=>()=>f(),[f]),m.useEffect(()=>{if(a&&d){const x=y=>p(y,d),g=y=>p(y,a);return a.addEventListener("pointerleave",x),d.addEventListener("pointerleave",g),()=>{a.removeEventListener("pointerleave",x),d.removeEventListener("pointerleave",g)}}},[a,d,p,f]),m.useEffect(()=>{if(i){const x=g=>{const y=g.target,b={x:g.clientX,y:g.clientY},w=(a==null?void 0:a.contains(y))||(d==null?void 0:d.contains(y)),T=!Za(b,i);w?f():T&&(f(),c())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[a,d,i,c,f]),u.jsx(ii,{...e,ref:o})}),[Wa,Ga]=Tt(si,{isInside:!1}),$a=sa("TooltipContent"),ii=m.forwardRef((e,t)=>{const{__scopeTooltip:n,children:s,"aria-label":r,onEscapeKeyDown:o,onPointerDownOutside:i,...l}=e,a=Pt(Re,n),c=vn(n),{onClose:d}=a;return m.useEffect(()=>(document.addEventListener(fs,d),()=>document.removeEventListener(fs,d)),[d]),m.useEffect(()=>{if(a.trigger){const h=f=>{const p=f.target;p!=null&&p.contains(a.trigger)&&d()};return window.addEventListener("scroll",h,{capture:!0}),()=>window.removeEventListener("scroll",h,{capture:!0})}},[a.trigger,d]),u.jsx(ea,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:h=>h.preventDefault(),onDismiss:d,children:u.jsxs(ta,{"data-state":a.stateAttribute,...c,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[u.jsx($a,{children:s}),u.jsx(Wa,{scope:n,isInside:!0,children:u.jsx(Oa,{id:a.contentId,role:"tooltip",children:r||s})})]})})});ri.displayName=Re;var oi="TooltipArrow",Ka=m.forwardRef((e,t)=>{const{__scopeTooltip:n,...s}=e,r=vn(n);return Ga(oi,n).isInside?null:u.jsx(na,{...r,...s,ref:t})});Ka.displayName=oi;function qa(e,t){const n=Math.abs(t.top-e.y),s=Math.abs(t.bottom-e.y),r=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,s,r,o)){case o:return"left";case r:return"right";case n:return"top";case s:return"bottom";default:throw new Error("unreachable")}}function Ya(e,t,n=5){const s=[];switch(t){case"top":s.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":s.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":s.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":s.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return s}function Xa(e){const{top:t,right:n,bottom:s,left:r}=e;return[{x:r,y:t},{x:n,y:t},{x:n,y:s},{x:r,y:s}]}function Za(e,t){const{x:n,y:s}=e;let r=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const l=t[o].x,a=t[o].y,c=t[i].x,d=t[i].y;a>s!=d>s&&n<(c-l)*(s-a)/(d-a)+l&&(r=!r)}return r}function Ja(e){const t=e.slice();return t.sort((n,s)=>n.x<s.x?-1:n.x>s.x?1:n.y<s.y?-1:n.y>s.y?1:0),Qa(t)}function Qa(e){if(e.length<=1)return e.slice();const t=[];for(let s=0;s<e.length;s++){const r=e[s];for(;t.length>=2;){const o=t[t.length-1],i=t[t.length-2];if((o.x-i.x)*(r.y-i.y)>=(o.y-i.y)*(r.x-i.x))t.pop();else break}t.push(r)}t.pop();const n=[];for(let s=e.length-1;s>=0;s--){const r=e[s];for(;n.length>=2;){const o=n[n.length-1],i=n[n.length-2];if((o.x-i.x)*(r.y-i.y)>=(o.y-i.y)*(r.x-i.x))n.pop();else break}n.push(r)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var el=ni,ai=ri;function li(e){var t,n,s="";if(typeof e=="string"||typeof e=="number")s+=e;else if(typeof e=="object")if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(n=li(e[t]))&&(s&&(s+=" "),s+=n)}else for(n in e)e[n]&&(s&&(s+=" "),s+=n);return s}function tl(){for(var e,t,n=0,s="",r=arguments.length;n<r;n++)(e=arguments[n])&&(t=li(e))&&(s&&(s+=" "),s+=t);return s}const wn="-",nl=e=>{const t=rl(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:i=>{const l=i.split(wn);return l[0]===""&&l.length!==1&&l.shift(),ci(l,t)||sl(i)},getConflictingClassGroupIds:(i,l)=>{const a=n[i]||[];return l&&s[i]?[...a,...s[i]]:a}}},ci=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],s=t.nextPart.get(n),r=s?ci(e.slice(1),s):void 0;if(r)return r;if(t.validators.length===0)return;const o=e.join(wn);return(i=t.validators.find(({validator:l})=>l(o)))==null?void 0:i.classGroupId},ps=/^\[(.+)\]$/,sl=e=>{if(ps.test(e)){const t=ps.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},rl=e=>{const{theme:t,prefix:n}=e,s={nextPart:new Map,validators:[]};return ol(Object.entries(e.classGroups),n).forEach(([o,i])=>{Xt(i,s,o,t)}),s},Xt=(e,t,n,s)=>{e.forEach(r=>{if(typeof r=="string"){const o=r===""?t:ms(t,r);o.classGroupId=n;return}if(typeof r=="function"){if(il(r)){Xt(r(s),t,n,s);return}t.validators.push({validator:r,classGroupId:n});return}Object.entries(r).forEach(([o,i])=>{Xt(i,ms(t,o),n,s)})})},ms=(e,t)=>{let n=e;return t.split(wn).forEach(s=>{n.nextPart.has(s)||n.nextPart.set(s,{nextPart:new Map,validators:[]}),n=n.nextPart.get(s)}),n},il=e=>e.isThemeGetter,ol=(e,t)=>t?e.map(([n,s])=>{const r=s.map(o=>typeof o=="string"?t+o:typeof o=="object"?Object.fromEntries(Object.entries(o).map(([i,l])=>[t+i,l])):o);return[n,r]}):e,al=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,s=new Map;const r=(o,i)=>{n.set(o,i),t++,t>e&&(t=0,s=n,n=new Map)};return{get(o){let i=n.get(o);if(i!==void 0)return i;if((i=s.get(o))!==void 0)return r(o,i),i},set(o,i){n.has(o)?n.set(o,i):r(o,i)}}},ui="!",ll=e=>{const{separator:t,experimentalParseClassName:n}=e,s=t.length===1,r=t[0],o=t.length,i=l=>{const a=[];let c=0,d=0,h;for(let y=0;y<l.length;y++){let b=l[y];if(c===0){if(b===r&&(s||l.slice(y,y+o)===t)){a.push(l.slice(d,y)),d=y+o;continue}if(b==="/"){h=y;continue}}b==="["?c++:b==="]"&&c--}const f=a.length===0?l:l.substring(d),p=f.startsWith(ui),x=p?f.substring(1):f,g=h&&h>d?h-d:void 0;return{modifiers:a,hasImportantModifier:p,baseClassName:x,maybePostfixModifierPosition:g}};return n?l=>n({className:l,parseClassName:i}):i},cl=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(s=>{s[0]==="["?(t.push(...n.sort(),s),n=[]):n.push(s)}),t.push(...n.sort()),t},ul=e=>({cache:al(e.cacheSize),parseClassName:ll(e),...nl(e)}),dl=/\s+/,hl=(e,t)=>{const{parseClassName:n,getClassGroupId:s,getConflictingClassGroupIds:r}=t,o=[],i=e.trim().split(dl);let l="";for(let a=i.length-1;a>=0;a-=1){const c=i[a],{modifiers:d,hasImportantModifier:h,baseClassName:f,maybePostfixModifierPosition:p}=n(c);let x=!!p,g=s(x?f.substring(0,p):f);if(!g){if(!x){l=c+(l.length>0?" "+l:l);continue}if(g=s(f),!g){l=c+(l.length>0?" "+l:l);continue}x=!1}const y=cl(d).join(":"),b=h?y+ui:y,w=b+g;if(o.includes(w))continue;o.push(w);const T=r(g,x);for(let A=0;A<T.length;++A){const P=T[A];o.push(b+P)}l=c+(l.length>0?" "+l:l)}return l};function fl(){let e=0,t,n,s="";for(;e<arguments.length;)(t=arguments[e++])&&(n=di(t))&&(s&&(s+=" "),s+=n);return s}const di=e=>{if(typeof e=="string")return e;let t,n="";for(let s=0;s<e.length;s++)e[s]&&(t=di(e[s]))&&(n&&(n+=" "),n+=t);return n};function pl(e,...t){let n,s,r,o=i;function i(a){const c=t.reduce((d,h)=>h(d),e());return n=ul(c),s=n.cache.get,r=n.cache.set,o=l,l(a)}function l(a){const c=s(a);if(c)return c;const d=hl(a,n);return r(a,d),d}return function(){return o(fl.apply(null,arguments))}}const E=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},hi=/^\[(?:([a-z-]+):)?(.+)\]$/i,ml=/^\d+\/\d+$/,gl=new Set(["px","full","screen"]),yl=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,xl=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,bl=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,vl=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,wl=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ne=e=>Ee(e)||gl.has(e)||ml.test(e),ae=e=>Ie(e,"length",Nl),Ee=e=>!!e&&!Number.isNaN(Number(e)),Mt=e=>Ie(e,"number",Ee),Fe=e=>!!e&&Number.isInteger(Number(e)),Tl=e=>e.endsWith("%")&&Ee(e.slice(0,-1)),j=e=>hi.test(e),le=e=>yl.test(e),Pl=new Set(["length","size","percentage"]),Sl=e=>Ie(e,Pl,fi),jl=e=>Ie(e,"position",fi),Cl=new Set(["image","url"]),Al=e=>Ie(e,Cl,El),kl=e=>Ie(e,"",Vl),ze=()=>!0,Ie=(e,t,n)=>{const s=hi.exec(e);return s?s[1]?typeof t=="string"?s[1]===t:t.has(s[1]):n(s[2]):!1},Nl=e=>xl.test(e)&&!bl.test(e),fi=()=>!1,Vl=e=>vl.test(e),El=e=>wl.test(e),Ml=()=>{const e=E("colors"),t=E("spacing"),n=E("blur"),s=E("brightness"),r=E("borderColor"),o=E("borderRadius"),i=E("borderSpacing"),l=E("borderWidth"),a=E("contrast"),c=E("grayscale"),d=E("hueRotate"),h=E("invert"),f=E("gap"),p=E("gradientColorStops"),x=E("gradientColorStopPositions"),g=E("inset"),y=E("margin"),b=E("opacity"),w=E("padding"),T=E("saturate"),A=E("scale"),P=E("sepia"),N=E("skew"),I=E("space"),C=E("translate"),O=()=>["auto","contain","none"],_=()=>["auto","hidden","clip","visible","scroll"],X=()=>["auto",j,t],V=()=>[j,t],rt=()=>["",ne,ae],me=()=>["auto",Ee,j],Vt=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Be=()=>["solid","dashed","dotted","double","none"],B=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>["start","end","center","between","around","evenly","stretch"],ee=()=>["","0",j],Se=()=>["auto","avoid","all","avoid-page","page","left","right","column"],te=()=>[Ee,j];return{cacheSize:500,separator:":",theme:{colors:[ze],spacing:[ne,ae],blur:["none","",le,j],brightness:te(),borderColor:[e],borderRadius:["none","","full",le,j],borderSpacing:V(),borderWidth:rt(),contrast:te(),grayscale:ee(),hueRotate:te(),invert:ee(),gap:V(),gradientColorStops:[e],gradientColorStopPositions:[Tl,ae],inset:X(),margin:X(),opacity:te(),padding:V(),saturate:te(),scale:te(),sepia:ee(),skew:te(),space:V(),translate:V()},classGroups:{aspect:[{aspect:["auto","square","video",j]}],container:["container"],columns:[{columns:[le]}],"break-after":[{"break-after":Se()}],"break-before":[{"break-before":Se()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Vt(),j]}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Fe,j]}],basis:[{basis:X()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",j]}],grow:[{grow:ee()}],shrink:[{shrink:ee()}],order:[{order:["first","last","none",Fe,j]}],"grid-cols":[{"grid-cols":[ze]}],"col-start-end":[{col:["auto",{span:["full",Fe,j]},j]}],"col-start":[{"col-start":me()}],"col-end":[{"col-end":me()}],"grid-rows":[{"grid-rows":[ze]}],"row-start-end":[{row:["auto",{span:[Fe,j]},j]}],"row-start":[{"row-start":me()}],"row-end":[{"row-end":me()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",j]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",j]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...W()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...W(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...W(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[w]}],px:[{px:[w]}],py:[{py:[w]}],ps:[{ps:[w]}],pe:[{pe:[w]}],pt:[{pt:[w]}],pr:[{pr:[w]}],pb:[{pb:[w]}],pl:[{pl:[w]}],m:[{m:[y]}],mx:[{mx:[y]}],my:[{my:[y]}],ms:[{ms:[y]}],me:[{me:[y]}],mt:[{mt:[y]}],mr:[{mr:[y]}],mb:[{mb:[y]}],ml:[{ml:[y]}],"space-x":[{"space-x":[I]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[I]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",j,t]}],"min-w":[{"min-w":[j,t,"min","max","fit"]}],"max-w":[{"max-w":[j,t,"none","full","min","max","fit","prose",{screen:[le]},le]}],h:[{h:[j,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[j,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[j,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[j,t,"auto","min","max","fit"]}],"font-size":[{text:["base",le,ae]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Mt]}],"font-family":[{font:[ze]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",j]}],"line-clamp":[{"line-clamp":["none",Ee,Mt]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",ne,j]}],"list-image":[{"list-image":["none",j]}],"list-style-type":[{list:["none","disc","decimal",j]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Be(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",ne,ae]}],"underline-offset":[{"underline-offset":["auto",ne,j]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",j]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",j]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Vt(),jl]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Sl]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Al]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:[...Be(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:Be()}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["",...Be()]}],"outline-offset":[{"outline-offset":[ne,j]}],"outline-w":[{outline:[ne,ae]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:rt()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[ne,ae]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",le,kl]}],"shadow-color":[{shadow:[ze]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":[...B(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":B()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[s]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",le,j]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[h]}],saturate:[{saturate:[T]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[s]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[T]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",j]}],duration:[{duration:te()}],ease:[{ease:["linear","in","out","in-out",j]}],delay:[{delay:te()}],animate:[{animate:["none","spin","ping","pulse","bounce",j]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[A]}],"scale-x":[{"scale-x":[A]}],"scale-y":[{"scale-y":[A]}],rotate:[{rotate:[Fe,j]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[N]}],"skew-y":[{"skew-y":[N]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",j]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",j]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",j]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[ne,ae,Mt]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Rl=pl(Ml);function Te(...e){return Rl(tl(e))}const Dl=el,Ll=m.forwardRef(({className:e,sideOffset:t=4,...n},s)=>u.jsx(ai,{ref:s,sideOffset:t,className:Te("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",e),...n}));Ll.displayName=ai.displayName;const pi=m.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:Te("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));pi.displayName="Card";const Il=m.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:Te("flex flex-col space-y-1.5 p-6",e),...t}));Il.displayName="CardHeader";const Ol=m.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:Te("text-2xl font-semibold leading-none tracking-tight",e),...t}));Ol.displayName="CardTitle";const Bl=m.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:Te("text-sm text-muted-foreground",e),...t}));Bl.displayName="CardDescription";const mi=m.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:Te("p-6 pt-0",e),...t}));mi.displayName="CardContent";const Fl=m.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:Te("flex items-center p-6 pt-0",e),...t}));Fl.displayName="CardFooter";/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zl=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),gi=(...e)=>e.filter((t,n,s)=>!!t&&s.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var _l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hl=m.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:r="",children:o,iconNode:i,...l},a)=>m.createElement("svg",{ref:a,..._l,width:t,height:t,stroke:e,strokeWidth:s?Number(n)*24/Number(t):n,className:gi("lucide",r),...l},[...i.map(([c,d])=>m.createElement(c,d)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=(e,t)=>{const n=m.forwardRef(({className:s,...r},o)=>m.createElement(Hl,{ref:o,iconNode:t,className:gi(`lucide-${zl(e)}`,s),...r}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yi=M("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=M("Bike",[["circle",{cx:"18.5",cy:"17.5",r:"3.5",key:"15x4ox"}],["circle",{cx:"5.5",cy:"17.5",r:"3.5",key:"1noe27"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["path",{d:"M12 17.5V14l-3-3 4-3 2 3h2",key:"1npguv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gs=M("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wl=M("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gl=M("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=M("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tn=M("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kl=M("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ys=M("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ql=M("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lt=M("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yl=M("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xl=M("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zl=M("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jl=M("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ql=M("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ec=M("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tc=M("Sandwich",[["path",{d:"m2.37 11.223 8.372-6.777a2 2 0 0 1 2.516 0l8.371 6.777",key:"f1wd0e"}],["path",{d:"M21 15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5.25",key:"1pfu07"}],["path",{d:"M3 15a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h9",key:"1oq9qw"}],["path",{d:"m6.67 15 6.13 4.6a2 2 0 0 0 2.8-.4l3.15-4.2",key:"1fnwu5"}],["rect",{width:"20",height:"4",x:"2",y:"11",rx:"1",key:"itshg"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nc=M("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zt=M("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sc=M("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rc=M("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xi=M("Utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ic=M("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function oc(){return u.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:u.jsx(pi,{className:"w-full max-w-md mx-4",children:u.jsxs(mi,{className:"pt-6",children:[u.jsxs("div",{className:"flex mb-4 gap-2",children:[u.jsx($l,{className:"h-8 w-8 text-red-500"}),u.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),u.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}function ac(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...s)=>e(...s);return new Proxy(n,{get:(s,r)=>r==="create"?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}function St(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Jt=e=>Array.isArray(e);function bi(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let s=0;s<n;s++)if(t[s]!==e[s])return!1;return!0}function Xe(e){return typeof e=="string"||Array.isArray(e)}function xs(e){const t=[{},{}];return e==null||e.values.forEach((n,s)=>{t[0][s]=n.get(),t[1][s]=n.getVelocity()}),t}function Pn(e,t,n,s){if(typeof t=="function"){const[r,o]=xs(s);t=t(n!==void 0?n:e.custom,r,o)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[r,o]=xs(s);t=t(n!==void 0?n:e.custom,r,o)}return t}function jt(e,t,n){const s=e.getProps();return Pn(s,t,n!==void 0?n:s.custom,e)}const Sn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],jn=["initial",...Sn],Qe=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Pe=new Set(Qe),re=e=>e*1e3,ie=e=>e/1e3,lc={type:"spring",stiffness:500,damping:25,restSpeed:10},cc=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),uc={type:"keyframes",duration:.8},dc={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},hc=(e,{keyframes:t})=>t.length>2?uc:Pe.has(e)?e.startsWith("scale")?cc(t[1]):lc:dc;function Cn(e,t){return e?e[t]||e.default||e:void 0}const fc={skipAnimations:!1,useManualTiming:!1},pc=e=>e!==null;function Ct(e,{repeat:t,repeatType:n="loop"},s){const r=e.filter(pc),o=t&&n!=="loop"&&t%2===1?0:r.length-1;return!o||s===void 0?r[o]:s}const z=e=>e;let mc=z,Qt=z;function gc(e){let t=new Set,n=new Set,s=!1,r=!1;const o=new WeakSet;let i={delta:0,timestamp:0,isProcessing:!1};function l(c){o.has(c)&&(a.schedule(c),e()),c(i)}const a={schedule:(c,d=!1,h=!1)=>{const p=h&&s?t:n;return d&&o.add(c),p.has(c)||p.add(c),c},cancel:c=>{n.delete(c),o.delete(c)},process:c=>{if(i=c,s){r=!0;return}s=!0,[t,n]=[n,t],n.clear(),t.forEach(l),s=!1,r&&(r=!1,a.process(c))}};return a}const it=["read","resolveKeyframes","update","preRender","render","postRender"],yc=40;function vi(e,t){let n=!1,s=!0;const r={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,i=it.reduce((b,w)=>(b[w]=gc(o),b),{}),{read:l,resolveKeyframes:a,update:c,preRender:d,render:h,postRender:f}=i,p=()=>{const b=performance.now();n=!1,r.delta=s?1e3/60:Math.max(Math.min(b-r.timestamp,yc),1),r.timestamp=b,r.isProcessing=!0,l.process(r),a.process(r),c.process(r),d.process(r),h.process(r),f.process(r),r.isProcessing=!1,n&&t&&(s=!1,e(p))},x=()=>{n=!0,s=!0,r.isProcessing||e(p)};return{schedule:it.reduce((b,w)=>{const T=i[w];return b[w]=(A,P=!1,N=!1)=>(n||x(),T.schedule(A,P,N)),b},{}),cancel:b=>{for(let w=0;w<it.length;w++)i[it[w]].cancel(b)},state:r,steps:i}}const{schedule:k,cancel:Y,state:F,steps:Rt}=vi(typeof requestAnimationFrame<"u"?requestAnimationFrame:z,!0),wi=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,xc=1e-7,bc=12;function vc(e,t,n,s,r){let o,i,l=0;do i=t+(n-t)/2,o=wi(i,s,r)-e,o>0?n=i:t=i;while(Math.abs(o)>xc&&++l<bc);return i}function et(e,t,n,s){if(e===t&&n===s)return z;const r=o=>vc(o,0,1,e,n);return o=>o===0||o===1?o:wi(r(o),t,s)}const Ti=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Pi=e=>t=>1-e(1-t),Si=et(.33,1.53,.69,.99),An=Pi(Si),ji=Ti(An),Ci=e=>(e*=2)<1?.5*An(e):.5*(2-Math.pow(2,-10*(e-1))),kn=e=>1-Math.sin(Math.acos(e)),Ai=Pi(kn),ki=Ti(kn),Ni=e=>/^0[^.\s]+$/u.test(e);function wc(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Ni(e):!0}const Vi=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Ei=e=>t=>typeof t=="string"&&t.startsWith(e),Mi=Ei("--"),Tc=Ei("var(--"),Nn=e=>Tc(e)?Pc.test(e.split("/*")[0].trim()):!1,Pc=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Sc=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function jc(e){const t=Sc.exec(e);if(!t)return[,];const[,n,s,r]=t;return[`--${n??s}`,r]}function Ri(e,t,n=1){const[s,r]=jc(e);if(!s)return;const o=window.getComputedStyle(t).getPropertyValue(s);if(o){const i=o.trim();return Vi(i)?parseFloat(i):i}return Nn(r)?Ri(r,t,n+1):r}const oe=(e,t,n)=>n>t?t:n<e?e:n,Oe={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Ze={...Oe,transform:e=>oe(0,1,e)},ot={...Oe,default:1},tt=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),ce=tt("deg"),J=tt("%"),S=tt("px"),Cc=tt("vh"),Ac=tt("vw"),bs={...J,parse:e=>J.parse(e)/100,transform:e=>J.transform(e*100)},kc=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),vs=e=>e===Oe||e===S,ws=(e,t)=>parseFloat(e.split(", ")[t]),Ts=(e,t)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const r=s.match(/^matrix3d\((.+)\)$/u);if(r)return ws(r[1],t);{const o=s.match(/^matrix\((.+)\)$/u);return o?ws(o[1],e):0}},Nc=new Set(["x","y","z"]),Vc=Qe.filter(e=>!Nc.has(e));function Ec(e){const t=[];return Vc.forEach(n=>{const s=e.getValue(n);s!==void 0&&(t.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),t}const De={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Ts(4,13),y:Ts(5,14)};De.translateX=De.x;De.translateY=De.y;const Di=e=>t=>t.test(e),Mc={test:e=>e==="auto",parse:e=>e},Li=[Oe,S,J,ce,Ac,Cc,Mc],Ps=e=>Li.find(Di(e)),ve=new Set;let en=!1,tn=!1;function Ii(){if(tn){const e=Array.from(ve).filter(s=>s.needsMeasurement),t=new Set(e.map(s=>s.element)),n=new Map;t.forEach(s=>{const r=Ec(s);r.length&&(n.set(s,r),s.render())}),e.forEach(s=>s.measureInitialState()),t.forEach(s=>{s.render();const r=n.get(s);r&&r.forEach(([o,i])=>{var l;(l=s.getValue(o))===null||l===void 0||l.set(i)})}),e.forEach(s=>s.measureEndState()),e.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}tn=!1,en=!1,ve.forEach(e=>e.complete()),ve.clear()}function Oi(){ve.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tn=!0)})}function Rc(){Oi(),Ii()}class Vn{constructor(t,n,s,r,o,i=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=s,this.motionValue=r,this.element=o,this.isAsync=i}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ve.add(this),en||(en=!0,k.read(Oi),k.resolveKeyframes(Ii))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:s,motionValue:r}=this;for(let o=0;o<t.length;o++)if(t[o]===null)if(o===0){const i=r==null?void 0:r.get(),l=t[t.length-1];if(i!==void 0)t[0]=i;else if(s&&n){const a=s.readValue(n,l);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=l),r&&i===void 0&&r.set(t[0])}else t[o]=t[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ve.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ve.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Ge=e=>Math.round(e*1e5)/1e5,En=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Dc(e){return e==null}const Lc=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Mn=(e,t)=>n=>!!(typeof n=="string"&&Lc.test(n)&&n.startsWith(e)||t&&!Dc(n)&&Object.prototype.hasOwnProperty.call(n,t)),Bi=(e,t,n)=>s=>{if(typeof s!="string")return s;const[r,o,i,l]=s.match(En);return{[e]:parseFloat(r),[t]:parseFloat(o),[n]:parseFloat(i),alpha:l!==void 0?parseFloat(l):1}},Ic=e=>oe(0,255,e),Dt={...Oe,transform:e=>Math.round(Ic(e))},be={test:Mn("rgb","red"),parse:Bi("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:s=1})=>"rgba("+Dt.transform(e)+", "+Dt.transform(t)+", "+Dt.transform(n)+", "+Ge(Ze.transform(s))+")"};function Oc(e){let t="",n="",s="",r="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),s=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),s=e.substring(3,4),r=e.substring(4,5),t+=t,n+=n,s+=s,r+=r),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}}const nn={test:Mn("#"),parse:Oc,transform:be.transform},Ce={test:Mn("hsl","hue"),parse:Bi("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:s=1})=>"hsla("+Math.round(e)+", "+J.transform(Ge(t))+", "+J.transform(Ge(n))+", "+Ge(Ze.transform(s))+")"},H={test:e=>be.test(e)||nn.test(e)||Ce.test(e),parse:e=>be.test(e)?be.parse(e):Ce.test(e)?Ce.parse(e):nn.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?be.transform(e):Ce.transform(e)},Bc=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Fc(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(En))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Bc))===null||n===void 0?void 0:n.length)||0)>0}const Fi="number",zi="color",zc="var",_c="var(",Ss="${}",Hc=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Je(e){const t=e.toString(),n=[],s={color:[],number:[],var:[]},r=[];let o=0;const l=t.replace(Hc,a=>(H.test(a)?(s.color.push(o),r.push(zi),n.push(H.parse(a))):a.startsWith(_c)?(s.var.push(o),r.push(zc),n.push(a)):(s.number.push(o),r.push(Fi),n.push(parseFloat(a))),++o,Ss)).split(Ss);return{values:n,split:l,indexes:s,types:r}}function _i(e){return Je(e).values}function Hi(e){const{split:t,types:n}=Je(e),s=t.length;return r=>{let o="";for(let i=0;i<s;i++)if(o+=t[i],r[i]!==void 0){const l=n[i];l===Fi?o+=Ge(r[i]):l===zi?o+=H.transform(r[i]):o+=r[i]}return o}}const Uc=e=>typeof e=="number"?0:e;function Wc(e){const t=_i(e);return Hi(e)(t.map(Uc))}const fe={test:Fc,parse:_i,createTransformer:Hi,getAnimatableNone:Wc},Gc=new Set(["brightness","contrast","saturate","opacity"]);function $c(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[s]=n.match(En)||[];if(!s)return e;const r=n.replace(s,"");let o=Gc.has(t)?1:0;return s!==n&&(o*=100),t+"("+o+r+")"}const Kc=/\b([a-z-]*)\(.*?\)/gu,sn={...fe,getAnimatableNone:e=>{const t=e.match(Kc);return t?t.map($c).join(" "):e}},qc={borderWidth:S,borderTopWidth:S,borderRightWidth:S,borderBottomWidth:S,borderLeftWidth:S,borderRadius:S,radius:S,borderTopLeftRadius:S,borderTopRightRadius:S,borderBottomRightRadius:S,borderBottomLeftRadius:S,width:S,maxWidth:S,height:S,maxHeight:S,top:S,right:S,bottom:S,left:S,padding:S,paddingTop:S,paddingRight:S,paddingBottom:S,paddingLeft:S,margin:S,marginTop:S,marginRight:S,marginBottom:S,marginLeft:S,backgroundPositionX:S,backgroundPositionY:S},Yc={rotate:ce,rotateX:ce,rotateY:ce,rotateZ:ce,scale:ot,scaleX:ot,scaleY:ot,scaleZ:ot,skew:ce,skewX:ce,skewY:ce,distance:S,translateX:S,translateY:S,translateZ:S,x:S,y:S,z:S,perspective:S,transformPerspective:S,opacity:Ze,originX:bs,originY:bs,originZ:S},js={...Oe,transform:Math.round},Rn={...qc,...Yc,zIndex:js,size:S,fillOpacity:Ze,strokeOpacity:Ze,numOctaves:js},Xc={...Rn,color:H,backgroundColor:H,outlineColor:H,fill:H,stroke:H,borderColor:H,borderTopColor:H,borderRightColor:H,borderBottomColor:H,borderLeftColor:H,filter:sn,WebkitFilter:sn},Dn=e=>Xc[e];function Ui(e,t){let n=Dn(e);return n!==sn&&(n=fe),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Zc=new Set(["auto","none","0"]);function Jc(e,t,n){let s=0,r;for(;s<e.length&&!r;){const o=e[s];typeof o=="string"&&!Zc.has(o)&&Je(o).values.length&&(r=e[s]),s++}if(r&&n)for(const o of t)e[o]=Ui(n,r)}class Wi extends Vn{constructor(t,n,s,r,o){super(t,n,s,r,o,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="string"&&(c=c.trim(),Nn(c))){const d=Ri(c,n.current);d!==void 0&&(t[a]=d),a===t.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!kc.has(s)||t.length!==2)return;const[r,o]=t,i=Ps(r),l=Ps(o);if(i!==l)if(vs(i)&&vs(l))for(let a=0;a<t.length;a++){const c=t[a];typeof c=="string"&&(t[a]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,s=[];for(let r=0;r<t.length;r++)wc(t[r])&&s.push(r);s.length&&Jc(t,s,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:s}=this;if(!t||!t.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=De[s](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const r=n[n.length-1];r!==void 0&&t.getValue(s,r).jump(r,!1)}measureEndState(){var t;const{element:n,name:s,unresolvedKeyframes:r}=this;if(!n||!n.current)return;const o=n.getValue(s);o&&o.jump(this.measuredOrigin,!1);const i=r.length-1,l=r[i];r[i]=De[s](n.measureViewportBox(),window.getComputedStyle(n.current)),l!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=l),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([a,c])=>{n.getValue(a).set(c)}),this.resolveNoneKeyframes()}}function Ln(e){return typeof e=="function"}let ct;function Qc(){ct=void 0}const Q={now:()=>(ct===void 0&&Q.set(F.isProcessing||fc.useManualTiming?F.timestamp:performance.now()),ct),set:e=>{ct=e,queueMicrotask(Qc)}},Cs=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(fe.test(e)||e==="0")&&!e.startsWith("url("));function eu(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function tu(e,t,n,s){const r=e[0];if(r===null)return!1;if(t==="display"||t==="visibility")return!0;const o=e[e.length-1],i=Cs(r,t),l=Cs(o,t);return!i||!l?!1:eu(e)||(n==="spring"||Ln(n))&&s}const nu=40;class Gi{constructor({autoplay:t=!0,delay:n=0,type:s="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:i="loop",...l}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Q.now(),this.options={autoplay:t,delay:n,type:s,repeat:r,repeatDelay:o,repeatType:i,...l},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>nu?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Rc(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=Q.now(),this.hasAttemptedResolve=!0;const{name:s,type:r,velocity:o,delay:i,onComplete:l,onUpdate:a,isGenerator:c}=this.options;if(!c&&!tu(t,s,r,o))if(i)this.options.duration=0;else{a==null||a(Ct(t,this.options,n)),l==null||l(),this.resolveFinishedPromise();return}const d=this.initPlayback(t,n);d!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...d},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const we=(e,t,n)=>{const s=t-e;return s===0?1:(n-e)/s},$i=(e,t,n=10)=>{let s="";const r=Math.max(Math.round(t/n),2);for(let o=0;o<r;o++)s+=e(we(0,r-1,o))+", ";return`linear(${s.substring(0,s.length-2)})`};function In(e,t){return t?e*(1e3/t):0}const su=5;function Ki(e,t,n){const s=Math.max(t-su,0);return In(n-e(s),t-s)}const D={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Lt=.001;function ru({duration:e=D.duration,bounce:t=D.bounce,velocity:n=D.velocity,mass:s=D.mass}){let r,o,i=1-t;i=oe(D.minDamping,D.maxDamping,i),e=oe(D.minDuration,D.maxDuration,ie(e)),i<1?(r=c=>{const d=c*i,h=d*e,f=d-n,p=rn(c,i),x=Math.exp(-h);return Lt-f/p*x},o=c=>{const h=c*i*e,f=h*n+n,p=Math.pow(i,2)*Math.pow(c,2)*e,x=Math.exp(-h),g=rn(Math.pow(c,2),i);return(-r(c)+Lt>0?-1:1)*((f-p)*x)/g}):(r=c=>{const d=Math.exp(-c*e),h=(c-n)*e+1;return-Lt+d*h},o=c=>{const d=Math.exp(-c*e),h=(n-c)*(e*e);return d*h});const l=5/e,a=ou(r,o,l);if(e=re(e),isNaN(a))return{stiffness:D.stiffness,damping:D.damping,duration:e};{const c=Math.pow(a,2)*s;return{stiffness:c,damping:i*2*Math.sqrt(s*c),duration:e}}}const iu=12;function ou(e,t,n){let s=n;for(let r=1;r<iu;r++)s=s-e(s)/t(s);return s}function rn(e,t){return e*Math.sqrt(1-t*t)}const on=2e4;function qi(e){let t=0;const n=50;let s=e.next(t);for(;!s.done&&t<on;)t+=n,s=e.next(t);return t>=on?1/0:t}const au=["duration","bounce"],lu=["stiffness","damping","mass"];function As(e,t){return t.some(n=>e[n]!==void 0)}function cu(e){let t={velocity:D.velocity,stiffness:D.stiffness,damping:D.damping,mass:D.mass,isResolvedFromDuration:!1,...e};if(!As(e,lu)&&As(e,au))if(e.visualDuration){const n=e.visualDuration,s=2*Math.PI/(n*1.2),r=s*s,o=2*oe(.05,1,1-e.bounce)*Math.sqrt(r);t={...t,mass:D.mass,stiffness:r,damping:o}}else{const n=ru(e);t={...t,...n,mass:D.mass},t.isResolvedFromDuration=!0}return t}function Yi(e=D.visualDuration,t=D.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:s,restDelta:r}=n;const o=n.keyframes[0],i=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:a,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=cu({...n,velocity:-ie(n.velocity||0)}),x=f||0,g=c/(2*Math.sqrt(a*d)),y=i-o,b=ie(Math.sqrt(a/d)),w=Math.abs(y)<5;s||(s=w?D.restSpeed.granular:D.restSpeed.default),r||(r=w?D.restDelta.granular:D.restDelta.default);let T;if(g<1){const P=rn(b,g);T=N=>{const I=Math.exp(-g*b*N);return i-I*((x+g*b*y)/P*Math.sin(P*N)+y*Math.cos(P*N))}}else if(g===1)T=P=>i-Math.exp(-b*P)*(y+(x+b*y)*P);else{const P=b*Math.sqrt(g*g-1);T=N=>{const I=Math.exp(-g*b*N),C=Math.min(P*N,300);return i-I*((x+g*b*y)*Math.sinh(C)+P*y*Math.cosh(C))/P}}const A={calculatedDuration:p&&h||null,next:P=>{const N=T(P);if(p)l.done=P>=h;else{let I=0;g<1&&(I=P===0?re(x):Ki(T,P,N));const C=Math.abs(I)<=s,O=Math.abs(i-N)<=r;l.done=C&&O}return l.value=l.done?i:N,l},toString:()=>{const P=Math.min(qi(A),on),N=$i(I=>A.next(P*I).value,P,30);return P+"ms "+N}};return A}function ks({keyframes:e,velocity:t=0,power:n=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:o=500,modifyTarget:i,min:l,max:a,restDelta:c=.5,restSpeed:d}){const h=e[0],f={done:!1,value:h},p=C=>l!==void 0&&C<l||a!==void 0&&C>a,x=C=>l===void 0?a:a===void 0||Math.abs(l-C)<Math.abs(a-C)?l:a;let g=n*t;const y=h+g,b=i===void 0?y:i(y);b!==y&&(g=b-h);const w=C=>-g*Math.exp(-C/s),T=C=>b+w(C),A=C=>{const O=w(C),_=T(C);f.done=Math.abs(O)<=c,f.value=f.done?b:_};let P,N;const I=C=>{p(f.value)&&(P=C,N=Yi({keyframes:[f.value,x(f.value)],velocity:Ki(T,C,f.value),damping:r,stiffness:o,restDelta:c,restSpeed:d}))};return I(0),{calculatedDuration:null,next:C=>{let O=!1;return!N&&P===void 0&&(O=!0,A(C),I(C)),P!==void 0&&C>=P?N.next(C-P):(!O&&A(C),f)}}}const uu=et(.42,0,1,1),du=et(0,0,.58,1),Xi=et(.42,0,.58,1),hu=e=>Array.isArray(e)&&typeof e[0]!="number",On=e=>Array.isArray(e)&&typeof e[0]=="number",Ns={linear:z,easeIn:uu,easeInOut:Xi,easeOut:du,circIn:kn,circInOut:ki,circOut:Ai,backIn:An,backInOut:ji,backOut:Si,anticipate:Ci},Vs=e=>{if(On(e)){Qt(e.length===4);const[t,n,s,r]=e;return et(t,n,s,r)}else if(typeof e=="string")return Qt(Ns[e]!==void 0),Ns[e];return e},fu=(e,t)=>n=>t(e(n)),de=(...e)=>e.reduce(fu),R=(e,t,n)=>e+(t-e)*n;function It(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function pu({hue:e,saturation:t,lightness:n,alpha:s}){e/=360,t/=100,n/=100;let r=0,o=0,i=0;if(!t)r=o=i=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;r=It(a,l,e+1/3),o=It(a,l,e),i=It(a,l,e-1/3)}return{red:Math.round(r*255),green:Math.round(o*255),blue:Math.round(i*255),alpha:s}}function pt(e,t){return n=>n>0?t:e}const Ot=(e,t,n)=>{const s=e*e,r=n*(t*t-s)+s;return r<0?0:Math.sqrt(r)},mu=[nn,be,Ce],gu=e=>mu.find(t=>t.test(e));function Es(e){const t=gu(e);if(!t)return!1;let n=t.parse(e);return t===Ce&&(n=pu(n)),n}const Ms=(e,t)=>{const n=Es(e),s=Es(t);if(!n||!s)return pt(e,t);const r={...n};return o=>(r.red=Ot(n.red,s.red,o),r.green=Ot(n.green,s.green,o),r.blue=Ot(n.blue,s.blue,o),r.alpha=R(n.alpha,s.alpha,o),be.transform(r))},an=new Set(["none","hidden"]);function yu(e,t){return an.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function xu(e,t){return n=>R(e,t,n)}function Bn(e){return typeof e=="number"?xu:typeof e=="string"?Nn(e)?pt:H.test(e)?Ms:wu:Array.isArray(e)?Zi:typeof e=="object"?H.test(e)?Ms:bu:pt}function Zi(e,t){const n=[...e],s=n.length,r=e.map((o,i)=>Bn(o)(o,t[i]));return o=>{for(let i=0;i<s;i++)n[i]=r[i](o);return n}}function bu(e,t){const n={...e,...t},s={};for(const r in n)e[r]!==void 0&&t[r]!==void 0&&(s[r]=Bn(e[r])(e[r],t[r]));return r=>{for(const o in s)n[o]=s[o](r);return n}}function vu(e,t){var n;const s=[],r={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){const i=t.types[o],l=e.indexes[i][r[i]],a=(n=e.values[l])!==null&&n!==void 0?n:0;s[o]=a,r[i]++}return s}const wu=(e,t)=>{const n=fe.createTransformer(t),s=Je(e),r=Je(t);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?an.has(e)&&!r.values.length||an.has(t)&&!s.values.length?yu(e,t):de(Zi(vu(s,r),r.values),n):pt(e,t)};function Ji(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?R(e,t,n):Bn(e)(e,t)}function Tu(e,t,n){const s=[],r=n||Ji,o=e.length-1;for(let i=0;i<o;i++){let l=r(e[i],e[i+1]);if(t){const a=Array.isArray(t)?t[i]||z:t;l=de(a,l)}s.push(l)}return s}function Fn(e,t,{clamp:n=!0,ease:s,mixer:r}={}){const o=e.length;if(Qt(o===t.length),o===1)return()=>t[0];if(o===2&&e[0]===e[1])return()=>t[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const i=Tu(t,s,r),l=i.length,a=c=>{let d=0;if(l>1)for(;d<e.length-2&&!(c<e[d+1]);d++);const h=we(e[d],e[d+1],c);return i[d](h)};return n?c=>a(oe(e[0],e[o-1],c)):a}function Pu(e,t){const n=e[e.length-1];for(let s=1;s<=t;s++){const r=we(0,t,s);e.push(R(n,1,r))}}function Qi(e){const t=[0];return Pu(t,e.length-1),t}function Su(e,t){return e.map(n=>n*t)}function ju(e,t){return e.map(()=>t||Xi).splice(0,e.length-1)}function mt({duration:e=300,keyframes:t,times:n,ease:s="easeInOut"}){const r=hu(s)?s.map(Vs):Vs(s),o={done:!1,value:t[0]},i=Su(n&&n.length===t.length?n:Qi(t),e),l=Fn(i,t,{ease:Array.isArray(r)?r:ju(t,r)});return{calculatedDuration:e,next:a=>(o.value=l(a),o.done=a>=e,o)}}const Cu=e=>{const t=({timestamp:n})=>e(n);return{start:()=>k.update(t,!0),stop:()=>Y(t),now:()=>F.isProcessing?F.timestamp:Q.now()}},Au={decay:ks,inertia:ks,tween:mt,keyframes:mt,spring:Yi},ku=e=>e/100;class zn extends Gi{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:a}=this.options;a&&a()};const{name:n,motionValue:s,element:r,keyframes:o}=this.options,i=(r==null?void 0:r.KeyframeResolver)||Vn,l=(a,c)=>this.onKeyframesResolved(a,c);this.resolver=new i(o,l,n,s,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:o,velocity:i=0}=this.options,l=Ln(n)?n:Au[n]||mt;let a,c;l!==mt&&typeof t[0]!="number"&&(a=de(ku,Ji(t[0],t[1])),t=[0,100]);const d=l({...this.options,keyframes:t});o==="mirror"&&(c=l({...this.options,keyframes:[...t].reverse(),velocity:-i})),d.calculatedDuration===null&&(d.calculatedDuration=qi(d));const{calculatedDuration:h}=d,f=h+r,p=f*(s+1)-r;return{generator:d,mirroredGenerator:c,mapPercentToKeyframes:a,calculatedDuration:h,resolvedDuration:f,totalDuration:p}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:s}=this;if(!s){const{keyframes:C}=this.options;return{done:!0,value:C[C.length-1]}}const{finalKeyframe:r,generator:o,mirroredGenerator:i,mapPercentToKeyframes:l,keyframes:a,calculatedDuration:c,totalDuration:d,resolvedDuration:h}=s;if(this.startTime===null)return o.next(0);const{delay:f,repeat:p,repeatType:x,repeatDelay:g,onUpdate:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-d/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const b=this.currentTime-f*(this.speed>=0?1:-1),w=this.speed>=0?b<0:b>d;this.currentTime=Math.max(b,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=d);let T=this.currentTime,A=o;if(p){const C=Math.min(this.currentTime,d)/h;let O=Math.floor(C),_=C%1;!_&&C>=1&&(_=1),_===1&&O--,O=Math.min(O,p+1),!!(O%2)&&(x==="reverse"?(_=1-_,g&&(_-=g/h)):x==="mirror"&&(A=i)),T=oe(0,1,_)*h}const P=w?{done:!1,value:a[0]}:A.next(T);l&&(P.value=l(P.value));let{done:N}=P;!w&&c!==null&&(N=this.speed>=0?this.currentTime>=d:this.currentTime<=0);const I=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&N);return I&&r!==void 0&&(P.value=Ct(a,this.options,r)),y&&y(P.value),I&&this.finish(),P}get duration(){const{resolved:t}=this;return t?ie(t.calculatedDuration):0}get time(){return ie(this.currentTime)}set time(t){t=re(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=ie(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=Cu,onPlay:n,startTime:s}=this.options;this.driver||(this.driver=t(o=>this.tick(o))),n&&n();const r=this.driver.now();this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=r):this.startTime=s??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const Nu=new Set(["opacity","clipPath","filter","transform"]);function _n(e){let t;return()=>(t===void 0&&(t=e()),t)}const Vu={linearEasing:void 0};function Eu(e,t){const n=_n(e);return()=>{var s;return(s=Vu[t])!==null&&s!==void 0?s:n()}}const gt=Eu(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing");function eo(e){return!!(typeof e=="function"&&gt()||!e||typeof e=="string"&&(e in ln||gt())||On(e)||Array.isArray(e)&&e.every(eo))}const Ue=([e,t,n,s])=>`cubic-bezier(${e}, ${t}, ${n}, ${s})`,ln={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ue([0,.65,.55,1]),circOut:Ue([.55,0,1,.45]),backIn:Ue([.31,.01,.66,-.59]),backOut:Ue([.33,1.53,.69,.99])};function to(e,t){if(e)return typeof e=="function"&&gt()?$i(e,t):On(e)?Ue(e):Array.isArray(e)?e.map(n=>to(n,t)||ln.easeOut):ln[e]}function Mu(e,t,n,{delay:s=0,duration:r=300,repeat:o=0,repeatType:i="loop",ease:l="easeInOut",times:a}={}){const c={[t]:n};a&&(c.offset=a);const d=to(l,r);return Array.isArray(d)&&(c.easing=d),e.animate(c,{delay:s,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:i==="reverse"?"alternate":"normal"})}function Rs(e,t){e.timeline=t,e.onfinish=null}const Ru=_n(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),yt=10,Du=2e4;function Lu(e){return Ln(e.type)||e.type==="spring"||!eo(e.ease)}function Iu(e,t){const n=new zn({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let s={done:!1,value:e[0]};const r=[];let o=0;for(;!s.done&&o<Du;)s=n.sample(o),r.push(s.value),o+=yt;return{times:void 0,keyframes:r,duration:o-yt,ease:"linear"}}const no={anticipate:Ci,backInOut:ji,circInOut:ki};function Ou(e){return e in no}class Ds extends Gi{constructor(t){super(t);const{name:n,motionValue:s,element:r,keyframes:o}=this.options;this.resolver=new Wi(o,(i,l)=>this.onKeyframesResolved(i,l),n,s,r),this.resolver.scheduleResolve()}initPlayback(t,n){var s;let{duration:r=300,times:o,ease:i,type:l,motionValue:a,name:c,startTime:d}=this.options;if(!(!((s=a.owner)===null||s===void 0)&&s.current))return!1;if(typeof i=="string"&&gt()&&Ou(i)&&(i=no[i]),Lu(this.options)){const{onComplete:f,onUpdate:p,motionValue:x,element:g,...y}=this.options,b=Iu(t,y);t=b.keyframes,t.length===1&&(t[1]=t[0]),r=b.duration,o=b.times,i=b.ease,l="keyframes"}const h=Mu(a.owner.current,c,t,{...this.options,duration:r,times:o,ease:i});return h.startTime=d??this.calcStartTime(),this.pendingTimeline?(Rs(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{const{onComplete:f}=this.options;a.set(Ct(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:r,times:o,type:l,ease:i,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return ie(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return ie(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.currentTime=re(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return z;const{animation:s}=n;Rs(s,t)}return z}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:s,duration:r,type:o,ease:i,times:l}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:c,onUpdate:d,onComplete:h,element:f,...p}=this.options,x=new zn({...p,keyframes:s,duration:r,type:o,ease:i,times:l,isGenerator:!0}),g=re(this.time);c.setWithVelocity(x.sample(g-yt).value,x.sample(g).value,yt)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:s,repeatDelay:r,repeatType:o,damping:i,type:l}=t;return Ru()&&s&&Nu.has(s)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!r&&o!=="mirror"&&i!==0&&l!=="inertia"}}const so=_n(()=>window.ScrollTimeline!==void 0);class Bu{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}then(t,n){return Promise.all(this.animations).then(t).catch(n)}getAll(t){return this.animations[0][t]}setAll(t,n){for(let s=0;s<this.animations.length;s++)this.animations[s][t]=n}attachTimeline(t,n){const s=this.animations.map(r=>so()&&r.attachTimeline?r.attachTimeline(t):n(r));return()=>{s.forEach((r,o)=>{r&&r(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function Fu({when:e,delay:t,delayChildren:n,staggerChildren:s,staggerDirection:r,repeat:o,repeatType:i,repeatDelay:l,from:a,elapsed:c,...d}){return!!Object.keys(d).length}const Hn=(e,t,n,s={},r,o)=>i=>{const l=Cn(s,e)||{},a=l.delay||s.delay||0;let{elapsed:c=0}=s;c=c-re(a);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-c,onUpdate:f=>{t.set(f),l.onUpdate&&l.onUpdate(f)},onComplete:()=>{i(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:o?void 0:r};Fu(l)||(d={...d,...hc(e,d)}),d.duration&&(d.duration=re(d.duration)),d.repeatDelay&&(d.repeatDelay=re(d.repeatDelay)),d.from!==void 0&&(d.keyframes[0]=d.from);let h=!1;if((d.type===!1||d.duration===0&&!d.repeatDelay)&&(d.duration=0,d.delay===0&&(h=!0)),h&&!o&&t.get()!==void 0){const f=Ct(d.keyframes,l);if(f!==void 0)return k.update(()=>{d.onUpdate(f),d.onComplete()}),new Bu([])}return!o&&Ds.supports(d)?new Ds(d):new zn(d)},zu=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),_u=e=>Jt(e)?e[e.length-1]||0:e;function Un(e,t){e.indexOf(t)===-1&&e.push(t)}function Wn(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Gn{constructor(){this.subscriptions=[]}add(t){return Un(this.subscriptions,t),()=>Wn(this.subscriptions,t)}notify(t,n,s){const r=this.subscriptions.length;if(r)if(r===1)this.subscriptions[0](t,n,s);else for(let o=0;o<r;o++){const i=this.subscriptions[o];i&&i(t,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ls=30,Hu=e=>!isNaN(parseFloat(e)),$e={current:void 0};class Uu{constructor(t,n={}){this.version="11.13.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,r=!0)=>{const o=Q.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),r&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=Q.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=Hu(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Gn);const s=this.events[t].add(n);return t==="change"?()=>{s(),k.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-s}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return $e.current&&$e.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=Q.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Ls)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Ls);return In(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Z(e,t){return new Uu(e,t)}function Wu(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Z(n))}function Gu(e,t){const n=jt(e,t);let{transitionEnd:s={},transition:r={},...o}=n||{};o={...o,...s};for(const i in o){const l=_u(o[i]);Wu(e,i,l)}}const $n=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),$u="framerAppearId",ro="data-"+$n($u);function io(e){return e.props[ro]}const U=e=>!!(e&&e.getVelocity);function Ku(e){return!!(U(e)&&e.add)}function cn(e,t){const n=e.getValue("willChange");if(Ku(n))return n.add(t)}function qu({protectedKeys:e,needsAnimating:t},n){const s=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,s}function oo(e,t,{delay:n=0,transitionOverride:s,type:r}={}){var o;let{transition:i=e.getDefaultTransition(),transitionEnd:l,...a}=t;s&&(i=s);const c=[],d=r&&e.animationState&&e.animationState.getState()[r];for(const h in a){const f=e.getValue(h,(o=e.latestValues[h])!==null&&o!==void 0?o:null),p=a[h];if(p===void 0||d&&qu(d,h))continue;const x={delay:n,...Cn(i||{},h)};let g=!1;if(window.MotionHandoffAnimation){const b=io(e);if(b){const w=window.MotionHandoffAnimation(b,h,k);w!==null&&(x.startTime=w,g=!0)}}cn(e,h),f.start(Hn(h,f,p,e.shouldReduceMotion&&Pe.has(h)?{type:!1}:x,e,g));const y=f.animation;y&&c.push(y)}return l&&Promise.all(c).then(()=>{k.update(()=>{l&&Gu(e,l)})}),c}function un(e,t,n={}){var s;const r=jt(e,t,n.type==="exit"?(s=e.presenceContext)===null||s===void 0?void 0:s.custom:void 0);let{transition:o=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(o=n.transitionOverride);const i=r?()=>Promise.all(oo(e,r,n)):()=>Promise.resolve(),l=e.variantChildren&&e.variantChildren.size?(c=0)=>{const{delayChildren:d=0,staggerChildren:h,staggerDirection:f}=o;return Yu(e,t,d+c,h,f,n)}:()=>Promise.resolve(),{when:a}=o;if(a){const[c,d]=a==="beforeChildren"?[i,l]:[l,i];return c().then(()=>d())}else return Promise.all([i(),l(n.delay)])}function Yu(e,t,n=0,s=0,r=1,o){const i=[],l=(e.variantChildren.size-1)*s,a=r===1?(c=0)=>c*s:(c=0)=>l-c*s;return Array.from(e.variantChildren).sort(Xu).forEach((c,d)=>{c.notify("AnimationStart",t),i.push(un(c,t,{...o,delay:n+a(d)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(i)}function Xu(e,t){return e.sortNodePosition(t)}function Zu(e,t,n={}){e.notify("AnimationStart",t);let s;if(Array.isArray(t)){const r=t.map(o=>un(e,o,n));s=Promise.all(r)}else if(typeof t=="string")s=un(e,t,n);else{const r=typeof t=="function"?jt(e,t,n.custom):t;s=Promise.all(oo(e,r,n))}return s.then(()=>{e.notify("AnimationComplete",t)})}const Ju=jn.length;function ao(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?ao(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<Ju;n++){const s=jn[n],r=e.props[s];(Xe(r)||r===!1)&&(t[s]=r)}return t}const Qu=[...Sn].reverse(),ed=Sn.length;function td(e){return t=>Promise.all(t.map(({animation:n,options:s})=>Zu(e,n,s)))}function nd(e){let t=td(e),n=Is(),s=!0;const r=a=>(c,d)=>{var h;const f=jt(e,d,a==="exit"?(h=e.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:p,transitionEnd:x,...g}=f;c={...c,...g,...x}}return c};function o(a){t=a(e)}function i(a){const{props:c}=e,d=ao(e.parent)||{},h=[],f=new Set;let p={},x=1/0;for(let y=0;y<ed;y++){const b=Qu[y],w=n[b],T=c[b]!==void 0?c[b]:d[b],A=Xe(T),P=b===a?w.isActive:null;P===!1&&(x=y);let N=T===d[b]&&T!==c[b]&&A;if(N&&s&&e.manuallyAnimateOnMount&&(N=!1),w.protectedKeys={...p},!w.isActive&&P===null||!T&&!w.prevProp||St(T)||typeof T=="boolean")continue;const I=sd(w.prevProp,T);let C=I||b===a&&w.isActive&&!N&&A||y>x&&A,O=!1;const _=Array.isArray(T)?T:[T];let X=_.reduce(r(b),{});P===!1&&(X={});const{prevResolvedValues:V={}}=w,rt={...V,...X},me=B=>{C=!0,f.has(B)&&(O=!0,f.delete(B)),w.needsAnimating[B]=!0;const W=e.getValue(B);W&&(W.liveStyle=!1)};for(const B in rt){const W=X[B],ee=V[B];if(p.hasOwnProperty(B))continue;let Se=!1;Jt(W)&&Jt(ee)?Se=!bi(W,ee):Se=W!==ee,Se?W!=null?me(B):f.add(B):W!==void 0&&f.has(B)?me(B):w.protectedKeys[B]=!0}w.prevProp=T,w.prevResolvedValues=X,w.isActive&&(p={...p,...X}),s&&e.blockInitialAnimation&&(C=!1),C&&(!(N&&I)||O)&&h.push(..._.map(B=>({animation:B,options:{type:b}})))}if(f.size){const y={};f.forEach(b=>{const w=e.getBaseTarget(b),T=e.getValue(b);T&&(T.liveStyle=!0),y[b]=w??null}),h.push({animation:y})}let g=!!h.length;return s&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(g=!1),s=!1,g?t(h):Promise.resolve()}function l(a,c){var d;if(n[a].isActive===c)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(f=>{var p;return(p=f.animationState)===null||p===void 0?void 0:p.setActive(a,c)}),n[a].isActive=c;const h=i(a);for(const f in n)n[f].protectedKeys={};return h}return{animateChanges:i,setActive:l,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Is(),s=!0}}}function sd(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!bi(t,e):!1}function ge(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Is(){return{animate:ge(!0),whileInView:ge(),whileHover:ge(),whileTap:ge(),whileDrag:ge(),whileFocus:ge(),exit:ge()}}class pe{constructor(t){this.isMounted=!1,this.node=t}update(){}}class rd extends pe{constructor(t){super(t),t.animationState||(t.animationState=nd(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();St(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let id=0;class od extends pe{constructor(){super(...arguments),this.id=id++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===s)return;const r=this.node.animationState.setActive("exit",!t);n&&!t&&r.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const ad={animation:{Feature:rd},exit:{Feature:od}};function Kn(e,t,n){var s;if(e instanceof Element)return[e];if(typeof e=="string"){let r=document;const o=(s=void 0)!==null&&s!==void 0?s:r.querySelectorAll(e);return o?Array.from(o):[]}return Array.from(e)}const q={x:!1,y:!1};function lo(){return q.x||q.y}function Os(e){return t=>{t.pointerType==="touch"||lo()||e(t)}}function ld(e,t,n={}){const s=new AbortController,r={passive:!0,...n,signal:s.signal},o=Os(i=>{const{target:l}=i,a=t(i);if(!a||!l)return;const c=Os(d=>{a(d),l.removeEventListener("pointerleave",c)});l.addEventListener("pointerleave",c,r)});return Kn(e).forEach(i=>{i.addEventListener("pointerenter",o,r)}),()=>s.abort()}function cd(e){return e==="x"||e==="y"?q[e]?null:(q[e]=!0,()=>{q[e]=!1}):q.x||q.y?null:(q.x=q.y=!0,()=>{q.x=q.y=!1})}const co=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function nt(e){return{point:{x:e.pageX,y:e.pageY}}}const ud=e=>t=>co(t)&&e(t,nt(t));function se(e,t,n,s={passive:!0}){return e.addEventListener(t,n,s),()=>e.removeEventListener(t,n)}function he(e,t,n,s){return se(e,t,ud(n),s)}const Bs=(e,t)=>Math.abs(e-t);function dd(e,t){const n=Bs(e.x,t.x),s=Bs(e.y,t.y);return Math.sqrt(n**2+s**2)}class uo{constructor(t,n,{transformPagePoint:s,contextWindow:r,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=Ft(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,p=dd(h.offset,{x:0,y:0})>=3;if(!f&&!p)return;const{point:x}=h,{timestamp:g}=F;this.history.push({...x,timestamp:g});const{onStart:y,onMove:b}=this.handlers;f||(y&&y(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),b&&b(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=Bt(f,this.transformPagePoint),k.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:p,onSessionEnd:x,resumeAnimation:g}=this.handlers;if(this.dragSnapToOrigin&&g&&g(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const y=Ft(h.type==="pointercancel"?this.lastMoveEventInfo:Bt(f,this.transformPagePoint),this.history);this.startEvent&&p&&p(h,y),x&&x(h,y)},!co(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.contextWindow=r||window;const i=nt(t),l=Bt(i,this.transformPagePoint),{point:a}=l,{timestamp:c}=F;this.history=[{...a,timestamp:c}];const{onSessionStart:d}=n;d&&d(t,Ft(l,this.history)),this.removeListeners=de(he(this.contextWindow,"pointermove",this.handlePointerMove),he(this.contextWindow,"pointerup",this.handlePointerUp),he(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Y(this.updatePoint)}}function Bt(e,t){return t?{point:t(e.point)}:e}function Fs(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ft({point:e},t){return{point:e,delta:Fs(e,ho(t)),offset:Fs(e,hd(t)),velocity:fd(t,.1)}}function hd(e){return e[0]}function ho(e){return e[e.length-1]}function fd(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,s=null;const r=ho(e);for(;n>=0&&(s=e[n],!(r.timestamp-s.timestamp>re(t)));)n--;if(!s)return{x:0,y:0};const o=ie(r.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const i={x:(r.x-s.x)/o,y:(r.y-s.y)/o};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function Ae(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const fo=1e-4,pd=1-fo,md=1+fo,po=.01,gd=0-po,yd=0+po;function G(e){return e.max-e.min}function xd(e,t,n){return Math.abs(e-t)<=n}function zs(e,t,n,s=.5){e.origin=s,e.originPoint=R(t.min,t.max,e.origin),e.scale=G(n)/G(t),e.translate=R(n.min,n.max,e.origin)-e.originPoint,(e.scale>=pd&&e.scale<=md||isNaN(e.scale))&&(e.scale=1),(e.translate>=gd&&e.translate<=yd||isNaN(e.translate))&&(e.translate=0)}function Ke(e,t,n,s){zs(e.x,t.x,n.x,s?s.originX:void 0),zs(e.y,t.y,n.y,s?s.originY:void 0)}function _s(e,t,n){e.min=n.min+t.min,e.max=e.min+G(t)}function bd(e,t,n){_s(e.x,t.x,n.x),_s(e.y,t.y,n.y)}function Hs(e,t,n){e.min=t.min-n.min,e.max=e.min+G(t)}function qe(e,t,n){Hs(e.x,t.x,n.x),Hs(e.y,t.y,n.y)}function vd(e,{min:t,max:n},s){return t!==void 0&&e<t?e=s?R(t,e,s.min):Math.max(e,t):n!==void 0&&e>n&&(e=s?R(n,e,s.max):Math.min(e,n)),e}function Us(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function wd(e,{top:t,left:n,bottom:s,right:r}){return{x:Us(e.x,n,r),y:Us(e.y,t,s)}}function Ws(e,t){let n=t.min-e.min,s=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,s]=[s,n]),{min:n,max:s}}function Td(e,t){return{x:Ws(e.x,t.x),y:Ws(e.y,t.y)}}function Pd(e,t){let n=.5;const s=G(e),r=G(t);return r>s?n=we(t.min,t.max-s,e.min):s>r&&(n=we(e.min,e.max-r,t.min)),oe(0,1,n)}function Sd(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const dn=.35;function jd(e=dn){return e===!1?e=0:e===!0&&(e=dn),{x:Gs(e,"left","right"),y:Gs(e,"top","bottom")}}function Gs(e,t,n){return{min:$s(e,t),max:$s(e,n)}}function $s(e,t){return typeof e=="number"?e:e[t]||0}const Ks=()=>({translate:0,scale:1,origin:0,originPoint:0}),ke=()=>({x:Ks(),y:Ks()}),qs=()=>({min:0,max:0}),L=()=>({x:qs(),y:qs()});function K(e){return[e("x"),e("y")]}function mo({top:e,left:t,right:n,bottom:s}){return{x:{min:t,max:n},y:{min:e,max:s}}}function Cd({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Ad(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),s=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function zt(e){return e===void 0||e===1}function hn({scale:e,scaleX:t,scaleY:n}){return!zt(e)||!zt(t)||!zt(n)}function ye(e){return hn(e)||go(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function go(e){return Ys(e.x)||Ys(e.y)}function Ys(e){return e&&e!=="0%"}function xt(e,t,n){const s=e-n,r=t*s;return n+r}function Xs(e,t,n,s,r){return r!==void 0&&(e=xt(e,r,s)),xt(e,n,s)+t}function fn(e,t=0,n=1,s,r){e.min=Xs(e.min,t,n,s,r),e.max=Xs(e.max,t,n,s,r)}function yo(e,{x:t,y:n}){fn(e.x,t.translate,t.scale,t.originPoint),fn(e.y,n.translate,n.scale,n.originPoint)}const Zs=.999999999999,Js=1.0000000000001;function kd(e,t,n,s=!1){const r=n.length;if(!r)return;t.x=t.y=1;let o,i;for(let l=0;l<r;l++){o=n[l],i=o.projectionDelta;const{visualElement:a}=o.options;a&&a.props.style&&a.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Ve(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,yo(e,i)),s&&ye(o.latestValues)&&Ve(e,o.latestValues))}t.x<Js&&t.x>Zs&&(t.x=1),t.y<Js&&t.y>Zs&&(t.y=1)}function Ne(e,t){e.min=e.min+t,e.max=e.max+t}function Qs(e,t,n,s,r=.5){const o=R(e.min,e.max,r);fn(e,t,n,o,s)}function Ve(e,t){Qs(e.x,t.x,t.scaleX,t.scale,t.originX),Qs(e.y,t.y,t.scaleY,t.scale,t.originY)}function xo(e,t){return mo(Ad(e.getBoundingClientRect(),t))}function Nd(e,t,n){const s=xo(e,n),{scroll:r}=t;return r&&(Ne(s.x,r.offset.x),Ne(s.y,r.offset.y)),s}const bo=({current:e})=>e?e.ownerDocument.defaultView:null,Vd=new WeakMap;class Ed{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=L(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const r=d=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(nt(d).point)},o=(d,h)=>{const{drag:f,dragPropagation:p,onDragStart:x}=this.getProps();if(f&&!p&&(this.openDragLock&&this.openDragLock(),this.openDragLock=cd(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),K(y=>{let b=this.getAxisMotionValue(y).get()||0;if(J.test(b)){const{projection:w}=this.visualElement;if(w&&w.layout){const T=w.layout.layoutBox[y];T&&(b=G(T)*(parseFloat(b)/100))}}this.originPoint[y]=b}),x&&k.postRender(()=>x(d,h)),cn(this.visualElement,"transform");const{animationState:g}=this.visualElement;g&&g.setActive("whileDrag",!0)},i=(d,h)=>{const{dragPropagation:f,dragDirectionLock:p,onDirectionLock:x,onDrag:g}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:y}=h;if(p&&this.currentDirection===null){this.currentDirection=Md(y),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",h.point,y),this.updateAxis("y",h.point,y),this.visualElement.render(),g&&g(d,h)},l=(d,h)=>this.stop(d,h),a=()=>K(d=>{var h;return this.getAnimationState(d)==="paused"&&((h=this.getAxisMotionValue(d).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new uo(t,{onSessionStart:r,onStart:o,onMove:i,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:bo(this.visualElement)})}stop(t,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:r}=n;this.startAnimation(r);const{onDragEnd:o}=this.getProps();o&&k.postRender(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,s){const{drag:r}=this.getProps();if(!s||!at(t,r,this.currentDirection))return;const o=this.getAxisMotionValue(t);let i=this.originPoint[t]+s[t];this.constraints&&this.constraints[t]&&(i=vd(i,this.constraints[t],this.elastic[t])),o.set(i)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:s}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&Ae(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&r?this.constraints=wd(r.layoutBox,n):this.constraints=!1,this.elastic=jd(s),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&K(i=>{this.constraints!==!1&&this.getAxisMotionValue(i)&&(this.constraints[i]=Sd(r.layoutBox[i],this.constraints[i]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Ae(t))return!1;const s=t.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const o=Nd(s,r.root,this.visualElement.getTransformPagePoint());let i=Td(r.layout.layoutBox,o);if(n){const l=n(Cd(i));this.hasMutatedConstraints=!!l,l&&(i=mo(l))}return i}startAnimation(t){const{drag:n,dragMomentum:s,dragElastic:r,dragTransition:o,dragSnapToOrigin:i,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},c=K(d=>{if(!at(d,n,this.currentDirection))return;let h=a&&a[d]||{};i&&(h={min:0,max:0});const f=r?200:1e6,p=r?40:1e7,x={type:"inertia",velocity:s?t[d]:0,bounceStiffness:f,bounceDamping:p,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(d,x)});return Promise.all(c).then(l)}startAxisValueAnimation(t,n){const s=this.getAxisMotionValue(t);return cn(this.visualElement,t),s.start(Hn(t,s,0,n,this.visualElement,!1))}stopAnimation(){K(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){K(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,s=this.visualElement.getProps(),r=s[n];return r||this.visualElement.getValue(t,(s.initial?s.initial[t]:void 0)||0)}snapToCursor(t){K(n=>{const{drag:s}=this.getProps();if(!at(n,s,this.currentDirection))return;const{projection:r}=this.visualElement,o=this.getAxisMotionValue(n);if(r&&r.layout){const{min:i,max:l}=r.layout.layoutBox[n];o.set(t[n]-R(i,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!Ae(n)||!s||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};K(i=>{const l=this.getAxisMotionValue(i);if(l&&this.constraints!==!1){const a=l.get();r[i]=Pd({min:a,max:a},this.constraints[i])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),K(i=>{if(!at(i,t,null))return;const l=this.getAxisMotionValue(i),{min:a,max:c}=this.constraints[i];l.set(R(a,c,r[i]))})}addListeners(){if(!this.visualElement.current)return;Vd.set(this.visualElement,this);const t=this.visualElement.current,n=he(t,"pointerdown",a=>{const{drag:c,dragListener:d=!0}=this.getProps();c&&d&&this.start(a)}),s=()=>{const{dragConstraints:a}=this.getProps();Ae(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,o=r.addEventListener("measure",s);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),k.read(s);const i=se(window,"resize",()=>this.scalePositionWithinConstraints()),l=r.addEventListener("didUpdate",({delta:a,hasLayoutChanged:c})=>{this.isDragging&&c&&(K(d=>{const h=this.getAxisMotionValue(d);h&&(this.originPoint[d]+=a[d].translate,h.set(h.get()+a[d].translate))}),this.visualElement.render())});return()=>{i(),n(),o(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:r=!1,dragConstraints:o=!1,dragElastic:i=dn,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:s,dragPropagation:r,dragConstraints:o,dragElastic:i,dragMomentum:l}}}function at(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Md(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class Rd extends pe{constructor(t){super(t),this.removeGroupControls=z,this.removeListeners=z,this.controls=new Ed(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||z}unmount(){this.removeGroupControls(),this.removeListeners()}}const er=e=>(t,n)=>{e&&k.postRender(()=>e(t,n))};class Dd extends pe{constructor(){super(...arguments),this.removePointerDownListener=z}onPointerDown(t){this.session=new uo(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:bo(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:s,onPanEnd:r}=this.node.getProps();return{onSessionStart:er(t),onStart:er(n),onMove:s,onEnd:(o,i)=>{delete this.session,r&&k.postRender(()=>r(o,i))}}}mount(){this.removePointerDownListener=he(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const qn=m.createContext(null);function Ld(){const e=m.useContext(qn);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:s}=e,r=m.useId();m.useEffect(()=>s(r),[]);const o=m.useCallback(()=>n&&n(r),[r,n]);return!t&&n?[!1,o]:[!0]}const vo=m.createContext({}),wo=m.createContext({}),ut={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function tr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const _e={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(S.test(e))e=parseFloat(e);else return e;const n=tr(e,t.target.x),s=tr(e,t.target.y);return`${n}% ${s}%`}},Id={correct:(e,{treeScale:t,projectionDelta:n})=>{const s=e,r=fe.parse(e);if(r.length>5)return s;const o=fe.createTransformer(e),i=typeof r[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;r[0+i]/=l,r[1+i]/=a;const c=R(l,a,.5);return typeof r[2+i]=="number"&&(r[2+i]/=c),typeof r[3+i]=="number"&&(r[3+i]/=c),o(r)}},bt={};function Od(e){Object.assign(bt,e)}const{schedule:Yn,cancel:Tp}=vi(queueMicrotask,!1);class Bd extends m.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:s,layoutId:r}=this.props,{projection:o}=t;Od(Fd),o&&(n.group&&n.group.add(o),s&&s.register&&r&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),ut.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:s,drag:r,isPresent:o}=this.props,i=s.projection;return i&&(i.isPresent=o,r||t.layoutDependency!==n||n===void 0?i.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?i.promote():i.relegate()||k.postRender(()=>{const l=i.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Yn.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(r),s&&s.deregister&&s.deregister(r))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function To(e){const[t,n]=Ld(),s=m.useContext(vo);return u.jsx(Bd,{...e,layoutGroup:s,switchLayoutGroup:m.useContext(wo),isPresent:t,safeToRemove:n})}const Fd={borderRadius:{..._e,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:_e,borderTopRightRadius:_e,borderBottomLeftRadius:_e,borderBottomRightRadius:_e,boxShadow:Id},Po=["TopLeft","TopRight","BottomLeft","BottomRight"],zd=Po.length,nr=e=>typeof e=="string"?parseFloat(e):e,sr=e=>typeof e=="number"||S.test(e);function _d(e,t,n,s,r,o){r?(e.opacity=R(0,n.opacity!==void 0?n.opacity:1,Hd(s)),e.opacityExit=R(t.opacity!==void 0?t.opacity:1,0,Ud(s))):o&&(e.opacity=R(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let i=0;i<zd;i++){const l=`border${Po[i]}Radius`;let a=rr(t,l),c=rr(n,l);if(a===void 0&&c===void 0)continue;a||(a=0),c||(c=0),a===0||c===0||sr(a)===sr(c)?(e[l]=Math.max(R(nr(a),nr(c),s),0),(J.test(c)||J.test(a))&&(e[l]+="%")):e[l]=c}(t.rotate||n.rotate)&&(e.rotate=R(t.rotate||0,n.rotate||0,s))}function rr(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Hd=So(0,.5,Ai),Ud=So(.5,.95,z);function So(e,t,n){return s=>s<e?0:s>t?1:n(we(e,t,s))}function ir(e,t){e.min=t.min,e.max=t.max}function $(e,t){ir(e.x,t.x),ir(e.y,t.y)}function or(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function ar(e,t,n,s,r){return e-=t,e=xt(e,1/n,s),r!==void 0&&(e=xt(e,1/r,s)),e}function Wd(e,t=0,n=1,s=.5,r,o=e,i=e){if(J.test(t)&&(t=parseFloat(t),t=R(i.min,i.max,t/100)-i.min),typeof t!="number")return;let l=R(o.min,o.max,s);e===o&&(l-=t),e.min=ar(e.min,t,n,l,r),e.max=ar(e.max,t,n,l,r)}function lr(e,t,[n,s,r],o,i){Wd(e,t[n],t[s],t[r],t.scale,o,i)}const Gd=["x","scaleX","originX"],$d=["y","scaleY","originY"];function cr(e,t,n,s){lr(e.x,t,Gd,n?n.x:void 0,s?s.x:void 0),lr(e.y,t,$d,n?n.y:void 0,s?s.y:void 0)}function ur(e){return e.translate===0&&e.scale===1}function jo(e){return ur(e.x)&&ur(e.y)}function dr(e,t){return e.min===t.min&&e.max===t.max}function Kd(e,t){return dr(e.x,t.x)&&dr(e.y,t.y)}function hr(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Co(e,t){return hr(e.x,t.x)&&hr(e.y,t.y)}function fr(e){return G(e.x)/G(e.y)}function pr(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class qd{constructor(){this.members=[]}add(t){Un(this.members,t),t.scheduleRender()}remove(t){if(Wn(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(r=>t===r);if(n===0)return!1;let s;for(let r=n;r>=0;r--){const o=this.members[r];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(t,n){const s=this.lead;if(t!==s&&(this.prevLead=s,this.lead=t,t.show(),s)){s.instance&&s.scheduleRender(),t.scheduleRender(),t.resumeFrom=s,n&&(t.resumeFrom.preserveOpacity=!0),s.snapshot&&(t.snapshot=s.snapshot,t.snapshot.latestValues=s.animationValues||s.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:r}=t.options;r===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:s}=t;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Yd(e,t,n){let s="";const r=e.x.translate/t.x,o=e.y.translate/t.y,i=(n==null?void 0:n.z)||0;if((r||o||i)&&(s=`translate3d(${r}px, ${o}px, ${i}px) `),(t.x!==1||t.y!==1)&&(s+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:c,rotate:d,rotateX:h,rotateY:f,skewX:p,skewY:x}=n;c&&(s=`perspective(${c}px) ${s}`),d&&(s+=`rotate(${d}deg) `),h&&(s+=`rotateX(${h}deg) `),f&&(s+=`rotateY(${f}deg) `),p&&(s+=`skewX(${p}deg) `),x&&(s+=`skewY(${x}deg) `)}const l=e.x.scale*t.x,a=e.y.scale*t.y;return(l!==1||a!==1)&&(s+=`scale(${l}, ${a})`),s||"none"}const Xd=(e,t)=>e.depth-t.depth;class Zd{constructor(){this.children=[],this.isDirty=!1}add(t){Un(this.children,t),this.isDirty=!0}remove(t){Wn(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Xd),this.isDirty=!1,this.children.forEach(t)}}function dt(e){const t=U(e)?e.get():e;return zu(t)?t.toValue():t}function Jd(e,t){const n=Q.now(),s=({timestamp:r})=>{const o=r-n;o>=t&&(Y(s),e(o-t))};return k.read(s,!0),()=>Y(s)}function Qd(e){return e instanceof SVGElement&&e.tagName!=="svg"}function eh(e,t,n){const s=U(e)?e:Z(e);return s.start(Hn("",s,t,n)),s.animation}const xe={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},We=typeof window<"u"&&window.MotionDebug!==void 0,_t=["","X","Y","Z"],th={visibility:"hidden"},mr=1e3;let nh=0;function Ht(e,t,n,s){const{latestValues:r}=t;r[e]&&(n[e]=r[e],t.setStaticValue(e,0),s&&(s[e]=0))}function Ao(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=io(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:r,layoutId:o}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",k,!(r||o))}const{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&Ao(s)}function ko({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(i={},l=t==null?void 0:t()){this.id=nh++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,We&&(xe.totalNodes=xe.resolvedTargetDeltas=xe.recalculatedProjection=0),this.nodes.forEach(ih),this.nodes.forEach(uh),this.nodes.forEach(dh),this.nodes.forEach(oh),We&&window.MotionDebug.record(xe)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new Zd)}addEventListener(i,l){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new Gn),this.eventHandlers.get(i).add(l)}notifyListeners(i,...l){const a=this.eventHandlers.get(i);a&&a.notify(...l)}hasListeners(i){return this.eventHandlers.has(i)}mount(i,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Qd(i),this.instance=i;const{layoutId:a,layout:c,visualElement:d}=this.options;if(d&&!d.current&&d.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(c||a)&&(this.isLayoutDirty=!0),e){let h;const f=()=>this.root.updateBlockedByResize=!1;e(i,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Jd(f,250),ut.hasAnimatedSinceResize&&(ut.hasAnimatedSinceResize=!1,this.nodes.forEach(yr))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&d&&(a||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:p,layout:x})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const g=this.options.transition||d.getDefaultTransition()||gh,{onLayoutAnimationStart:y,onLayoutAnimationComplete:b}=d.getProps(),w=!this.targetLayout||!Co(this.targetLayout,x)||p,T=!f&&p;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||T||f&&(w||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,T);const A={...Cn(g,"layout"),onPlay:y,onComplete:b};(d.shouldReduceMotion||this.options.layoutRoot)&&(A.delay=0,A.type=!1),this.startAnimation(A)}else f||yr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=x})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Y(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(hh),this.animationId++)}getTransformTemplate(){const{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Ao(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const h=this.path[d];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(gr);return}this.isUpdating||this.nodes.forEach(lh),this.isUpdating=!1,this.nodes.forEach(ch),this.nodes.forEach(sh),this.nodes.forEach(rh),this.clearAllSnapshots();const l=Q.now();F.delta=oe(0,1e3/60,l-F.timestamp),F.timestamp=l,F.isProcessing=!0,Rt.update.process(F),Rt.preRender.process(F),Rt.render.process(F),F.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Yn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(ah),this.sharedNodes.forEach(fh)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,k.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){k.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=L(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(l=!1),l){const a=s(this.instance);this.scroll={animationId:this.root.animationId,phase:i,isRoot:a,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:a}}}resetTransform(){if(!r)return;const i=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!jo(this.projectionDelta),a=this.getTransformTemplate(),c=a?a(this.latestValues,""):void 0,d=c!==this.prevTransformTemplateValue;i&&(l||ye(this.latestValues)||d)&&(r(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return i&&(a=this.removeTransform(a)),yh(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){var i;const{visualElement:l}=this.options;if(!l)return L();const a=l.measureViewportBox();if(!(((i=this.scroll)===null||i===void 0?void 0:i.wasRoot)||this.path.some(xh))){const{scroll:d}=this.root;d&&(Ne(a.x,d.offset.x),Ne(a.y,d.offset.y))}return a}removeElementScroll(i){var l;const a=L();if($(a,i),!((l=this.scroll)===null||l===void 0)&&l.wasRoot)return a;for(let c=0;c<this.path.length;c++){const d=this.path[c],{scroll:h,options:f}=d;d!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&$(a,i),Ne(a.x,h.offset.x),Ne(a.y,h.offset.y))}return a}applyTransform(i,l=!1){const a=L();$(a,i);for(let c=0;c<this.path.length;c++){const d=this.path[c];!l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&Ve(a,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),ye(d.latestValues)&&Ve(a,d.latestValues)}return ye(this.latestValues)&&Ve(a,this.latestValues),a}removeTransform(i){const l=L();$(l,i);for(let a=0;a<this.path.length;a++){const c=this.path[a];if(!c.instance||!ye(c.latestValues))continue;hn(c.latestValues)&&c.updateSnapshot();const d=L(),h=c.measurePageBox();$(d,h),cr(l,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return ye(this.latestValues)&&cr(l,this.latestValues),l}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==F.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==a;if(!(i||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=F.timestamp,!this.targetDelta&&!this.relativeTarget){const p=this.getClosestProjectingParent();p&&p.layout&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=L(),this.relativeTargetOrigin=L(),qe(this.relativeTargetOrigin,this.layout.layoutBox,p.layout.layoutBox),$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=L(),this.targetWithTransforms=L()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),bd(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):$(this.target,this.layout.layoutBox),yo(this.target,this.targetDelta)):$(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const p=this.getClosestProjectingParent();p&&!!p.resumingFrom==!!this.resumingFrom&&!p.options.layoutScroll&&p.target&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=L(),this.relativeTargetOrigin=L(),qe(this.relativeTargetOrigin,this.target,p.target),$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}We&&xe.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||hn(this.parent.latestValues)||go(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var i;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let c=!0;if((this.isProjectionDirty||!((i=this.parent)===null||i===void 0)&&i.isProjectionDirty)&&(c=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===F.timestamp&&(c=!1),c)return;const{layout:d,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||h))return;$(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,p=this.treeScale.y;kd(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox,l.targetWithTransforms=L());const{target:x}=l;if(!x){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(or(this.prevProjectionDelta.x,this.projectionDelta.x),or(this.prevProjectionDelta.y,this.projectionDelta.y)),Ke(this.projectionDelta,this.layoutCorrected,x,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==p||!pr(this.projectionDelta.x,this.prevProjectionDelta.x)||!pr(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",x)),We&&xe.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){var l;if((l=this.options.visualElement)===null||l===void 0||l.scheduleRender(),i){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ke(),this.projectionDelta=ke(),this.projectionDeltaWithTransform=ke()}setAnimationOrigin(i,l=!1){const a=this.snapshot,c=a?a.latestValues:{},d={...this.latestValues},h=ke();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const f=L(),p=a?a.source:void 0,x=this.layout?this.layout.source:void 0,g=p!==x,y=this.getStack(),b=!y||y.members.length<=1,w=!!(g&&!b&&this.options.crossfade===!0&&!this.path.some(mh));this.animationProgress=0;let T;this.mixTargetDelta=A=>{const P=A/1e3;xr(h.x,i.x,P),xr(h.y,i.y,P),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(qe(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),ph(this.relativeTarget,this.relativeTargetOrigin,f,P),T&&Kd(this.relativeTarget,T)&&(this.isProjectionDirty=!1),T||(T=L()),$(T,this.relativeTarget)),g&&(this.animationValues=d,_d(d,c,this.latestValues,P,w,b)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=P},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Y(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=k.update(()=>{ut.hasAnimatedSinceResize=!0,this.currentAnimation=eh(0,mr,{...i,onUpdate:l=>{this.mixTargetDelta(l),i.onUpdate&&i.onUpdate(l)},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(mr),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:l,target:a,layout:c,latestValues:d}=i;if(!(!l||!a||!c)){if(this!==i&&this.layout&&c&&No(this.options.animationType,this.layout.layoutBox,c.layoutBox)){a=this.target||L();const h=G(this.layout.layoutBox.x);a.x.min=i.target.x.min,a.x.max=a.x.min+h;const f=G(this.layout.layoutBox.y);a.y.min=i.target.y.min,a.y.max=a.y.min+f}$(l,a),Ve(l,d),Ke(this.projectionDeltaWithTransform,this.layoutCorrected,l,d)}}registerSharedNode(i,l){this.sharedNodes.has(i)||this.sharedNodes.set(i,new qd),this.sharedNodes.get(i).add(l);const c=l.options.initialPromotionConfig;l.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(l):void 0})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){var i;const{layoutId:l}=this.options;return l?((i=this.getStack())===null||i===void 0?void 0:i.lead)||this:this}getPrevLead(){var i;const{layoutId:l}=this.options;return l?(i=this.getStack())===null||i===void 0?void 0:i.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:l,preserveFollowOpacity:a}={}){const c=this.getStack();c&&c.promote(this,a),i&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetSkewAndRotation(){const{visualElement:i}=this.options;if(!i)return;let l=!1;const{latestValues:a}=i;if((a.z||a.rotate||a.rotateX||a.rotateY||a.rotateZ||a.skewX||a.skewY)&&(l=!0),!l)return;const c={};a.z&&Ht("z",i,c,this.animationValues);for(let d=0;d<_t.length;d++)Ht(`rotate${_t[d]}`,i,c,this.animationValues),Ht(`skew${_t[d]}`,i,c,this.animationValues);i.render();for(const d in c)i.setStaticValue(d,c[d]),this.animationValues&&(this.animationValues[d]=c[d]);i.scheduleRender()}getProjectionStyles(i){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return th;const c={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=dt(i==null?void 0:i.pointerEvents)||"",c.transform=d?d(this.latestValues,""):"none",c;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const g={};return this.options.layoutId&&(g.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,g.pointerEvents=dt(i==null?void 0:i.pointerEvents)||""),this.hasProjected&&!ye(this.latestValues)&&(g.transform=d?d({},""):"none",this.hasProjected=!1),g}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),c.transform=Yd(this.projectionDeltaWithTransform,this.treeScale,f),d&&(c.transform=d(f,c.transform));const{x:p,y:x}=this.projectionDelta;c.transformOrigin=`${p.origin*100}% ${x.origin*100}% 0`,h.animationValues?c.opacity=h===this?(a=(l=f.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const g in bt){if(f[g]===void 0)continue;const{correct:y,applyTo:b}=bt[g],w=c.transform==="none"?f[g]:y(f[g],h);if(b){const T=b.length;for(let A=0;A<T;A++)c[b[A]]=w}else c[g]=w}return this.options.layoutId&&(c.pointerEvents=h===this?dt(i==null?void 0:i.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>{var l;return(l=i.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(gr),this.root.sharedNodes.clear()}}}function sh(e){e.updateLayout()}function rh(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:r}=e.layout,{animationType:o}=e.options,i=n.source!==e.layout.source;o==="size"?K(h=>{const f=i?n.measuredBox[h]:n.layoutBox[h],p=G(f);f.min=s[h].min,f.max=f.min+p}):No(o,n.layoutBox,s)&&K(h=>{const f=i?n.measuredBox[h]:n.layoutBox[h],p=G(s[h]);f.max=f.min+p,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+p)});const l=ke();Ke(l,s,n.layoutBox);const a=ke();i?Ke(a,e.applyTransform(r,!0),n.measuredBox):Ke(a,s,n.layoutBox);const c=!jo(l);let d=!1;if(!e.resumeFrom){const h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:p}=h;if(f&&p){const x=L();qe(x,n.layoutBox,f.layoutBox);const g=L();qe(g,s,p.layoutBox),Co(x,g)||(d=!0),h.options.layoutRoot&&(e.relativeTarget=g,e.relativeTargetOrigin=x,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:c,hasRelativeTargetChanged:d})}else if(e.isLead()){const{onExitComplete:s}=e.options;s&&s()}e.options.transition=void 0}function ih(e){We&&xe.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function oh(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function ah(e){e.clearSnapshot()}function gr(e){e.clearMeasurements()}function lh(e){e.isLayoutDirty=!1}function ch(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function yr(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function uh(e){e.resolveTargetDelta()}function dh(e){e.calcProjection()}function hh(e){e.resetSkewAndRotation()}function fh(e){e.removeLeadSnapshot()}function xr(e,t,n){e.translate=R(t.translate,0,n),e.scale=R(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function br(e,t,n,s){e.min=R(t.min,n.min,s),e.max=R(t.max,n.max,s)}function ph(e,t,n,s){br(e.x,t.x,n.x,s),br(e.y,t.y,n.y,s)}function mh(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const gh={duration:.45,ease:[.4,0,.1,1]},vr=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),wr=vr("applewebkit/")&&!vr("chrome/")?Math.round:z;function Tr(e){e.min=wr(e.min),e.max=wr(e.max)}function yh(e){Tr(e.x),Tr(e.y)}function No(e,t,n){return e==="position"||e==="preserve-aspect"&&!xd(fr(t),fr(n),.2)}function xh(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const bh=ko({attachResizeListener:(e,t)=>se(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ut={current:void 0},Vo=ko({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Ut.current){const e=new bh({});e.mount(window),e.setOptions({layoutScroll:!0}),Ut.current=e}return Ut.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),vh={pan:{Feature:Dd},drag:{Feature:Rd,ProjectionNode:Vo,MeasureLayout:To}};function Pr(e,t,n){const{props:s}=e;e.animationState&&s.whileHover&&e.animationState.setActive("whileHover",n);const r=s[n?"onHoverStart":"onHoverEnd"];r&&k.postRender(()=>r(t,nt(t)))}class wh extends pe{mount(){const{current:t,props:n}=this.node;t&&(this.unmount=ld(t,s=>(Pr(this.node,s,!0),r=>Pr(this.node,r,!1)),{passive:!n.onHoverStart&&!n.onHoverEnd}))}unmount(){}}class Th extends pe{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=de(se(this.node.current,"focus",()=>this.onFocus()),se(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Eo=(e,t)=>t?e===t?!0:Eo(e,t.parentElement):!1;function Wt(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,nt(n))}class Ph extends pe{constructor(){super(...arguments),this.removeStartListeners=z,this.removeEndListeners=z,this.removeAccessibleListeners=z,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const s=this.node.getProps(),o=he(window,"pointerup",(l,a)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:d,globalTapTarget:h}=this.node.getProps(),f=!h&&!Eo(this.node.current,l.target)?d:c;f&&k.update(()=>f(l,a))},{passive:!(s.onTap||s.onPointerUp)}),i=he(window,"pointercancel",(l,a)=>this.cancelPress(l,a),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=de(o,i),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const i=l=>{l.key!=="Enter"||!this.checkPressEnd()||Wt("up",(a,c)=>{const{onTap:d}=this.node.getProps();d&&k.postRender(()=>d(a,c))})};this.removeEndListeners(),this.removeEndListeners=se(this.node.current,"keyup",i),Wt("down",(l,a)=>{this.startPress(l,a)})},n=se(this.node.current,"keydown",t),s=()=>{this.isPressing&&Wt("cancel",(o,i)=>this.cancelPress(o,i))},r=se(this.node.current,"blur",s);this.removeAccessibleListeners=de(n,r)}}startPress(t,n){this.isPressing=!0;const{onTapStart:s,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&k.postRender(()=>s(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!lo()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:s}=this.node.getProps();s&&k.postRender(()=>s(t,n))}mount(){const t=this.node.getProps(),n=he(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),s=se(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=de(n,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const pn=new WeakMap,Gt=new WeakMap,Sh=e=>{const t=pn.get(e.target);t&&t(e)},jh=e=>{e.forEach(Sh)};function Ch({root:e,...t}){const n=e||document;Gt.has(n)||Gt.set(n,{});const s=Gt.get(n),r=JSON.stringify(t);return s[r]||(s[r]=new IntersectionObserver(jh,{root:e,...t})),s[r]}function Ah(e,t,n){const s=Ch(t);return pn.set(e,n),s.observe(e),()=>{pn.delete(e),s.unobserve(e)}}const kh={some:0,all:1};class Nh extends pe{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:s,amount:r="some",once:o}=t,i={root:n?n.current:void 0,rootMargin:s,threshold:typeof r=="number"?r:kh[r]},l=a=>{const{isIntersecting:c}=a;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:d,onViewportLeave:h}=this.node.getProps(),f=c?d:h;f&&f(a)};return Ah(this.node.current,i,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Vh(t,n))&&this.startObserver()}unmount(){}}function Vh({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Eh={inView:{Feature:Nh},tap:{Feature:Ph},focus:{Feature:Th},hover:{Feature:wh}},Mh={layout:{ProjectionNode:Vo,MeasureLayout:To}},Xn=m.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),At=m.createContext({}),Zn=typeof window<"u",Jn=Zn?m.useLayoutEffect:m.useEffect,Mo=m.createContext({strict:!1});function Rh(e,t,n,s,r){var o,i;const{visualElement:l}=m.useContext(At),a=m.useContext(Mo),c=m.useContext(qn),d=m.useContext(Xn).reducedMotion,h=m.useRef();s=s||a.renderer,!h.current&&s&&(h.current=s(e,{visualState:t,parent:l,props:n,presenceContext:c,blockInitialAnimation:c?c.initial===!1:!1,reducedMotionConfig:d}));const f=h.current,p=m.useContext(wo);f&&!f.projection&&r&&(f.type==="html"||f.type==="svg")&&Dh(h.current,n,r,p);const x=m.useRef(!1);m.useInsertionEffect(()=>{f&&x.current&&f.update(n,c)});const g=n[ro],y=m.useRef(!!g&&!(!((o=window.MotionHandoffIsComplete)===null||o===void 0)&&o.call(window,g))&&((i=window.MotionHasOptimisedAnimation)===null||i===void 0?void 0:i.call(window,g)));return Jn(()=>{f&&(x.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Yn.render(f.render),y.current&&f.animationState&&f.animationState.animateChanges())}),m.useEffect(()=>{f&&(!y.current&&f.animationState&&f.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var b;(b=window.MotionHandoffMarkAsComplete)===null||b===void 0||b.call(window,g)}),y.current=!1))}),f}function Dh(e,t,n,s){const{layoutId:r,layout:o,drag:i,dragConstraints:l,layoutScroll:a,layoutRoot:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Ro(e.parent)),e.projection.setOptions({layoutId:r,layout:o,alwaysMeasureLayout:!!i||l&&Ae(l),visualElement:e,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,layoutScroll:a,layoutRoot:c})}function Ro(e){if(e)return e.options.allowProjection!==!1?e.projection:Ro(e.parent)}function Lh(e,t,n){return m.useCallback(s=>{s&&e.mount&&e.mount(s),t&&(s?t.mount(s):t.unmount()),n&&(typeof n=="function"?n(s):Ae(n)&&(n.current=s))},[t])}function kt(e){return St(e.animate)||jn.some(t=>Xe(e[t]))}function Do(e){return!!(kt(e)||e.variants)}function Ih(e,t){if(kt(e)){const{initial:n,animate:s}=e;return{initial:n===!1||Xe(n)?n:void 0,animate:Xe(s)?s:void 0}}return e.inherit!==!1?t:{}}function Oh(e){const{initial:t,animate:n}=Ih(e,m.useContext(At));return m.useMemo(()=>({initial:t,animate:n}),[Sr(t),Sr(n)])}function Sr(e){return Array.isArray(e)?e.join(" "):e}const jr={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Le={};for(const e in jr)Le[e]={isEnabled:t=>jr[e].some(n=>!!t[n])};function Bh(e){for(const t in e)Le[t]={...Le[t],...e[t]}}const Fh=Symbol.for("motionComponentSymbol");function zh({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:s,Component:r}){e&&Bh(e);function o(l,a){let c;const d={...m.useContext(Xn),...l,layoutId:_h(l)},{isStatic:h}=d,f=Oh(l),p=s(l,h);if(!h&&Zn){Hh();const x=Uh(d);c=x.MeasureLayout,f.visualElement=Rh(r,p,d,t,x.ProjectionNode)}return u.jsxs(At.Provider,{value:f,children:[c&&f.visualElement?u.jsx(c,{visualElement:f.visualElement,...d}):null,n(r,l,Lh(p,f.visualElement,a),p,h,f.visualElement)]})}const i=m.forwardRef(o);return i[Fh]=r,i}function _h({layoutId:e}){const t=m.useContext(vo).id;return t&&e!==void 0?t+"-"+e:e}function Hh(e,t){m.useContext(Mo).strict}function Uh(e){const{drag:t,layout:n}=Le;if(!t&&!n)return{};const s={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const Wh=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Qn(e){return typeof e!="string"||e.includes("-")?!1:!!(Wh.indexOf(e)>-1||/[A-Z]/u.test(e))}function Lo(e,{style:t,vars:n},s,r){Object.assign(e.style,t,r&&r.getProjectionStyles(s));for(const o in n)e.style.setProperty(o,n[o])}const Io=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Oo(e,t,n,s){Lo(e,t,void 0,s);for(const r in t.attrs)e.setAttribute(Io.has(r)?r:$n(r),t.attrs[r])}function Bo(e,{layout:t,layoutId:n}){return Pe.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!bt[e]||e==="opacity")}function es(e,t,n){var s;const{style:r}=e,o={};for(const i in r)(U(r[i])||t.style&&U(t.style[i])||Bo(i,e)||((s=n==null?void 0:n.getValue(i))===null||s===void 0?void 0:s.liveStyle)!==void 0)&&(o[i]=r[i]);return o}function Fo(e,t,n){const s=es(e,t,n);for(const r in e)if(U(e[r])||U(t[r])){const o=Qe.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;s[o]=e[r]}return s}function Nt(e){const t=m.useRef(null);return t.current===null&&(t.current=e()),t.current}function Gh({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},s,r,o){const i={latestValues:$h(s,r,o,e),renderState:t()};return n&&(i.mount=l=>n(s,l,i)),i}const zo=e=>(t,n)=>{const s=m.useContext(At),r=m.useContext(qn),o=()=>Gh(e,t,s,r);return n?o():Nt(o)};function $h(e,t,n,s){const r={},o=s(e,{});for(const f in o)r[f]=dt(o[f]);let{initial:i,animate:l}=e;const a=kt(e),c=Do(e);t&&c&&!a&&e.inherit!==!1&&(i===void 0&&(i=t.initial),l===void 0&&(l=t.animate));let d=n?n.initial===!1:!1;d=d||i===!1;const h=d?l:i;if(h&&typeof h!="boolean"&&!St(h)){const f=Array.isArray(h)?h:[h];for(let p=0;p<f.length;p++){const x=Pn(e,f[p]);if(x){const{transitionEnd:g,transition:y,...b}=x;for(const w in b){let T=b[w];if(Array.isArray(T)){const A=d?T.length-1:0;T=T[A]}T!==null&&(r[w]=T)}for(const w in g)r[w]=g[w]}}}return r}const ts=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),_o=()=>({...ts(),attrs:{}}),Ho=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Kh={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},qh=Qe.length;function Yh(e,t,n){let s="",r=!0;for(let o=0;o<qh;o++){const i=Qe[o],l=e[i];if(l===void 0)continue;let a=!0;if(typeof l=="number"?a=l===(i.startsWith("scale")?1:0):a=parseFloat(l)===0,!a||n){const c=Ho(l,Rn[i]);if(!a){r=!1;const d=Kh[i]||i;s+=`${d}(${c}) `}n&&(t[i]=c)}}return s=s.trim(),n?s=n(t,r?"":s):r&&(s="none"),s}function ns(e,t,n){const{style:s,vars:r,transformOrigin:o}=e;let i=!1,l=!1;for(const a in t){const c=t[a];if(Pe.has(a)){i=!0;continue}else if(Mi(a)){r[a]=c;continue}else{const d=Ho(c,Rn[a]);a.startsWith("origin")?(l=!0,o[a]=d):s[a]=d}}if(t.transform||(i||n?s.transform=Yh(t,e.transform,n):s.transform&&(s.transform="none")),l){const{originX:a="50%",originY:c="50%",originZ:d=0}=o;s.transformOrigin=`${a} ${c} ${d}`}}function Cr(e,t,n){return typeof e=="string"?e:S.transform(t+n*e)}function Xh(e,t,n){const s=Cr(t,e.x,e.width),r=Cr(n,e.y,e.height);return`${s} ${r}`}const Zh={offset:"stroke-dashoffset",array:"stroke-dasharray"},Jh={offset:"strokeDashoffset",array:"strokeDasharray"};function Qh(e,t,n=1,s=0,r=!0){e.pathLength=1;const o=r?Zh:Jh;e[o.offset]=S.transform(-s);const i=S.transform(t),l=S.transform(n);e[o.array]=`${i} ${l}`}function ss(e,{attrX:t,attrY:n,attrScale:s,originX:r,originY:o,pathLength:i,pathSpacing:l=1,pathOffset:a=0,...c},d,h){if(ns(e,c,h),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:p,dimensions:x}=e;f.transform&&(x&&(p.transform=f.transform),delete f.transform),x&&(r!==void 0||o!==void 0||p.transform)&&(p.transformOrigin=Xh(x,r!==void 0?r:.5,o!==void 0?o:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),s!==void 0&&(f.scale=s),i!==void 0&&Qh(f,i,l,a,!1)}const rs=e=>typeof e=="string"&&e.toLowerCase()==="svg",ef={useVisualState:zo({scrapeMotionValuesFromProps:Fo,createRenderState:_o,onMount:(e,t,{renderState:n,latestValues:s})=>{k.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),k.render(()=>{ss(n,s,rs(t.tagName),e.transformTemplate),Oo(t,n)})}})},tf={useVisualState:zo({scrapeMotionValuesFromProps:es,createRenderState:ts})};function Uo(e,t,n){for(const s in t)!U(t[s])&&!Bo(s,n)&&(e[s]=t[s])}function nf({transformTemplate:e},t){return m.useMemo(()=>{const n=ts();return ns(n,t,e),Object.assign({},n.vars,n.style)},[t])}function sf(e,t){const n=e.style||{},s={};return Uo(s,n,e),Object.assign(s,nf(e,t)),s}function rf(e,t){const n={},s=sf(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=s,n}const of=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function vt(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||of.has(e)}let Wo=e=>!vt(e);function af(e){e&&(Wo=t=>t.startsWith("on")?!vt(t):e(t))}try{af(require("@emotion/is-prop-valid").default)}catch{}function lf(e,t,n){const s={};for(const r in e)r==="values"&&typeof e.values=="object"||(Wo(r)||n===!0&&vt(r)||!t&&!vt(r)||e.draggable&&r.startsWith("onDrag"))&&(s[r]=e[r]);return s}function cf(e,t,n,s){const r=m.useMemo(()=>{const o=_o();return ss(o,t,rs(s),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Uo(o,e.style,e),r.style={...o,...r.style}}return r}function uf(e=!1){return(n,s,r,{latestValues:o},i)=>{const a=(Qn(n)?cf:rf)(s,o,i,n),c=lf(s,typeof n=="string",e),d=n!==m.Fragment?{...c,...a,ref:r}:{},{children:h}=s,f=m.useMemo(()=>U(h)?h.get():h,[h]);return m.createElement(n,{...d,children:f})}}function df(e,t){return function(s,{forwardMotionProps:r}={forwardMotionProps:!1}){const i={...Qn(s)?ef:tf,preloadedFeatures:e,useRender:uf(r),createVisualElement:t,Component:s};return zh(i)}}const mn={current:null},Go={current:!1};function hf(){if(Go.current=!0,!!Zn)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>mn.current=e.matches;e.addListener(t),t()}else mn.current=!1}function ff(e,t,n){for(const s in t){const r=t[s],o=n[s];if(U(r))e.addValue(s,r);else if(U(o))e.addValue(s,Z(r,{owner:e}));else if(o!==r)if(e.hasValue(s)){const i=e.getValue(s);i.liveStyle===!0?i.jump(r):i.hasAnimated||i.set(r)}else{const i=e.getStaticValue(s);e.addValue(s,Z(i!==void 0?i:r,{owner:e}))}}for(const s in n)t[s]===void 0&&e.removeValue(s);return t}const Ar=new WeakMap,pf=[...Li,H,fe],mf=e=>pf.find(Di(e)),kr=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class gf{scrapeMotionValuesFromProps(t,n,s){return{}}constructor({parent:t,props:n,presenceContext:s,reducedMotionConfig:r,blockInitialAnimation:o,visualState:i},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Vn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=Q.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,k.render(this.render,!1,!0))};const{latestValues:a,renderState:c}=i;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=c,this.parent=t,this.props=n,this.presenceContext=s,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=l,this.blockInitialAnimation=!!o,this.isControllingVariants=kt(n),this.isVariantNode=Do(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:d,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const p=h[f];a[f]!==void 0&&U(p)&&p.set(a[f],!1)}}mount(t){this.current=t,Ar.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Go.current||hf(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:mn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Ar.delete(this.current),this.projection&&this.projection.unmount(),Y(this.notifyUpdate),Y(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const s=Pe.has(t),r=n.on("change",l=>{this.latestValues[t]=l,this.props.onUpdate&&k.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{r(),o(),i&&i(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Le){const n=Le[t];if(!n)continue;const{isEnabled:s,Feature:r}=n;if(!this.features[t]&&r&&s(this.props)&&(this.features[t]=new r(this)),this.features[t]){const o=this.features[t];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):L()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<kr.length;s++){const r=kr[s];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const o="on"+r,i=t[o];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=ff(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const s=this.values.get(t);n!==s&&(s&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let s=this.values.get(t);return s===void 0&&n!==void 0&&(s=Z(n===null?void 0:n,{owner:this}),this.addValue(t,s)),s}readValue(t,n){var s;let r=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(s=this.getBaseTargetFromProps(this.props,t))!==null&&s!==void 0?s:this.readValueFromInstance(this.current,t,this.options);return r!=null&&(typeof r=="string"&&(Vi(r)||Ni(r))?r=parseFloat(r):!mf(r)&&fe.test(n)&&(r=Ui(t,n)),this.setBaseTarget(t,U(r)?r.get():r)),U(r)?r.get():r}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:s}=this.props;let r;if(typeof s=="string"||typeof s=="object"){const i=Pn(this.props,s,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);i&&(r=i[t])}if(s&&r!==void 0)return r;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!U(o)?o:this.initialValues[t]!==void 0&&r===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Gn),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class $o extends gf{constructor(){super(...arguments),this.KeyframeResolver=Wi}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:s}){delete n[t],delete s[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;U(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function yf(e){return window.getComputedStyle(e)}class xf extends $o{constructor(){super(...arguments),this.type="html",this.renderInstance=Lo}readValueFromInstance(t,n){if(Pe.has(n)){const s=Dn(n);return s&&s.default||0}else{const s=yf(t),r=(Mi(n)?s.getPropertyValue(n):s[n])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:n}){return xo(t,n)}build(t,n,s){ns(t,n,s.transformTemplate)}scrapeMotionValuesFromProps(t,n,s){return es(t,n,s)}}class bf extends $o{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=L}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(Pe.has(n)){const s=Dn(n);return s&&s.default||0}return n=Io.has(n)?n:$n(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,s){return Fo(t,n,s)}build(t,n,s){ss(t,n,this.isSVGTag,s.transformTemplate)}renderInstance(t,n,s,r){Oo(t,n,s,r)}mount(t){this.isSVGTag=rs(t.tagName),super.mount(t)}}const vf=(e,t)=>Qn(e)?new bf(t):new xf(t,{allowProjection:e!==m.Fragment}),wf=df({...ad,...Eh,...vh,...Mh},vf),v=ac(wf);function Tf(e){const t=Nt(()=>Z(e)),{isStatic:n}=m.useContext(Xn);if(n){const[,s]=m.useState(e);m.useEffect(()=>t.on("change",s),[])}return t}function Ko(e,t){const n=Tf(t()),s=()=>n.set(t());return s(),Jn(()=>{const r=()=>k.preRender(s,!1,!0),o=e.map(i=>i.on("change",r));return()=>{o.forEach(i=>i()),Y(s)}}),n}const Pf=e=>e&&typeof e=="object"&&e.mix,Sf=e=>Pf(e)?e.mix:void 0;function jf(...e){const t=!Array.isArray(e[0]),n=t?0:-1,s=e[0+n],r=e[1+n],o=e[2+n],i=e[3+n],l=Fn(r,o,{mixer:Sf(o[0]),...i});return t?l(s):l}function Cf(e){$e.current=[],e();const t=Ko($e.current,e);return $e.current=void 0,t}function Nr(e,t,n,s){if(typeof e=="function")return Cf(e);const r=typeof t=="function"?t:jf(t,n,s);return Array.isArray(e)?Vr(e,r):Vr([e],([o])=>r(o))}function Vr(e,t){const n=Nt(()=>[]);return Ko(e,()=>{n.length=0;const s=e.length;for(let r=0;r<s;r++)n[r]=e[r].get();return t(n)})}const ht=new WeakMap;let ue;function Af(e,t){if(t){const{inlineSize:n,blockSize:s}=t[0];return{width:n,height:s}}else return e instanceof SVGElement&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight}}function kf({target:e,contentRect:t,borderBoxSize:n}){var s;(s=ht.get(e))===null||s===void 0||s.forEach(r=>{r({target:e,contentSize:t,get size(){return Af(e,n)}})})}function Nf(e){e.forEach(kf)}function Vf(){typeof ResizeObserver>"u"||(ue=new ResizeObserver(Nf))}function Ef(e,t){ue||Vf();const n=Kn(e);return n.forEach(s=>{let r=ht.get(s);r||(r=new Set,ht.set(s,r)),r.add(t),ue==null||ue.observe(s)}),()=>{n.forEach(s=>{const r=ht.get(s);r==null||r.delete(t),r!=null&&r.size||ue==null||ue.unobserve(s)})}}const ft=new Set;let Ye;function Mf(){Ye=()=>{const e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};ft.forEach(n=>n(t))},window.addEventListener("resize",Ye)}function Rf(e){return ft.add(e),Ye||Mf(),()=>{ft.delete(e),!ft.size&&Ye&&(Ye=void 0)}}function Df(e,t){return typeof e=="function"?Rf(e):Ef(e,t)}const Lf=50,Er=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),If=()=>({time:0,x:Er(),y:Er()}),Of={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Mr(e,t,n,s){const r=n[t],{length:o,position:i}=Of[t],l=r.current,a=n.time;r.current=e[`scroll${i}`],r.scrollLength=e[`scroll${o}`]-e[`client${o}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=we(0,r.scrollLength,r.current);const c=s-a;r.velocity=c>Lf?0:In(r.current-l,c)}function Bf(e,t,n){Mr(e,"x",t,n),Mr(e,"y",t,n),t.time=n}function Ff(e,t){const n={x:0,y:0};let s=e;for(;s&&s!==t;)if(s instanceof HTMLElement)n.x+=s.offsetLeft,n.y+=s.offsetTop,s=s.offsetParent;else if(s.tagName==="svg"){const r=s.getBoundingClientRect();s=s.parentElement;const o=s.getBoundingClientRect();n.x+=r.left-o.left,n.y+=r.top-o.top}else if(s instanceof SVGGraphicsElement){const{x:r,y:o}=s.getBBox();n.x+=r,n.y+=o;let i=null,l=s.parentNode;for(;!i;)l.tagName==="svg"&&(i=l),l=s.parentNode;s=i}else break;return n}const zf={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},gn={start:0,center:.5,end:1};function Rr(e,t,n=0){let s=0;if(e in gn&&(e=gn[e]),typeof e=="string"){const r=parseFloat(e);e.endsWith("px")?s=r:e.endsWith("%")?e=r/100:e.endsWith("vw")?s=r/100*document.documentElement.clientWidth:e.endsWith("vh")?s=r/100*document.documentElement.clientHeight:e=r}return typeof e=="number"&&(s=t*e),n+s}const _f=[0,0];function Hf(e,t,n,s){let r=Array.isArray(e)?e:_f,o=0,i=0;return typeof e=="number"?r=[e,e]:typeof e=="string"&&(e=e.trim(),e.includes(" ")?r=e.split(" "):r=[e,gn[e]?e:"0"]),o=Rr(r[0],n,s),i=Rr(r[1],t),o-i}const Uf={x:0,y:0};function Wf(e){return"getBBox"in e&&e.tagName!=="svg"?e.getBBox():{width:e.clientWidth,height:e.clientHeight}}function Gf(e,t,n){const{offset:s=zf.All}=n,{target:r=e,axis:o="y"}=n,i=o==="y"?"height":"width",l=r!==e?Ff(r,e):Uf,a=r===e?{width:e.scrollWidth,height:e.scrollHeight}:Wf(r),c={width:e.clientWidth,height:e.clientHeight};t[o].offset.length=0;let d=!t[o].interpolate;const h=s.length;for(let f=0;f<h;f++){const p=Hf(s[f],c[i],a[i],l[o]);!d&&p!==t[o].interpolatorOffsets[f]&&(d=!0),t[o].offset[f]=p}d&&(t[o].interpolate=Fn(t[o].offset,Qi(s)),t[o].interpolatorOffsets=[...t[o].offset]),t[o].progress=t[o].interpolate(t[o].current)}function $f(e,t=e,n){if(n.x.targetOffset=0,n.y.targetOffset=0,t!==e){let s=t;for(;s&&s!==e;)n.x.targetOffset+=s.offsetLeft,n.y.targetOffset+=s.offsetTop,s=s.offsetParent}n.x.targetLength=t===e?t.scrollWidth:t.clientWidth,n.y.targetLength=t===e?t.scrollHeight:t.clientHeight,n.x.containerLength=e.clientWidth,n.y.containerLength=e.clientHeight}function Kf(e,t,n,s={}){return{measure:()=>$f(e,s.target,n),update:r=>{Bf(e,n,r),(s.offset||s.target)&&Gf(e,n,s)},notify:()=>t(n)}}const He=new WeakMap,Dr=new WeakMap,$t=new WeakMap,Lr=e=>e===document.documentElement?window:e;function is(e,{container:t=document.documentElement,...n}={}){let s=$t.get(t);s||(s=new Set,$t.set(t,s));const r=If(),o=Kf(t,e,r,n);if(s.add(o),!He.has(t)){const l=()=>{for(const f of s)f.measure()},a=()=>{for(const f of s)f.update(F.timestamp)},c=()=>{for(const f of s)f.notify()},d=()=>{k.read(l,!1,!0),k.read(a,!1,!0),k.update(c,!1,!0)};He.set(t,d);const h=Lr(t);window.addEventListener("resize",d,{passive:!0}),t!==document.documentElement&&Dr.set(t,Df(t,d)),h.addEventListener("scroll",d,{passive:!0})}const i=He.get(t);return k.read(i,!1,!0),()=>{var l;Y(i);const a=$t.get(t);if(!a||(a.delete(o),a.size))return;const c=He.get(t);He.delete(t),c&&(Lr(t).removeEventListener("scroll",c),(l=Dr.get(t))===null||l===void 0||l(),window.removeEventListener("resize",c))}}function qo(e,t){let n;const s=()=>{const{currentTime:r}=t,i=(r===null?0:r.value)/100;n!==i&&e(i),n=i};return k.update(s,!0),()=>Y(s)}function qf({source:e,container:t,axis:n="y"}){e&&(t=e);const s={value:0},r=is(o=>{s.value=o[n].progress*100},{container:t,axis:n});return{currentTime:s,cancel:r}}const Kt=new Map;function Yo({source:e,container:t=document.documentElement,axis:n="y"}={}){e&&(t=e),Kt.has(t)||Kt.set(t,{});const s=Kt.get(t);return s[n]||(s[n]=so()?new ScrollTimeline({source:t,axis:n}):qf({source:t,axis:n})),s[n]}function Yf(e){return e.length===2}function Xo(e){return e&&(e.target||e.offset)}function Xf(e,t){return Yf(e)||Xo(t)?is(n=>{e(n[t.axis].progress,n)},t):qo(e,Yo(t))}function Zf(e,t){if(e.flatten(),Xo(t))return e.pause(),is(n=>{e.time=e.duration*n[t.axis].progress},t);{const n=Yo(t);return e.attachTimeline?e.attachTimeline(n,s=>(s.pause(),qo(r=>{s.time=s.duration*r},n))):z}}function Jf(e,{axis:t="y",...n}={}){const s={axis:t,...n};return typeof e=="function"?Xf(e,s):Zf(e,s)}function Ir(e,t){mc(!!(!t||t.current))}const Qf=()=>({scrollX:Z(0),scrollY:Z(0),scrollXProgress:Z(0),scrollYProgress:Z(0)});function ep({container:e,target:t,layoutEffect:n=!0,...s}={}){const r=Nt(Qf);return(n?Jn:m.useEffect)(()=>(Ir("target",t),Ir("container",e),Jf((i,{x:l,y:a})=>{r.scrollX.set(l.current),r.scrollXProgress.set(l.progress),r.scrollY.set(a.current),r.scrollYProgress.set(a.progress)},{...s,container:(e==null?void 0:e.current)||void 0,target:(t==null?void 0:t.current)||void 0})),[e,t,JSON.stringify(s.offset)]),r}const tp={some:0,all:1};function np(e,t,{root:n,margin:s,amount:r="some"}={}){const o=Kn(e),i=new WeakMap,l=c=>{c.forEach(d=>{const h=i.get(d.target);if(d.isIntersecting!==!!h)if(d.isIntersecting){const f=t(d);typeof f=="function"?i.set(d.target,f):a.unobserve(d.target)}else h&&(h(d),i.delete(d.target))})},a=new IntersectionObserver(l,{root:n,rootMargin:s,threshold:typeof r=="number"?r:tp[r]});return o.forEach(c=>a.observe(c)),()=>a.disconnect()}function st(e,{root:t,margin:n,amount:s,once:r=!1}={}){const[o,i]=m.useState(!1);return m.useEffect(()=>{if(!e.current||r&&o)return;const l=()=>(i(!0),r?void 0:()=>i(!1)),a={root:t&&t.current||void 0,margin:n,amount:s};return np(e.current,l,a)},[t,e,n,r,s]),o}const sp="/assets/BullBuster_1752063257019-D-NwXa2E.png";function rp(){const[e,t]=m.useState(!1),[n,s]=m.useState(!1);m.useEffect(()=>{const o=()=>{t(window.scrollY>50)};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)},[]);const r=o=>{const i=document.getElementById(o);i&&(i.scrollIntoView({behavior:"smooth",block:"start"}),s(!1))};return u.jsx(v.nav,{initial:{y:-100},animate:{y:0},className:`fixed top-0 w-full z-50 transition-all duration-300 ${e?"glass-navbar shadow-lg":"glass-navbar"}`,children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs("div",{className:"flex items-center justify-between h-16 sm:h-20",children:[u.jsxs(v.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-2 sm:space-x-3",children:[u.jsx("img",{src:sp,alt:"BullBuster Logo",className:"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 object-contain"}),u.jsx("span",{className:"text-lg sm:text-xl lg:text-2xl font-black text-brand-yellow",children:"BullBuster"})]}),u.jsx("div",{className:"hidden md:flex items-center space-x-6 lg:space-x-8",children:["home","about","menu","gallery","track","contact"].map(o=>u.jsx("button",{onClick:()=>r(o),className:"text-white hover:text-brand-yellow transition-colors duration-300 font-medium capitalize text-sm lg:text-base",children:o==="track"?"Track Order":o},o))}),u.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[u.jsx(v.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>r("menu"),className:"hidden sm:block bg-brand-yellow text-brand-black px-4 lg:px-6 py-2 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300 shadow-lg text-sm lg:text-base",children:"Order Now"}),u.jsx("button",{className:"md:hidden text-white p-2 rounded-lg hover:bg-white/10 transition-colors",onClick:()=>s(!n),children:n?u.jsx(ic,{size:24}):u.jsx(Zl,{size:24})})]})]}),n&&u.jsx(v.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden border-t border-brand-yellow/30 bg-brand-black-soft/95 backdrop-blur-xl",children:u.jsxs("div",{className:"px-4 py-6 space-y-4",children:[["home","about","menu","gallery","track","contact"].map(o=>u.jsx("button",{onClick:()=>r(o),className:"block w-full text-left text-white hover:text-brand-yellow transition-colors duration-300 font-medium capitalize py-2 text-lg",children:o==="track"?"Track Order":o},o)),u.jsx(v.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>r("menu"),className:"w-full bg-brand-yellow text-brand-black py-3 rounded-lg font-bold mt-4 hover:bg-brand-yellow-light transition-all duration-300",children:"Order Now"})]})})]})})}function ip(){const e=m.useRef(null),{scrollYProgress:t}=ep({target:e,offset:["start start","end start"]}),n=Nr(t,[0,1],["0%","50%"]),s=Nr(t,[0,1],[1,0]),r=o=>{const i=document.getElementById(o);i&&i.scrollIntoView({behavior:"smooth",block:"start"})};return u.jsxs("section",{ref:e,id:"home",className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[u.jsxs(v.div,{style:{y:n},className:"absolute inset-0 parallax-bg",children:[u.jsx("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080')"}}),u.jsx("div",{className:"absolute inset-0 hero-overlay"}),u.jsx(v.div,{animate:{background:["radial-gradient(circle at 20% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 50%)","radial-gradient(circle at 80% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 50%)","radial-gradient(circle at 20% 50%, rgba(255, 193, 7, 0.1) 0%, transparent 50%)"]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute inset-0"})]}),u.jsx(v.div,{animate:{y:[0,-30,0],rotate:[0,5,-5,0],scale:[1,1.1,1]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute top-20 left-10 text-6xl opacity-20",children:"🍕"}),u.jsx(v.div,{animate:{y:[0,-25,0],rotate:[0,-5,5,0],scale:[1,1.05,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:-2},className:"absolute top-40 right-20 text-5xl opacity-25",children:"🍗"}),u.jsx(v.div,{animate:{y:[0,-35,0],rotate:[0,10,-10,0],scale:[1,1.15,1]},transition:{duration:9,repeat:1/0,ease:"easeInOut",delay:-4},className:"absolute bottom-32 left-20 text-7xl opacity-30",children:"🍔"}),u.jsx(v.div,{animate:{rotate:360},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 right-1/4 w-32 h-32 border-2 border-brand-yellow/10 rounded-full"}),u.jsx(v.div,{animate:{rotate:-360},transition:{duration:25,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 left-1/4 w-24 h-24 border-2 border-brand-yellow/15 rounded-lg"}),u.jsxs(v.div,{style:{opacity:s},className:"relative z-10 text-center text-white px-6 max-w-7xl mx-auto",children:[u.jsxs(v.div,{initial:{y:-30,opacity:0},animate:{y:0,opacity:1},transition:{duration:.6,delay:.2},className:"flex justify-center items-center space-x-8 mb-8 text-sm",children:[u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx(sc,{className:"w-4 h-4 text-brand-yellow"}),u.jsx("span",{children:"4.9/5 Rating"})]}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx(yi,{className:"w-4 h-4 text-brand-yellow"}),u.jsx("span",{children:"Best in Lahore"})]}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx(Tn,{className:"w-4 h-4 text-brand-yellow"}),u.jsx("span",{children:"15 Min Delivery"})]})]}),u.jsxs(v.h1,{initial:{y:100,opacity:0,scale:.8},animate:{y:0,opacity:1,scale:1},transition:{duration:1.2,ease:"easeOut",delay:.4,type:"spring",stiffness:80},className:"text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl font-black mb-4 sm:mb-6 leading-none",children:[u.jsx("span",{className:"gradient-text",children:"BULL"}),u.jsx("span",{className:"text-white",children:"BUSTER"})]}),u.jsxs(v.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.6},className:"mb-8",children:[u.jsxs("p",{className:"text-lg sm:text-xl md:text-2xl lg:text-3xl font-light max-w-4xl mx-auto leading-relaxed px-4",children:["Experience the"," ",u.jsx("span",{className:"text-brand-yellow font-semibold",children:"finest fast food"})," ","in Lahore."]}),u.jsx("p",{className:"text-base sm:text-lg md:text-xl text-gray-300 mt-3 sm:mt-4 max-w-3xl mx-auto px-4",children:"Premium burgers, legendary taste, unforgettable moments."})]}),u.jsxs(v.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5,ease:"easeOut",delay:.8},className:"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center px-4",children:[u.jsxs(v.button,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{duration:.8,delay:1,type:"spring"},whileHover:{scale:1.08,boxShadow:"0 25px 50px rgba(255, 193, 7, 0.4)",y:-8,transition:{duration:.3}},whileTap:{scale:.95},onClick:()=>r("menu"),className:"group bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-6 sm:px-8 lg:px-10 py-3 sm:py-4 lg:py-5 rounded-xl sm:rounded-2xl text-base sm:text-lg font-bold transition-all duration-300 shadow-2xl flex items-center gap-2 sm:gap-3 hover-glow w-full sm:w-auto justify-center",children:[u.jsx(xi,{className:"w-6 h-6 group-hover:rotate-12 transition-transform duration-300"}),u.jsx("span",{children:"Explore Menu"})]}),u.jsxs(v.button,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{duration:.8,delay:1.2,type:"spring"},whileHover:{scale:1.08,backgroundColor:"rgba(255, 193, 7, 1)",color:"rgba(0, 0, 0, 1)",y:-8,boxShadow:"0 20px 40px rgba(255, 193, 7, 0.3)",transition:{duration:.3}},whileTap:{scale:.95},onClick:()=>r("about"),className:"group border-2 border-brand-yellow text-brand-yellow px-6 sm:px-8 lg:px-10 py-3 sm:py-4 lg:py-5 rounded-xl sm:rounded-2xl text-base sm:text-lg font-bold transition-all duration-300 flex items-center gap-2 sm:gap-3 backdrop-blur-sm bg-white/5 w-full sm:w-auto justify-center",children:[u.jsx(Ql,{className:"w-6 h-6 group-hover:scale-110 transition-transform duration-300"}),u.jsx("span",{children:"Our Story"})]})]})]}),u.jsx(v.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2,duration:.6},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer group",onClick:()=>r("about"),children:u.jsxs(v.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"flex flex-col items-center space-y-2",children:[u.jsx("span",{className:"text-brand-yellow text-sm font-medium opacity-80 group-hover:opacity-100 transition-opacity",children:"Scroll Down"}),u.jsx(v.div,{whileHover:{scale:1.2},className:"p-3 rounded-full border-2 border-brand-yellow/30 group-hover:border-brand-yellow/60 transition-colors",children:u.jsx(Gl,{className:"text-brand-yellow text-xl"})})]})})]})}function op(){const e=m.useRef(null),t=st(e,{once:!0,amount:.3});return u.jsx("section",{id:"about",ref:e,className:"py-20 bg-gradient-to-br from-brand-black via-brand-black-soft to-brand-black relative overflow-hidden",children:u.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center",children:[u.jsxs(v.div,{initial:{x:-50,opacity:0},animate:t?{x:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut"},children:[u.jsx("span",{className:"text-brand-yellow font-semibold text-base sm:text-lg uppercase tracking-wider",children:"Our Story"}),u.jsx("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-black text-white mt-4 mb-4 sm:mb-6",children:"Crafting Perfection Since Day One"}),u.jsx("p",{className:"text-gray-300 text-base sm:text-lg leading-relaxed mb-6 sm:mb-8",children:"Born in the heart of Lahore, BullBuster represents a revolution in fast food excellence. We combine traditional Pakistani hospitality with international culinary standards to create an unforgettable dining experience."}),u.jsxs("div",{className:"grid grid-cols-2 gap-8 mb-8",children:[u.jsxs(v.div,{initial:{scale:.8,opacity:0},animate:t?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.2},className:"text-center",children:[u.jsx("div",{className:"text-3xl font-black text-brand-yellow mb-2",children:"50K+"}),u.jsx("div",{className:"text-gray-300 font-medium",children:"Happy Customers"})]}),u.jsxs(v.div,{initial:{scale:.8,opacity:0},animate:t?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.4},className:"text-center",children:[u.jsx("div",{className:"text-3xl font-black text-brand-yellow mb-2",children:"24/7"}),u.jsx("div",{className:"text-gray-300 font-medium",children:"Service Available"})]})]}),u.jsx(v.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300",children:"Learn More About Us"})]}),u.jsxs(v.div,{initial:{x:50,opacity:0},animate:t?{x:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut",delay:.3},className:"relative",children:[u.jsx("img",{src:"https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",alt:"Luxury restaurant interior with warm ambiance",className:"rounded-2xl shadow-2xl w-full h-96 object-cover"}),u.jsx(v.div,{initial:{scale:0,rotate:-180},animate:t?{scale:1,rotate:0}:{},transition:{duration:.6,delay:.6},className:"absolute -top-8 -left-8 w-24 h-24 bg-brand-yellow rounded-full flex items-center justify-center shadow-lg",children:u.jsx(yi,{className:"text-brand-black text-2xl"})})]})]})})})}const ap=[{id:1,name:"Bull Signature Burger",description:"Double beef patty, special sauce, fresh vegetables, premium bun",price:89900,category:"Burgers",image:"https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:2,name:"Crispy Chicken Deluxe",description:"Tender chicken breast, crispy coating, mayo, lettuce",price:79900,category:"Burgers",image:"https://images.unsplash.com/photo-1553979459-d2229ba7433a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:3,name:"Loaded Bull Fries",description:"Crispy fries, melted cheese, jalapenos, special sauce",price:49900,category:"Sides",image:"https://images.unsplash.com/photo-1518013431117-eb1465fa5752?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:4,name:"Chocolate Thunder Shake",description:"Rich chocolate, vanilla ice cream, whipped cream, cherry",price:39900,category:"Drinks",image:"https://images.unsplash.com/photo-1541518763669-27fef04b14ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:5,name:"Spicy Wings Combo",description:"8 pieces of spicy buffalo wings with ranch dip",price:69900,category:"Appetizers",image:"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0},{id:6,name:"Classic Fish & Chips",description:"Beer-battered fish with golden fries and tartar sauce",price:99900,category:"Mains",image:"https://images.unsplash.com/photo-1544982503-9f984c14501a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",available:!0}];function lp(){const e=m.useRef(null),t=st(e,{once:!0,amount:.2}),[n,s]=m.useState("All"),r=ap,o=["All",...Array.from(new Set(r.map(a=>a.category)))],i=n==="All"?r:r.filter(a=>a.category===n),l=a=>`Rs. ${(a/100).toFixed(0)}`;return u.jsx("section",{id:"menu",ref:e,className:"py-20 bg-gradient-to-br from-brand-black via-brand-black-soft to-brand-black",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs(v.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.8,ease:"easeOut"},className:"text-center mb-16",children:[u.jsx(v.span,{initial:{scale:.8,opacity:0},animate:t?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.2},className:"inline-block text-brand-yellow font-bold text-lg uppercase tracking-wider bg-brand-yellow/10 px-6 py-2 rounded-full border border-brand-yellow/20",children:"Our Menu"}),u.jsxs(v.h2,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.3},className:"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-white mt-4 sm:mt-6 mb-4 sm:mb-6",children:[u.jsx("span",{className:"gradient-text",children:"Delicious"})," Selections"]}),u.jsxs(v.p,{initial:{y:20,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.4},className:"text-gray-300 text-lg md:text-xl max-w-3xl mx-auto leading-relaxed",children:["From signature burgers to crispy delights, every bite tells a story of quality and passion.",u.jsxs("span",{className:"text-brand-yellow font-semibold",children:[" ","Crafted with love, served with pride."]})]})]}),u.jsx(v.div,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.5},className:"flex justify-center mb-16",children:u.jsx("div",{className:"flex flex-wrap gap-3 bg-brand-black-soft/80 backdrop-blur-xl rounded-2xl p-3 shadow-2xl border border-brand-yellow/20 glow-effect",children:o.map((a,c)=>u.jsxs(v.button,{initial:{opacity:0,scale:.8},animate:t?{opacity:1,scale:1}:{},transition:{duration:.3,delay:.6+c*.1},whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},onClick:()=>s(a),className:`relative px-6 py-3 rounded-xl font-bold transition-all duration-300 ${n===a?"bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black shadow-lg":"hover:bg-brand-yellow/10 text-white hover:text-brand-yellow"}`,children:[a,n===a&&u.jsx(v.div,{layoutId:"activeFilter",className:"absolute inset-0 bg-gradient-to-r from-brand-yellow to-brand-yellow-light rounded-xl -z-10",initial:!1,transition:{type:"spring",stiffness:380,damping:30}})]},a))})}),u.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8",children:i.map((a,c)=>u.jsxs(v.div,{initial:{y:80,opacity:0,scale:.8},animate:t?{y:0,opacity:1,scale:1}:{},transition:{duration:.8,delay:.15*c,ease:"easeOut",type:"spring",stiffness:100},whileHover:{y:-15,scale:1.03,boxShadow:"0 30px 60px rgba(255, 193, 7, 0.2)",transition:{duration:.3}},whileTap:{scale:.98},className:"group bg-brand-black-soft/60 backdrop-blur-xl border border-brand-yellow/20 rounded-3xl shadow-xl overflow-hidden hover:border-brand-yellow/40 transition-all duration-500 hover-glow",children:[u.jsxs("div",{className:"relative overflow-hidden rounded-t-3xl",children:[u.jsx(v.img,{src:a.image,alt:a.name,className:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110",whileHover:{scale:1.1}}),u.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),!a.available&&u.jsx(v.div,{initial:{opacity:0},animate:{opacity:1},className:"absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-4xl mb-2",children:"😔"}),u.jsx("span",{className:"text-white font-bold text-lg",children:"Currently Unavailable"})]})}),a.available&&u.jsx(v.div,{initial:{opacity:0,y:20},whileHover:{opacity:1,y:0},className:"absolute top-4 right-4 bg-brand-yellow text-brand-black px-3 py-1 rounded-full text-sm font-bold",children:"Available"})]}),u.jsxs("div",{className:"p-4 sm:p-6",children:[u.jsx(v.h3,{className:"text-lg sm:text-xl font-black text-white mb-2 sm:mb-3 group-hover:text-brand-yellow transition-colors duration-300",children:a.name}),u.jsx("p",{className:"text-sm sm:text-base text-gray-300 mb-4 sm:mb-6 leading-relaxed",children:a.description}),u.jsxs("div",{className:"flex justify-between items-center",children:[u.jsx(v.span,{className:"text-xl sm:text-2xl lg:text-3xl font-black gradient-text",whileHover:{scale:1.1},children:l(a.price)}),u.jsxs(v.button,{whileHover:{scale:1.1,boxShadow:"0 10px 20px rgba(255, 193, 7, 0.3)"},whileTap:{scale:.9},disabled:!a.available,className:"bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-4 sm:px-6 py-2 sm:py-3 rounded-full font-bold hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1 sm:gap-2 text-sm sm:text-base",children:[u.jsx(ec,{className:"w-4 h-4 sm:w-5 sm:h-5"}),u.jsx("span",{children:"Add"})]})]})]})]},a.id))}),u.jsx(v.div,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.5},className:"text-center mt-12",children:u.jsx(v.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-8 py-4 rounded-full font-semibold hover:bg-brand-yellow-light transition-all duration-300",children:"View Full Menu"})})]})})}const Or=[{id:1,url:"https://images.unsplash.com/photo-1512152272829-e3139592d56f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Signature Bull Burger",category:"food",likes:234},{id:2,url:"https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Restaurant Interior",category:"ambiance",likes:189},{id:3,url:"https://images.unsplash.com/photo-1551782450-17144efb9c50?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Gourmet Presentation",category:"food",likes:312},{id:4,url:"https://images.unsplash.com/photo-1565299507177-b0ac66763828?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Chef's Special",category:"food",likes:267},{id:5,url:"https://images.unsplash.com/photo-1544148103-0773bf10d330?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Dining Experience",category:"ambiance",likes:156},{id:6,url:"https://images.unsplash.com/photo-1586190848861-99aa4a171e90?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600",alt:"Premium Burger",category:"food",likes:298}];function cp(){const e=m.useRef(null),t=st(e,{once:!0,amount:.2}),[n,s]=m.useState("all"),[r,o]=m.useState([]),i=n==="all"?Or:Or.filter(a=>a.category===n),l=a=>{o(c=>c.includes(a)?c.filter(d=>d!==a):[...c,a])};return u.jsx("section",{id:"gallery",ref:e,className:"py-20 bg-gradient-to-b from-brand-black to-brand-black-soft",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs(v.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-12",children:[u.jsxs(v.div,{initial:{scale:.8,opacity:0},animate:t?{scale:1,opacity:1}:{},transition:{duration:.5,delay:.2},className:"inline-flex items-center space-x-2 text-brand-yellow font-bold text-lg uppercase tracking-wider bg-brand-yellow/10 px-6 py-2 rounded-full border border-brand-yellow/20 mb-6",children:[u.jsx(gs,{className:"w-5 h-5"}),u.jsx("span",{children:"Gallery"})]}),u.jsxs(v.h2,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.3},className:"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-white mb-4",children:[u.jsx("span",{className:"gradient-text",children:"Visual"})," Feast"]}),u.jsx(v.p,{initial:{y:20,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.4},className:"text-base sm:text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Every dish is a masterpiece, every moment is memorable. Follow our culinary journey."})]}),u.jsx(v.div,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.5},className:"flex justify-center mb-12",children:u.jsx("div",{className:"flex flex-wrap gap-3 bg-brand-black-soft/80 backdrop-blur-xl rounded-2xl p-3 shadow-2xl border border-brand-yellow/20",children:[{id:"all",label:"All",icon:gs},{id:"food",label:"Food",icon:ys},{id:"ambiance",label:"Ambiance",icon:lt}].map((a,c)=>{const d=a.icon;return u.jsxs(v.button,{initial:{opacity:0,scale:.8},animate:t?{opacity:1,scale:1}:{},transition:{duration:.3,delay:.6+c*.1},whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},onClick:()=>s(a.id),className:`flex items-center space-x-2 px-4 sm:px-6 py-2 sm:py-3 rounded-xl font-bold transition-all duration-300 ${n===a.id?"bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black shadow-lg":"hover:bg-brand-yellow/10 text-white hover:text-brand-yellow"}`,children:[u.jsx(d,{className:"w-4 h-4"}),u.jsx("span",{className:"text-sm sm:text-base",children:a.label})]},a.id)})})}),u.jsx(v.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.8,delay:.7},className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:i.map((a,c)=>u.jsxs(v.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.8+c*.1},whileHover:{y:-8,scale:1.02},className:"group relative overflow-hidden rounded-2xl bg-brand-black-soft shadow-xl hover:shadow-2xl transition-all duration-500",children:[u.jsxs("div",{className:"aspect-square overflow-hidden",children:[u.jsx("img",{src:a.url,alt:a.alt,className:"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"}),u.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),u.jsxs("div",{className:"absolute inset-0 flex flex-col justify-end p-4 sm:p-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[u.jsx("h3",{className:"text-white font-bold text-lg mb-2",children:a.alt}),u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx(lt,{className:"w-4 h-4 text-brand-yellow"}),u.jsx("span",{className:"text-white text-sm",children:"@bullbuster"})]}),u.jsxs(v.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>l(a.id),className:"flex items-center space-x-1 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1",children:[u.jsx(ys,{className:`w-4 h-4 ${r.includes(a.id)?"text-red-500 fill-red-500":"text-white"}`}),u.jsx("span",{className:"text-white text-sm",children:r.includes(a.id)?a.likes+1:a.likes})]})]})]})]},a.id))}),u.jsx(v.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:1.2},className:"text-center mt-16",children:u.jsxs(v.button,{whileHover:{scale:1.05,boxShadow:"0 20px 40px rgba(255, 193, 7, 0.3)"},whileTap:{scale:.95},className:"inline-flex items-center space-x-3 bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-300",children:[u.jsx(lt,{className:"w-6 h-6"}),u.jsx("span",{children:"Follow @BullBuster"})]})})]})})}const qt=[{id:"confirmed",label:"Order Confirmed",description:"Your order has been received and confirmed",icon:Wl},{id:"preparing",label:"Preparing",description:"Our chefs are preparing your delicious meal",icon:xi},{id:"out_for_delivery",label:"Out for Delivery",description:"Your order is on its way to you",icon:Ul},{id:"delivered",label:"Delivered",description:"Order delivered successfully",icon:ql}],up={12345:{id:"12345",status:"preparing",items:["Bull Signature Burger","Loaded Bull Fries","Chocolate Thunder Shake"],total:219700,estimatedTime:"25-30 minutes",createdAt:new Date(Date.now()-15*60*1e3)},67890:{id:"67890",status:"out_for_delivery",items:["Crispy Chicken Deluxe","Spicy Wings Combo"],total:149800,estimatedTime:"10-15 minutes",createdAt:new Date(Date.now()-35*60*1e3)},11111:{id:"11111",status:"delivered",items:["Classic Fish & Chips"],total:99900,estimatedTime:"Delivered",createdAt:new Date(Date.now()-60*60*1e3)}};function dp(){const e=m.useRef(null),t=st(e,{once:!0,amount:.2}),[n,s]=m.useState(""),[r,o]=m.useState(null),[i,l]=m.useState(!1),[a,c]=m.useState(!1),d=()=>{n.trim()&&(l(!0),c(!1),setTimeout(()=>{const p=up[n.trim()];p?(o(p),c(!1)):(o(null),c(!0)),l(!1)},1e3))},h=p=>{if(!r)return"pending";const x=qt.findIndex(y=>y.id===r.status),g=qt.findIndex(y=>y.id===p);return g<x?"completed":g===x?"active":"pending"},f=p=>`Rs. ${(p/100).toFixed(0)}`;return u.jsx("section",{id:"track",ref:e,className:"py-20 bg-gradient-to-br from-brand-black via-brand-black-soft to-brand-black",children:u.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs(v.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-12",children:[u.jsx("span",{className:"text-brand-yellow font-semibold text-base sm:text-lg uppercase tracking-wider",children:"Order Tracking"}),u.jsxs("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-black text-white mt-4 mb-6",children:[u.jsx("span",{className:"gradient-text",children:"Track Your"})," Order"]}),u.jsx("p",{className:"text-gray-300 text-base sm:text-lg max-w-2xl mx-auto",children:"Enter your order number to track the status of your delicious meal."})]}),u.jsx(v.div,{initial:{y:30,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6,delay:.2},className:"bg-brand-black-soft/60 backdrop-blur-xl border border-brand-yellow/20 rounded-2xl p-6 sm:p-8 mb-8",children:u.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[u.jsx("input",{type:"text",placeholder:"Enter your order number (try: 12345, 67890, or 11111)",value:n,onChange:p=>s(p.target.value),onKeyPress:p=>p.key==="Enter"&&d(),className:"flex-1 bg-brand-black/50 border border-brand-yellow/30 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:border-brand-yellow focus:outline-none transition-colors"}),u.jsx(v.button,{onClick:d,disabled:i,whileHover:{scale:1.02},whileTap:{scale:.98},className:"bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black font-bold py-3 px-6 rounded-xl hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50",children:i?u.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-brand-black"}):u.jsxs(u.Fragment,{children:[u.jsx(nc,{className:"w-5 h-5"}),u.jsx("span",{children:"Track Order"})]})})]})}),a&&u.jsxs(v.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-red-500/10 border border-red-500/30 rounded-2xl p-6 text-center",children:[u.jsx("p",{className:"text-red-400 font-semibold",children:"Order not found"}),u.jsx("p",{className:"text-gray-300 text-sm mt-2",children:"Please check your order number and try again."})]}),r&&u.jsxs(v.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[u.jsxs("div",{className:"bg-brand-black-soft/60 backdrop-blur-xl border border-brand-yellow/20 rounded-2xl p-6 sm:p-8",children:[u.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6",children:[u.jsxs("div",{children:[u.jsxs("h3",{className:"text-xl font-bold text-white",children:["Order #",r.id]}),u.jsxs("p",{className:"text-gray-300",children:["Total: ",f(r.total)]})]}),u.jsxs("div",{className:"flex items-center space-x-2 text-brand-yellow mt-2 sm:mt-0",children:[u.jsx(Tn,{className:"w-5 h-5"}),u.jsx("span",{className:"font-semibold",children:r.estimatedTime})]})]}),u.jsxs("div",{className:"mb-6",children:[u.jsx("h4",{className:"text-white font-semibold mb-2",children:"Items:"}),u.jsx("ul",{className:"text-gray-300 space-y-1",children:r.items.map((p,x)=>u.jsxs("li",{children:["• ",p]},x))})]})]}),u.jsxs("div",{className:"bg-brand-black-soft/60 backdrop-blur-xl border border-brand-yellow/20 rounded-2xl p-6 sm:p-8",children:[u.jsx("h3",{className:"text-xl font-bold text-white mb-8",children:"Order Progress"}),u.jsx("div",{className:"space-y-6",children:qt.map((p,x)=>{const g=h(p.id),y=p.icon;return u.jsxs(v.div,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{delay:x*.1},className:"flex items-start space-x-4",children:[u.jsx("div",{className:`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${g==="completed"?"bg-brand-yellow border-brand-yellow":g==="active"?"bg-brand-yellow/20 border-brand-yellow animate-pulse":"bg-gray-600/20 border-gray-600"}`,children:u.jsx(y,{className:`w-6 h-6 ${g==="completed"?"text-brand-black":g==="active"?"text-brand-yellow":"text-gray-400"}`})}),u.jsxs("div",{className:"flex-1",children:[u.jsx("h4",{className:`font-semibold ${g==="completed"||g==="active"?"text-white":"text-gray-400"}`,children:p.label}),u.jsx("p",{className:`text-sm ${g==="completed"||g==="active"?"text-gray-300":"text-gray-500"}`,children:p.description})]})]},p.id)})})]})]}),!r&&!a&&u.jsxs(v.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{duration:.6,delay:.4},className:"bg-brand-yellow/10 border border-brand-yellow/30 rounded-2xl p-6 text-center",children:[u.jsx("p",{className:"text-brand-yellow font-semibold mb-2",children:"Demo Order Numbers"}),u.jsxs("p",{className:"text-gray-300 text-sm",children:["Try these order numbers: ",u.jsx("span",{className:"text-brand-yellow font-mono",children:"12345"}),", ",u.jsx("span",{className:"text-brand-yellow font-mono",children:"67890"}),", or ",u.jsx("span",{className:"text-brand-yellow font-mono",children:"11111"})]})]})]})})}function hp(){const e=m.useRef(null),t=st(e,{once:!0,amount:.2}),[n,s]=m.useState(!1),[r,o]=m.useState({name:"",email:"",subject:"",message:""}),i=a=>{a.preventDefault(),s(!0),o({name:"",email:"",subject:"",message:""}),setTimeout(()=>{s(!1)},3e3)},l=a=>{o(c=>({...c,[a.target.name]:a.target.value}))};return u.jsx("section",{id:"contact",ref:e,className:"py-20 bg-gradient-to-b from-brand-black to-brand-black-soft",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs(v.div,{initial:{y:50,opacity:0},animate:t?{y:0,opacity:1}:{},transition:{duration:.6},className:"text-center mb-16",children:[u.jsx("span",{className:"text-brand-yellow font-semibold text-base sm:text-lg uppercase tracking-wider",children:"Contact Us"}),u.jsxs("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-black text-white mt-4 mb-6",children:[u.jsx("span",{className:"gradient-text",children:"Get In"})," Touch"]}),u.jsx("p",{className:"text-gray-300 text-base sm:text-lg max-w-2xl mx-auto",children:"Have questions or want to make a reservation? We'd love to hear from you."})]}),u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16",children:[u.jsx(v.div,{initial:{x:-50,opacity:0},animate:t?{x:0,opacity:1}:{},transition:{duration:.8,delay:.2},className:"space-y-8",children:u.jsxs("div",{className:"bg-brand-black-soft/60 backdrop-blur-xl border border-brand-yellow/20 rounded-2xl p-6 sm:p-8",children:[u.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-white mb-6",children:"Visit Our Restaurant"}),u.jsxs("div",{className:"space-y-6",children:[u.jsxs(v.div,{whileHover:{x:5},className:"flex items-start space-x-4",children:[u.jsx("div",{className:"bg-brand-yellow/20 p-3 rounded-xl",children:u.jsx(Xl,{className:"w-6 h-6 text-brand-yellow"})}),u.jsxs("div",{children:[u.jsx("h4",{className:"font-semibold text-white mb-1",children:"Address"}),u.jsx("p",{className:"text-gray-300",children:"123 Food Street, Gulberg III, Lahore, Pakistan"})]})]}),u.jsxs(v.div,{whileHover:{x:5},className:"flex items-start space-x-4",children:[u.jsx("div",{className:"bg-brand-yellow/20 p-3 rounded-xl",children:u.jsx(Jl,{className:"w-6 h-6 text-brand-yellow"})}),u.jsxs("div",{children:[u.jsx("h4",{className:"font-semibold text-white mb-1",children:"Phone"}),u.jsx("p",{className:"text-gray-300",children:"+92 42 1234 5678"})]})]}),u.jsxs(v.div,{whileHover:{x:5},className:"flex items-start space-x-4",children:[u.jsx("div",{className:"bg-brand-yellow/20 p-3 rounded-xl",children:u.jsx(Yl,{className:"w-6 h-6 text-brand-yellow"})}),u.jsxs("div",{children:[u.jsx("h4",{className:"font-semibold text-white mb-1",children:"Email"}),u.jsx("p",{className:"text-gray-300",children:"<EMAIL>"})]})]}),u.jsxs(v.div,{whileHover:{x:5},className:"flex items-start space-x-4",children:[u.jsx("div",{className:"bg-brand-yellow/20 p-3 rounded-xl",children:u.jsx(Tn,{className:"w-6 h-6 text-brand-yellow"})}),u.jsxs("div",{children:[u.jsx("h4",{className:"font-semibold text-white mb-1",children:"Hours"}),u.jsx("p",{className:"text-gray-300",children:"Daily: 11:00 AM - 11:00 PM"})]})]})]})]})}),u.jsx(v.div,{initial:{x:50,opacity:0},animate:t?{x:0,opacity:1}:{},transition:{duration:.8,delay:.4},children:u.jsxs("div",{className:"bg-brand-black-soft/60 backdrop-blur-xl border border-brand-yellow/20 rounded-2xl p-6 sm:p-8",children:[u.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-white mb-6",children:"Send us a Message"}),n?u.jsx(v.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},className:"text-center py-8",children:u.jsxs("div",{className:"bg-green-500/20 text-green-400 p-4 rounded-xl mb-4",children:[u.jsx(Zt,{className:"w-8 h-8 mx-auto mb-2"}),u.jsx("p",{className:"font-semibold",children:"Message Sent!"}),u.jsx("p",{className:"text-sm",children:"Thank you for contacting us. We'll get back to you soon."})]})}):u.jsxs("form",{onSubmit:i,className:"space-y-6",children:[u.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[u.jsx("div",{children:u.jsx("input",{type:"text",name:"name",placeholder:"Your Name",value:r.name,onChange:l,required:!0,className:"w-full bg-brand-black/50 border border-brand-yellow/30 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:border-brand-yellow focus:outline-none transition-colors"})}),u.jsx("div",{children:u.jsx("input",{type:"email",name:"email",placeholder:"Your Email",value:r.email,onChange:l,required:!0,className:"w-full bg-brand-black/50 border border-brand-yellow/30 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:border-brand-yellow focus:outline-none transition-colors"})})]}),u.jsx("div",{children:u.jsx("input",{type:"text",name:"subject",placeholder:"Subject",value:r.subject,onChange:l,required:!0,className:"w-full bg-brand-black/50 border border-brand-yellow/30 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:border-brand-yellow focus:outline-none transition-colors"})}),u.jsx("div",{children:u.jsx("textarea",{name:"message",placeholder:"Your Message",value:r.message,onChange:l,required:!0,rows:5,className:"w-full bg-brand-black/50 border border-brand-yellow/30 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:border-brand-yellow focus:outline-none transition-colors resize-none"})}),u.jsxs(v.button,{type:"submit",whileHover:{scale:1.02,boxShadow:"0 10px 30px rgba(255, 193, 7, 0.3)"},whileTap:{scale:.98},className:"w-full bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black font-bold py-3 px-6 rounded-xl hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2",children:[u.jsx(Zt,{className:"w-5 h-5"}),u.jsx("span",{children:"Send Message"})]})]})]})})]})]})})}function fp(){const[e,t]=m.useState(""),n=r=>{r.preventDefault(),console.log("Newsletter subscription:",e),t("")},s=r=>{const o=document.getElementById(r);o&&o.scrollIntoView({behavior:"smooth",block:"start"})};return u.jsx("footer",{className:"bg-gradient-to-t from-brand-black to-brand-black-soft text-white py-12 sm:py-16",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-8 sm:mb-12",children:[u.jsxs(v.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6},viewport:{once:!0},children:[u.jsxs("div",{className:"flex items-center space-x-2 mb-6",children:[u.jsx(tc,{className:"text-3xl text-brand-yellow"}),u.jsx("span",{className:"text-2xl font-black text-white",children:"BullBuster"})]}),u.jsx("p",{className:"text-gray-400 mb-6",children:"Premium fast food experience in the heart of Lahore, serving quality meals with passion and excellence."}),u.jsxs("div",{className:"flex space-x-4",children:[u.jsx(v.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:u.jsx(Kl,{className:"w-5 h-5"})}),u.jsx(v.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:u.jsx(lt,{className:"w-5 h-5"})}),u.jsx(v.a,{href:"#",whileHover:{scale:1.1},whileTap:{scale:.9},className:"w-10 h-10 bg-brand-yellow rounded-full flex items-center justify-center text-brand-black hover:bg-brand-yellow-light transition-colors duration-300",children:u.jsx(rc,{className:"w-5 h-5"})})]})]}),u.jsxs(v.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.1},viewport:{once:!0},children:[u.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Quick Links"}),u.jsx("ul",{className:"space-y-3",children:["home","about","menu","gallery","contact"].map(r=>u.jsx("li",{children:u.jsx("button",{onClick:()=>s(r),className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300 capitalize",children:r==="home"?"Home":r==="about"?"About Us":r.charAt(0).toUpperCase()+r.slice(1)})},r))})]}),u.jsxs(v.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[u.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Services"}),u.jsxs("ul",{className:"space-y-3",children:[u.jsx("li",{children:u.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Dine In"})}),u.jsx("li",{children:u.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Takeaway"})}),u.jsx("li",{children:u.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Home Delivery"})}),u.jsx("li",{children:u.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Catering"})}),u.jsx("li",{children:u.jsx("a",{href:"#",className:"text-gray-400 hover:text-brand-yellow transition-colors duration-300",children:"Party Orders"})})]})]}),u.jsxs(v.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.3},viewport:{once:!0},children:[u.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Newsletter"}),u.jsx("p",{className:"text-gray-400 mb-4",children:"Subscribe to get special offers and updates."}),u.jsxs("form",{onSubmit:n,className:"flex",children:[u.jsx("input",{type:"email",placeholder:"Your email",value:e,onChange:r=>t(r.target.value),required:!0,className:"flex-1 bg-white/20 border border-white/30 rounded-l-full px-4 py-3 text-white placeholder-gray-300 focus:border-brand-yellow focus:outline-none transition-colors duration-300"}),u.jsx(v.button,{type:"submit",whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-brand-yellow text-brand-black px-6 py-3 rounded-r-full hover:bg-brand-yellow-light transition-colors duration-300",children:u.jsx(Zt,{className:"w-5 h-5"})})]})]})]}),u.jsx(v.div,{initial:{y:20,opacity:0},whileInView:{y:0,opacity:1},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"border-t border-gray-700 pt-8 text-center",children:u.jsx("p",{className:"text-gray-400",children:"© 2024 BullBsuter. All rights reserved. | Privacy Policy | Terms of Service"})})]})})}function pp(){return u.jsxs("div",{className:"min-h-screen bg-brand-black",children:[u.jsx(rp,{}),u.jsx(ip,{}),u.jsx(op,{}),u.jsx(lp,{}),u.jsx(cp,{}),u.jsx(dp,{}),u.jsx(hp,{}),u.jsx(fp,{})]})}function mp(){return u.jsxs(La,{children:[u.jsx(hs,{path:"/",component:pp}),u.jsx(hs,{component:oc})]})}function gp(){return u.jsx(Dl,{children:u.jsx(mp,{})})}_r(document.getElementById("root")).render(u.jsx(gp,{}));
