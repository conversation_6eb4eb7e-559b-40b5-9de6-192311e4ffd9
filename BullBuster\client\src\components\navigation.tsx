import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, X, ShoppingBag, Phone, MapPin } from "lucide-react";
import logoPath from "@assets/BullBuster_1752063257019.png";

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("home");

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);

      // Update active section based on scroll position
      const sections = ["home", "about", "menu", "gallery", "track", "contact"];
      const scrollPosition = window.scrollY + 100;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (
            scrollPosition >= offsetTop &&
            scrollPosition < offsetTop + offsetHeight
          ) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" });
      setIsMobileMenuOpen(false);
    }
  };

  const navItems = [
    { id: "home", label: "Home" },
    { id: "about", label: "About" },
    { id: "menu", label: "Menu" },
    { id: "gallery", label: "Gallery" },
    { id: "track", label: "Track Order" },
    { id: "contact", label: "Contact" },
  ];

  return (
    <>
      {/* Top Info Bar */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className={`fixed top-0 w-full z-50 bg-brand-black text-white py-2 text-sm transition-all duration-300 ${
          isScrolled ? "transform -translate-y-full" : ""
        }`}
      >
        <div className='max-w-7xl mx-auto px-6 flex justify-between items-center'>
          <div className='flex items-center space-x-6'>
            <div className='flex items-center space-x-2'>
              <Phone className='w-4 h-4 text-brand-yellow' />
              <span>+92 300 1234567</span>
            </div>
            <div className='flex items-center space-x-2'>
              <MapPin className='w-4 h-4 text-brand-yellow' />
              <span>Lahore, Pakistan</span>
            </div>
          </div>
          <div className='hidden md:block'>
            <span className='text-brand-yellow font-semibold'>
              Free Delivery on Orders Above Rs. 1000
            </span>
          </div>
        </div>
      </motion.div>

      {/* Main Navigation */}
      <motion.nav
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        className={`fixed w-full z-40 transition-all duration-500 ${
          isScrolled ? "top-0 glass-navbar shadow-2xl" : "top-10 bg-transparent"
        }`}
      >
        <div className='max-w-7xl mx-auto px-6 py-4'>
          <div className='flex items-center justify-between'>
            {/* Logo */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className='flex items-center space-x-3'
            >
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
                className='relative'
              >
                <img
                  src={logoPath}
                  alt='BullBuster Logo'
                  className='w-12 h-12 object-contain'
                />
                <div className='absolute inset-0 bg-brand-yellow/20 rounded-full blur-lg'></div>
              </motion.div>
              <div>
                <span className='text-2xl font-black gradient-text'>
                  BullBuster
                </span>
                <div className='text-xs text-brand-yellow font-medium'>
                  Premium Fast Food
                </div>
              </div>
            </motion.div>

            {/* Desktop Navigation */}
            <div className='hidden lg:flex items-center space-x-1'>
              {navItems.map((item) => (
                <motion.button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={`relative px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                    activeSection === item.id
                      ? "text-brand-yellow"
                      : "text-white hover:text-brand-yellow"
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {item.label}
                  {activeSection === item.id && (
                    <motion.div
                      layoutId='activeSection'
                      className='absolute bottom-0 left-0 right-0 h-0.5 bg-brand-yellow rounded-full'
                      initial={false}
                      transition={{
                        type: "spring",
                        stiffness: 380,
                        damping: 30,
                      }}
                    />
                  )}
                </motion.button>
              ))}
            </div>

            {/* Action Buttons */}
            <div className='flex items-center space-x-4'>
              <motion.button
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 0 25px rgba(255, 193, 7, 0.4)",
                }}
                whileTap={{ scale: 0.95 }}
                onClick={() => scrollToSection("menu")}
                className='hidden md:flex items-center space-x-2 bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black px-6 py-3 rounded-full font-bold hover:shadow-lg transition-all duration-300'
              >
                <ShoppingBag className='w-4 h-4' />
                <span>Order Now</span>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className='lg:hidden relative z-50 p-2 rounded-lg bg-brand-yellow/10 backdrop-blur-sm border border-brand-yellow/20'
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                <motion.div
                  animate={isMobileMenuOpen ? { rotate: 180 } : { rotate: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {isMobileMenuOpen ? (
                    <X className='w-6 h-6 text-brand-yellow' />
                  ) : (
                    <Menu className='w-6 h-6 text-brand-yellow' />
                  )}
                </motion.div>
              </motion.button>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className='fixed inset-0 z-30 lg:hidden'
          >
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className='absolute inset-0 bg-black/80 backdrop-blur-sm'
              onClick={() => setIsMobileMenuOpen(false)}
            />

            {/* Menu Content */}
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
              className='absolute right-0 top-0 h-full w-80 bg-brand-black-soft/95 backdrop-blur-xl border-l border-brand-yellow/20 shadow-2xl'
            >
              <div className='p-8 pt-24'>
                <div className='space-y-6'>
                  {navItems.map((item, index) => (
                    <motion.button
                      key={item.id}
                      initial={{ opacity: 0, x: 50 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => scrollToSection(item.id)}
                      className={`block w-full text-left py-3 px-4 rounded-lg font-medium transition-all duration-300 ${
                        activeSection === item.id
                          ? "bg-brand-yellow text-brand-black"
                          : "text-white hover:bg-brand-yellow/10 hover:text-brand-yellow"
                      }`}
                    >
                      {item.label}
                    </motion.button>
                  ))}

                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    onClick={() => scrollToSection("menu")}
                    className='w-full bg-gradient-to-r from-brand-yellow to-brand-yellow-light text-brand-black py-4 rounded-xl font-bold mt-8 hover:shadow-lg transition-all duration-300'
                  >
                    Order Now
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
