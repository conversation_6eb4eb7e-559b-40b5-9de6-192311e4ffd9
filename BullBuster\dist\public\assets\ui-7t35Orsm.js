import{r as u,R as te,a as He,b as yn}from"./vendor-CX2mysxk.js";var Et={exports:{}},Re={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vn=u,xn=Symbol.for("react.element"),En=Symbol.for("react.fragment"),bn=Object.prototype.hasOwnProperty,Cn=vn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Pn={key:!0,ref:!0,__self:!0,__source:!0};function bt(e,t,o){var n,r={},s=null,i=null;o!==void 0&&(s=""+o),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(n in t)bn.call(t,n)&&!Pn.hasOwnProperty(n)&&(r[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)r[n]===void 0&&(r[n]=t[n]);return{$$typeof:xn,type:e,key:s,ref:i,props:r,_owner:Cn.current}}Re.Fragment=En;Re.jsx=bt;Re.jsxs=bt;Et.exports=Re;var T=Et.exports;function $(e,t,{checkForDefaultPrevented:o=!0}={}){return function(r){if(e==null||e(r),o===!1||!r.defaultPrevented)return t==null?void 0:t(r)}}function lt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Ct(...e){return t=>{let o=!1;const n=e.map(r=>{const s=lt(r,t);return!o&&typeof s=="function"&&(o=!0),s});if(o)return()=>{for(let r=0;r<n.length;r++){const s=n[r];typeof s=="function"?s():lt(e[r],null)}}}}function q(...e){return u.useCallback(Ct(...e),e)}function ze(e,t=[]){let o=[];function n(s,i){const a=u.createContext(i),l=o.length;o=[...o,i];const c=d=>{var y;const{scope:g,children:m,...w}=d,h=((y=g==null?void 0:g[e])==null?void 0:y[l])||a,p=u.useMemo(()=>w,Object.values(w));return T.jsx(h.Provider,{value:p,children:m})};c.displayName=s+"Provider";function f(d,g){var h;const m=((h=g==null?void 0:g[e])==null?void 0:h[l])||a,w=u.useContext(m);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[c,f]}const r=()=>{const s=o.map(i=>u.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return u.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return r.scopeName=e,[n,Rn(r,...t)]}function Rn(...e){const t=e[0];if(e.length===1)return t;const o=()=>{const n=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(s){const i=n.reduce((a,{useScope:l,scopeName:c})=>{const d=l(s)[`__scope${c}`];return{...a,...d}},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return o.scopeName=t.scopeName,o}function Ie(e){const t=Tn(e),o=u.forwardRef((n,r)=>{const{children:s,...i}=n,a=u.Children.toArray(s),l=a.find(An);if(l){const c=l.props.children,f=a.map(d=>d===l?u.Children.count(c)>1?u.Children.only(null):u.isValidElement(c)?c.props.children:null:d);return T.jsx(t,{...i,ref:r,children:u.isValidElement(c)?u.cloneElement(c,void 0,f):null})}return T.jsx(t,{...i,ref:r,children:s})});return o.displayName=`${e}.Slot`,o}function Tn(e){const t=u.forwardRef((o,n)=>{const{children:r,...s}=o;if(u.isValidElement(r)){const i=On(r),a=Sn(s,r.props);return r.type!==u.Fragment&&(a.ref=n?Ct(n,i):i),u.cloneElement(r,a)}return u.Children.count(r)>1?u.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Pt=Symbol("radix.slottable");function yr(e){const t=({children:o})=>T.jsx(T.Fragment,{children:o});return t.displayName=`${e}.Slottable`,t.__radixId=Pt,t}function An(e){return u.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Pt}function Sn(e,t){const o={...t};for(const n in t){const r=e[n],s=t[n];/^on[A-Z]/.test(n)?r&&s?o[n]=(...a)=>{s(...a),r(...a)}:r&&(o[n]=r):n==="style"?o[n]={...r,...s}:n==="className"&&(o[n]=[r,s].filter(Boolean).join(" "))}return{...e,...o}}function On(e){var n,r;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,o=t&&"isReactWarning"in t&&t.isReactWarning;return o?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,o=t&&"isReactWarning"in t&&t.isReactWarning,o?e.props.ref:e.props.ref||e.ref)}function Nn(e){const t=e+"CollectionProvider",[o,n]=ze(t),[r,s]=o(t,{collectionRef:{current:null},itemMap:new Map}),i=h=>{const{scope:p,children:y}=h,v=te.useRef(null),x=te.useRef(new Map).current;return T.jsx(r,{scope:p,itemMap:x,collectionRef:v,children:y})};i.displayName=t;const a=e+"CollectionSlot",l=Ie(a),c=te.forwardRef((h,p)=>{const{scope:y,children:v}=h,x=s(a,y),E=q(p,x.collectionRef);return T.jsx(l,{ref:E,children:v})});c.displayName=a;const f=e+"CollectionItemSlot",d="data-radix-collection-item",g=Ie(f),m=te.forwardRef((h,p)=>{const{scope:y,children:v,...x}=h,E=te.useRef(null),b=q(p,E),R=s(f,y);return te.useEffect(()=>(R.itemMap.set(E,{ref:E,...x}),()=>void R.itemMap.delete(E))),T.jsx(g,{[d]:"",ref:b,children:v})});m.displayName=f;function w(h){const p=s(e+"CollectionConsumer",h);return te.useCallback(()=>{const v=p.collectionRef.current;if(!v)return[];const x=Array.from(v.querySelectorAll(`[${d}]`));return Array.from(p.itemMap.values()).sort((R,P)=>x.indexOf(R.ref.current)-x.indexOf(P.ref.current))},[p.collectionRef,p.itemMap])}return[{Provider:i,Slot:c,ItemSlot:m},w,n]}var Dn=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],B=Dn.reduce((e,t)=>{const o=Ie(`Primitive.${t}`),n=u.forwardRef((r,s)=>{const{asChild:i,...a}=r,l=i?o:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),T.jsx(l,{...a,ref:s})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Rt(e,t){e&&He.flushSync(()=>e.dispatchEvent(t))}function K(e){const t=u.useRef(e);return u.useEffect(()=>{t.current=e}),u.useMemo(()=>(...o)=>{var n;return(n=t.current)==null?void 0:n.call(t,...o)},[])}function Ln(e,t=globalThis==null?void 0:globalThis.document){const o=K(e);u.useEffect(()=>{const n=r=>{r.key==="Escape"&&o(r)};return t.addEventListener("keydown",n,{capture:!0}),()=>t.removeEventListener("keydown",n,{capture:!0})},[o,t])}var _n="DismissableLayer",Fe="dismissableLayer.update",Mn="dismissableLayer.pointerDownOutside",In="dismissableLayer.focusOutside",ut,Tt=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),At=u.forwardRef((e,t)=>{const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:n,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...l}=e,c=u.useContext(Tt),[f,d]=u.useState(null),g=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=u.useState({}),w=q(t,P=>d(P)),h=Array.from(c.layers),[p]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),y=h.indexOf(p),v=f?h.indexOf(f):-1,x=c.layersWithOutsidePointerEventsDisabled.size>0,E=v>=y,b=kn(P=>{const S=P.target,L=[...c.branches].some(N=>N.contains(S));!E||L||(r==null||r(P),i==null||i(P),P.defaultPrevented||a==null||a())},g),R=jn(P=>{const S=P.target;[...c.branches].some(N=>N.contains(S))||(s==null||s(P),i==null||i(P),P.defaultPrevented||a==null||a())},g);return Ln(P=>{v===c.layers.size-1&&(n==null||n(P),!P.defaultPrevented&&a&&(P.preventDefault(),a()))},g),u.useEffect(()=>{if(f)return o&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(ut=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(f)),c.layers.add(f),ft(),()=>{o&&c.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=ut)}},[f,g,o,c]),u.useEffect(()=>()=>{f&&(c.layers.delete(f),c.layersWithOutsidePointerEventsDisabled.delete(f),ft())},[f,c]),u.useEffect(()=>{const P=()=>m({});return document.addEventListener(Fe,P),()=>document.removeEventListener(Fe,P)},[]),T.jsx(B.div,{...l,ref:w,style:{pointerEvents:x?E?"auto":"none":void 0,...e.style},onFocusCapture:$(e.onFocusCapture,R.onFocusCapture),onBlurCapture:$(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:$(e.onPointerDownCapture,b.onPointerDownCapture)})});At.displayName=_n;var Fn="DismissableLayerBranch",St=u.forwardRef((e,t)=>{const o=u.useContext(Tt),n=u.useRef(null),r=q(t,n);return u.useEffect(()=>{const s=n.current;if(s)return o.branches.add(s),()=>{o.branches.delete(s)}},[o.branches]),T.jsx(B.div,{...e,ref:r})});St.displayName=Fn;function kn(e,t=globalThis==null?void 0:globalThis.document){const o=K(e),n=u.useRef(!1),r=u.useRef(()=>{});return u.useEffect(()=>{const s=a=>{if(a.target&&!n.current){let l=function(){Ot(Mn,o,c,{discrete:!0})};const c={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",r.current),r.current=l,t.addEventListener("click",r.current,{once:!0})):l()}else t.removeEventListener("click",r.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",r.current)}},[t,o]),{onPointerDownCapture:()=>n.current=!0}}function jn(e,t=globalThis==null?void 0:globalThis.document){const o=K(e),n=u.useRef(!1);return u.useEffect(()=>{const r=s=>{s.target&&!n.current&&Ot(In,o,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",r),()=>t.removeEventListener("focusin",r)},[t,o]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}function ft(){const e=new CustomEvent(Fe);document.dispatchEvent(e)}function Ot(e,t,o,{discrete:n}){const r=o.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:o});t&&r.addEventListener(e,t,{once:!0}),n?Rt(r,s):r.dispatchEvent(s)}var $n=At,Wn=St,ne=globalThis!=null&&globalThis.document?u.useLayoutEffect:()=>{},Bn="Portal",Nt=u.forwardRef((e,t)=>{var a;const{container:o,...n}=e,[r,s]=u.useState(!1);ne(()=>s(!0),[]);const i=o||r&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?yn.createPortal(T.jsx(B.div,{...n,ref:t}),i):null});Nt.displayName=Bn;function Vn(e,t){return u.useReducer((o,n)=>t[o][n]??o,e)}var Dt=e=>{const{present:t,children:o}=e,n=Hn(t),r=typeof o=="function"?o({present:n.isPresent}):u.Children.only(o),s=q(n.ref,zn(r));return typeof o=="function"||n.isPresent?u.cloneElement(r,{ref:s}):null};Dt.displayName="Presence";function Hn(e){const[t,o]=u.useState(),n=u.useRef({}),r=u.useRef(e),s=u.useRef("none"),i=e?"mounted":"unmounted",[a,l]=Vn(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return u.useEffect(()=>{const c=we(n.current);s.current=a==="mounted"?c:"none"},[a]),ne(()=>{const c=n.current,f=r.current;if(f!==e){const g=s.current,m=we(c);e?l("MOUNT"):m==="none"||(c==null?void 0:c.display)==="none"?l("UNMOUNT"):l(f&&g!==m?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,l]),ne(()=>{if(t){let c;const f=t.ownerDocument.defaultView??window,d=m=>{const h=we(n.current).includes(m.animationName);if(m.target===t&&h&&(l("ANIMATION_END"),!r.current)){const p=t.style.animationFillMode;t.style.animationFillMode="forwards",c=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=p)})}},g=m=>{m.target===t&&(s.current=we(n.current))};return t.addEventListener("animationstart",g),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(c),t.removeEventListener("animationstart",g),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:u.useCallback(c=>{c&&(n.current=getComputedStyle(c)),o(c)},[])}}function we(e){return(e==null?void 0:e.animationName)||"none"}function zn(e){var n,r;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,o=t&&"isReactWarning"in t&&t.isReactWarning;return o?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,o=t&&"isReactWarning"in t&&t.isReactWarning,o?e.props.ref:e.props.ref||e.ref)}function Un({prop:e,defaultProp:t,onChange:o=()=>{}}){const[n,r]=Kn({defaultProp:t,onChange:o}),s=e!==void 0,i=s?e:n,a=K(o),l=u.useCallback(c=>{if(s){const d=typeof c=="function"?c(e):c;d!==e&&a(d)}else r(c)},[s,e,r,a]);return[i,l]}function Kn({defaultProp:e,onChange:t}){const o=u.useState(e),[n]=o,r=u.useRef(n),s=K(t);return u.useEffect(()=>{r.current!==n&&(s(n),r.current=n)},[n,r,s]),o}var Yn="VisuallyHidden",Te=u.forwardRef((e,t)=>T.jsx(B.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Te.displayName=Yn;var vr=Te,Ue="ToastProvider",[Ke,Xn,qn]=Nn("Toast"),[Lt,xr]=ze("Toast",[qn]),[Zn,Ae]=Lt(Ue),_t=e=>{const{__scopeToast:t,label:o="Notification",duration:n=5e3,swipeDirection:r="right",swipeThreshold:s=50,children:i}=e,[a,l]=u.useState(null),[c,f]=u.useState(0),d=u.useRef(!1),g=u.useRef(!1);return o.trim()||console.error(`Invalid prop \`label\` supplied to \`${Ue}\`. Expected non-empty \`string\`.`),T.jsx(Ke.Provider,{scope:t,children:T.jsx(Zn,{scope:t,label:o,duration:n,swipeDirection:r,swipeThreshold:s,toastCount:c,viewport:a,onViewportChange:l,onToastAdd:u.useCallback(()=>f(m=>m+1),[]),onToastRemove:u.useCallback(()=>f(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:g,children:i})})};_t.displayName=Ue;var Mt="ToastViewport",Jn=["F8"],ke="toast.viewportPause",je="toast.viewportResume",It=u.forwardRef((e,t)=>{const{__scopeToast:o,hotkey:n=Jn,label:r="Notifications ({hotkey})",...s}=e,i=Ae(Mt,o),a=Xn(o),l=u.useRef(null),c=u.useRef(null),f=u.useRef(null),d=u.useRef(null),g=q(t,d,i.onViewportChange),m=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=i.toastCount>0;u.useEffect(()=>{const p=y=>{var x;n.length!==0&&n.every(E=>y[E]||y.code===E)&&((x=d.current)==null||x.focus())};return document.addEventListener("keydown",p),()=>document.removeEventListener("keydown",p)},[n]),u.useEffect(()=>{const p=l.current,y=d.current;if(w&&p&&y){const v=()=>{if(!i.isClosePausedRef.current){const R=new CustomEvent(ke);y.dispatchEvent(R),i.isClosePausedRef.current=!0}},x=()=>{if(i.isClosePausedRef.current){const R=new CustomEvent(je);y.dispatchEvent(R),i.isClosePausedRef.current=!1}},E=R=>{!p.contains(R.relatedTarget)&&x()},b=()=>{p.contains(document.activeElement)||x()};return p.addEventListener("focusin",v),p.addEventListener("focusout",E),p.addEventListener("pointermove",v),p.addEventListener("pointerleave",b),window.addEventListener("blur",v),window.addEventListener("focus",x),()=>{p.removeEventListener("focusin",v),p.removeEventListener("focusout",E),p.removeEventListener("pointermove",v),p.removeEventListener("pointerleave",b),window.removeEventListener("blur",v),window.removeEventListener("focus",x)}}},[w,i.isClosePausedRef]);const h=u.useCallback(({tabbingDirection:p})=>{const v=a().map(x=>{const E=x.ref.current,b=[E,...uo(E)];return p==="forwards"?b:b.reverse()});return(p==="forwards"?v.reverse():v).flat()},[a]);return u.useEffect(()=>{const p=d.current;if(p){const y=v=>{var b,R,P;const x=v.altKey||v.ctrlKey||v.metaKey;if(v.key==="Tab"&&!x){const S=document.activeElement,L=v.shiftKey;if(v.target===p&&L){(b=c.current)==null||b.focus();return}const I=h({tabbingDirection:L?"backwards":"forwards"}),_=I.findIndex(C=>C===S);Le(I.slice(_+1))?v.preventDefault():L?(R=c.current)==null||R.focus():(P=f.current)==null||P.focus()}};return p.addEventListener("keydown",y),()=>p.removeEventListener("keydown",y)}},[a,h]),T.jsxs(Wn,{ref:l,role:"region","aria-label":r.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&T.jsx($e,{ref:c,onFocusFromOutsideViewport:()=>{const p=h({tabbingDirection:"forwards"});Le(p)}}),T.jsx(Ke.Slot,{scope:o,children:T.jsx(B.ol,{tabIndex:-1,...s,ref:g})}),w&&T.jsx($e,{ref:f,onFocusFromOutsideViewport:()=>{const p=h({tabbingDirection:"backwards"});Le(p)}})]})});It.displayName=Mt;var Ft="ToastFocusProxy",$e=u.forwardRef((e,t)=>{const{__scopeToast:o,onFocusFromOutsideViewport:n,...r}=e,s=Ae(Ft,o);return T.jsx(Te,{"aria-hidden":!0,tabIndex:0,...r,ref:t,style:{position:"fixed"},onFocus:i=>{var c;const a=i.relatedTarget;!((c=s.viewport)!=null&&c.contains(a))&&n()}})});$e.displayName=Ft;var Se="Toast",Gn="toast.swipeStart",Qn="toast.swipeMove",eo="toast.swipeCancel",to="toast.swipeEnd",kt=u.forwardRef((e,t)=>{const{forceMount:o,open:n,defaultOpen:r,onOpenChange:s,...i}=e,[a=!0,l]=Un({prop:n,defaultProp:r,onChange:s});return T.jsx(Dt,{present:o||a,children:T.jsx(ro,{open:a,...i,ref:t,onClose:()=>l(!1),onPause:K(e.onPause),onResume:K(e.onResume),onSwipeStart:$(e.onSwipeStart,c=>{c.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:$(e.onSwipeMove,c=>{const{x:f,y:d}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","move"),c.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:$(e.onSwipeCancel,c=>{c.currentTarget.setAttribute("data-swipe","cancel"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:$(e.onSwipeEnd,c=>{const{x:f,y:d}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","end"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),l(!1)})})})});kt.displayName=Se;var[no,oo]=Lt(Se,{onClose(){}}),ro=u.forwardRef((e,t)=>{const{__scopeToast:o,type:n="foreground",duration:r,open:s,onClose:i,onEscapeKeyDown:a,onPause:l,onResume:c,onSwipeStart:f,onSwipeMove:d,onSwipeCancel:g,onSwipeEnd:m,...w}=e,h=Ae(Se,o),[p,y]=u.useState(null),v=q(t,C=>y(C)),x=u.useRef(null),E=u.useRef(null),b=r||h.duration,R=u.useRef(0),P=u.useRef(b),S=u.useRef(0),{onToastAdd:L,onToastRemove:N}=h,F=K(()=>{var D;(p==null?void 0:p.contains(document.activeElement))&&((D=h.viewport)==null||D.focus()),i()}),I=u.useCallback(C=>{!C||C===1/0||(window.clearTimeout(S.current),R.current=new Date().getTime(),S.current=window.setTimeout(F,C))},[F]);u.useEffect(()=>{const C=h.viewport;if(C){const D=()=>{I(P.current),c==null||c()},O=()=>{const M=new Date().getTime()-R.current;P.current=P.current-M,window.clearTimeout(S.current),l==null||l()};return C.addEventListener(ke,O),C.addEventListener(je,D),()=>{C.removeEventListener(ke,O),C.removeEventListener(je,D)}}},[h.viewport,b,l,c,I]),u.useEffect(()=>{s&&!h.isClosePausedRef.current&&I(b)},[s,b,h.isClosePausedRef,I]),u.useEffect(()=>(L(),()=>N()),[L,N]);const _=u.useMemo(()=>p?zt(p):null,[p]);return h.viewport?T.jsxs(T.Fragment,{children:[_&&T.jsx(so,{__scopeToast:o,role:"status","aria-live":n==="foreground"?"assertive":"polite","aria-atomic":!0,children:_}),T.jsx(no,{scope:o,onClose:F,children:He.createPortal(T.jsx(Ke.ItemSlot,{scope:o,children:T.jsx($n,{asChild:!0,onEscapeKeyDown:$(a,()=>{h.isFocusedToastEscapeKeyDownRef.current||F(),h.isFocusedToastEscapeKeyDownRef.current=!1}),children:T.jsx(B.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":h.swipeDirection,...w,ref:v,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:$(e.onKeyDown,C=>{C.key==="Escape"&&(a==null||a(C.nativeEvent),C.nativeEvent.defaultPrevented||(h.isFocusedToastEscapeKeyDownRef.current=!0,F()))}),onPointerDown:$(e.onPointerDown,C=>{C.button===0&&(x.current={x:C.clientX,y:C.clientY})}),onPointerMove:$(e.onPointerMove,C=>{if(!x.current)return;const D=C.clientX-x.current.x,O=C.clientY-x.current.y,M=!!E.current,A=["left","right"].includes(h.swipeDirection),k=["left","up"].includes(h.swipeDirection)?Math.min:Math.max,V=A?k(0,D):0,re=A?0:k(0,O),ue=C.pointerType==="touch"?10:2,se={x:V,y:re},he={originalEvent:C,delta:se};M?(E.current=se,ye(Qn,d,he,{discrete:!1})):dt(se,h.swipeDirection,ue)?(E.current=se,ye(Gn,f,he,{discrete:!1}),C.target.setPointerCapture(C.pointerId)):(Math.abs(D)>ue||Math.abs(O)>ue)&&(x.current=null)}),onPointerUp:$(e.onPointerUp,C=>{const D=E.current,O=C.target;if(O.hasPointerCapture(C.pointerId)&&O.releasePointerCapture(C.pointerId),E.current=null,x.current=null,D){const M=C.currentTarget,A={originalEvent:C,delta:D};dt(D,h.swipeDirection,h.swipeThreshold)?ye(to,m,A,{discrete:!0}):ye(eo,g,A,{discrete:!0}),M.addEventListener("click",k=>k.preventDefault(),{once:!0})}})})})}),h.viewport)})]}):null}),so=e=>{const{__scopeToast:t,children:o,...n}=e,r=Ae(Se,t),[s,i]=u.useState(!1),[a,l]=u.useState(!1);return ao(()=>i(!0)),u.useEffect(()=>{const c=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(c)},[]),a?null:T.jsx(Nt,{asChild:!0,children:T.jsx(Te,{...n,children:s&&T.jsxs(T.Fragment,{children:[r.label," ",o]})})})},io="ToastTitle",jt=u.forwardRef((e,t)=>{const{__scopeToast:o,...n}=e;return T.jsx(B.div,{...n,ref:t})});jt.displayName=io;var co="ToastDescription",$t=u.forwardRef((e,t)=>{const{__scopeToast:o,...n}=e;return T.jsx(B.div,{...n,ref:t})});$t.displayName=co;var Wt="ToastAction",Bt=u.forwardRef((e,t)=>{const{altText:o,...n}=e;return o.trim()?T.jsx(Ht,{altText:o,asChild:!0,children:T.jsx(Ye,{...n,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Wt}\`. Expected non-empty \`string\`.`),null)});Bt.displayName=Wt;var Vt="ToastClose",Ye=u.forwardRef((e,t)=>{const{__scopeToast:o,...n}=e,r=oo(Vt,o);return T.jsx(Ht,{asChild:!0,children:T.jsx(B.button,{type:"button",...n,ref:t,onClick:$(e.onClick,r.onClose)})})});Ye.displayName=Vt;var Ht=u.forwardRef((e,t)=>{const{__scopeToast:o,altText:n,...r}=e;return T.jsx(B.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...r,ref:t})});function zt(e){const t=[];return Array.from(e.childNodes).forEach(n=>{if(n.nodeType===n.TEXT_NODE&&n.textContent&&t.push(n.textContent),lo(n)){const r=n.ariaHidden||n.hidden||n.style.display==="none",s=n.dataset.radixToastAnnounceExclude==="";if(!r)if(s){const i=n.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...zt(n))}}),t}function ye(e,t,o,{discrete:n}){const r=o.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:o});t&&r.addEventListener(e,t,{once:!0}),n?Rt(r,s):r.dispatchEvent(s)}var dt=(e,t,o=0)=>{const n=Math.abs(e.x),r=Math.abs(e.y),s=n>r;return t==="left"||t==="right"?s&&n>o:!s&&r>o};function ao(e=()=>{}){const t=K(e);ne(()=>{let o=0,n=0;return o=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(o),window.cancelAnimationFrame(n)}},[t])}function lo(e){return e.nodeType===e.ELEMENT_NODE}function uo(e){const t=[],o=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const r=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||r?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;o.nextNode();)t.push(o.currentNode);return t}function Le(e){const t=document.activeElement;return e.some(o=>o===t?!0:(o.focus(),document.activeElement!==t))}var Er=_t,br=It,Cr=kt,Pr=jt,Rr=$t,Tr=Bt,Ar=Ye;const fo=["top","right","bottom","left"],G=Math.min,j=Math.max,Ee=Math.round,ve=Math.floor,U=e=>({x:e,y:e}),po={left:"right",right:"left",bottom:"top",top:"bottom"},mo={start:"end",end:"start"};function We(e,t,o){return j(e,G(t,o))}function Z(e,t){return typeof e=="function"?e(t):e}function J(e){return e.split("-")[0]}function ae(e){return e.split("-")[1]}function Xe(e){return e==="x"?"y":"x"}function qe(e){return e==="y"?"height":"width"}function Q(e){return["top","bottom"].includes(J(e))?"y":"x"}function Ze(e){return Xe(Q(e))}function ho(e,t,o){o===void 0&&(o=!1);const n=ae(e),r=Ze(e),s=qe(r);let i=r==="x"?n===(o?"end":"start")?"right":"left":n==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=be(i)),[i,be(i)]}function go(e){const t=be(e);return[Be(e),t,Be(t)]}function Be(e){return e.replace(/start|end/g,t=>mo[t])}function wo(e,t,o){const n=["left","right"],r=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return o?t?r:n:t?n:r;case"left":case"right":return t?s:i;default:return[]}}function yo(e,t,o,n){const r=ae(e);let s=wo(J(e),o==="start",n);return r&&(s=s.map(i=>i+"-"+r),t&&(s=s.concat(s.map(Be)))),s}function be(e){return e.replace(/left|right|bottom|top/g,t=>po[t])}function vo(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ut(e){return typeof e!="number"?vo(e):{top:e,right:e,bottom:e,left:e}}function Ce(e){const{x:t,y:o,width:n,height:r}=e;return{width:n,height:r,top:o,left:t,right:t+n,bottom:o+r,x:t,y:o}}function pt(e,t,o){let{reference:n,floating:r}=e;const s=Q(t),i=Ze(t),a=qe(i),l=J(t),c=s==="y",f=n.x+n.width/2-r.width/2,d=n.y+n.height/2-r.height/2,g=n[a]/2-r[a]/2;let m;switch(l){case"top":m={x:f,y:n.y-r.height};break;case"bottom":m={x:f,y:n.y+n.height};break;case"right":m={x:n.x+n.width,y:d};break;case"left":m={x:n.x-r.width,y:d};break;default:m={x:n.x,y:n.y}}switch(ae(t)){case"start":m[i]-=g*(o&&c?-1:1);break;case"end":m[i]+=g*(o&&c?-1:1);break}return m}const xo=async(e,t,o)=>{const{placement:n="bottom",strategy:r="absolute",middleware:s=[],platform:i}=o,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:f,y:d}=pt(c,n,l),g=n,m={},w=0;for(let h=0;h<a.length;h++){const{name:p,fn:y}=a[h],{x:v,y:x,data:E,reset:b}=await y({x:f,y:d,initialPlacement:n,placement:g,strategy:r,middlewareData:m,rects:c,platform:i,elements:{reference:e,floating:t}});f=v??f,d=x??d,m={...m,[p]:{...m[p],...E}},b&&w<=50&&(w++,typeof b=="object"&&(b.placement&&(g=b.placement),b.rects&&(c=b.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:r}):b.rects),{x:f,y:d}=pt(c,g,l)),h=-1)}return{x:f,y:d,placement:g,strategy:r,middlewareData:m}};async function de(e,t){var o;t===void 0&&(t={});const{x:n,y:r,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:g=!1,padding:m=0}=Z(t,e),w=Ut(m),p=a[g?d==="floating"?"reference":"floating":d],y=Ce(await s.getClippingRect({element:(o=await(s.isElement==null?void 0:s.isElement(p)))==null||o?p:p.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:c,rootBoundary:f,strategy:l})),v=d==="floating"?{x:n,y:r,width:i.floating.width,height:i.floating.height}:i.reference,x=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),E=await(s.isElement==null?void 0:s.isElement(x))?await(s.getScale==null?void 0:s.getScale(x))||{x:1,y:1}:{x:1,y:1},b=Ce(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:x,strategy:l}):v);return{top:(y.top-b.top+w.top)/E.y,bottom:(b.bottom-y.bottom+w.bottom)/E.y,left:(y.left-b.left+w.left)/E.x,right:(b.right-y.right+w.right)/E.x}}const Eo=e=>({name:"arrow",options:e,async fn(t){const{x:o,y:n,placement:r,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:c,padding:f=0}=Z(e,t)||{};if(c==null)return{};const d=Ut(f),g={x:o,y:n},m=Ze(r),w=qe(m),h=await i.getDimensions(c),p=m==="y",y=p?"top":"left",v=p?"bottom":"right",x=p?"clientHeight":"clientWidth",E=s.reference[w]+s.reference[m]-g[m]-s.floating[w],b=g[m]-s.reference[m],R=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let P=R?R[x]:0;(!P||!await(i.isElement==null?void 0:i.isElement(R)))&&(P=a.floating[x]||s.floating[w]);const S=E/2-b/2,L=P/2-h[w]/2-1,N=G(d[y],L),F=G(d[v],L),I=N,_=P-h[w]-F,C=P/2-h[w]/2+S,D=We(I,C,_),O=!l.arrow&&ae(r)!=null&&C!==D&&s.reference[w]/2-(C<I?N:F)-h[w]/2<0,M=O?C<I?C-I:C-_:0;return{[m]:g[m]+M,data:{[m]:D,centerOffset:C-D-M,...O&&{alignmentOffset:M}},reset:O}}}),bo=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var o,n;const{placement:r,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:c}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:g,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:h=!0,...p}=Z(e,t);if((o=s.arrow)!=null&&o.alignmentOffset)return{};const y=J(r),v=Q(a),x=J(a)===a,E=await(l.isRTL==null?void 0:l.isRTL(c.floating)),b=g||(x||!h?[be(a)]:go(a)),R=w!=="none";!g&&R&&b.push(...yo(a,h,w,E));const P=[a,...b],S=await de(t,p),L=[];let N=((n=s.flip)==null?void 0:n.overflows)||[];if(f&&L.push(S[y]),d){const C=ho(r,i,E);L.push(S[C[0]],S[C[1]])}if(N=[...N,{placement:r,overflows:L}],!L.every(C=>C<=0)){var F,I;const C=(((F=s.flip)==null?void 0:F.index)||0)+1,D=P[C];if(D)return{data:{index:C,overflows:N},reset:{placement:D}};let O=(I=N.filter(M=>M.overflows[0]<=0).sort((M,A)=>M.overflows[1]-A.overflows[1])[0])==null?void 0:I.placement;if(!O)switch(m){case"bestFit":{var _;const M=(_=N.filter(A=>{if(R){const k=Q(A.placement);return k===v||k==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(k=>k>0).reduce((k,V)=>k+V,0)]).sort((A,k)=>A[1]-k[1])[0])==null?void 0:_[0];M&&(O=M);break}case"initialPlacement":O=a;break}if(r!==O)return{reset:{placement:O}}}return{}}}};function mt(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ht(e){return fo.some(t=>e[t]>=0)}const Co=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:o}=t,{strategy:n="referenceHidden",...r}=Z(e,t);switch(n){case"referenceHidden":{const s=await de(t,{...r,elementContext:"reference"}),i=mt(s,o.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:ht(i)}}}case"escaped":{const s=await de(t,{...r,altBoundary:!0}),i=mt(s,o.floating);return{data:{escapedOffsets:i,escaped:ht(i)}}}default:return{}}}}};async function Po(e,t){const{placement:o,platform:n,elements:r}=e,s=await(n.isRTL==null?void 0:n.isRTL(r.floating)),i=J(o),a=ae(o),l=Q(o)==="y",c=["left","top"].includes(i)?-1:1,f=s&&l?-1:1,d=Z(t,e);let{mainAxis:g,crossAxis:m,alignmentAxis:w}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&typeof w=="number"&&(m=a==="end"?w*-1:w),l?{x:m*f,y:g*c}:{x:g*c,y:m*f}}const Ro=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var o,n;const{x:r,y:s,placement:i,middlewareData:a}=t,l=await Po(t,e);return i===((o=a.offset)==null?void 0:o.placement)&&(n=a.arrow)!=null&&n.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:i}}}}},To=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:o,y:n,placement:r}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:p=>{let{x:y,y:v}=p;return{x:y,y:v}}},...l}=Z(e,t),c={x:o,y:n},f=await de(t,l),d=Q(J(r)),g=Xe(d);let m=c[g],w=c[d];if(s){const p=g==="y"?"top":"left",y=g==="y"?"bottom":"right",v=m+f[p],x=m-f[y];m=We(v,m,x)}if(i){const p=d==="y"?"top":"left",y=d==="y"?"bottom":"right",v=w+f[p],x=w-f[y];w=We(v,w,x)}const h=a.fn({...t,[g]:m,[d]:w});return{...h,data:{x:h.x-o,y:h.y-n,enabled:{[g]:s,[d]:i}}}}}},Ao=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:o,y:n,placement:r,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=Z(e,t),f={x:o,y:n},d=Q(r),g=Xe(d);let m=f[g],w=f[d];const h=Z(a,t),p=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(l){const x=g==="y"?"height":"width",E=s.reference[g]-s.floating[x]+p.mainAxis,b=s.reference[g]+s.reference[x]-p.mainAxis;m<E?m=E:m>b&&(m=b)}if(c){var y,v;const x=g==="y"?"width":"height",E=["top","left"].includes(J(r)),b=s.reference[d]-s.floating[x]+(E&&((y=i.offset)==null?void 0:y[d])||0)+(E?0:p.crossAxis),R=s.reference[d]+s.reference[x]+(E?0:((v=i.offset)==null?void 0:v[d])||0)-(E?p.crossAxis:0);w<b?w=b:w>R&&(w=R)}return{[g]:m,[d]:w}}}},So=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var o,n;const{placement:r,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...c}=Z(e,t),f=await de(t,c),d=J(r),g=ae(r),m=Q(r)==="y",{width:w,height:h}=s.floating;let p,y;d==="top"||d==="bottom"?(p=d,y=g===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(y=d,p=g==="end"?"top":"bottom");const v=h-f.top-f.bottom,x=w-f.left-f.right,E=G(h-f[p],v),b=G(w-f[y],x),R=!t.middlewareData.shift;let P=E,S=b;if((o=t.middlewareData.shift)!=null&&o.enabled.x&&(S=x),(n=t.middlewareData.shift)!=null&&n.enabled.y&&(P=v),R&&!g){const N=j(f.left,0),F=j(f.right,0),I=j(f.top,0),_=j(f.bottom,0);m?S=w-2*(N!==0||F!==0?N+F:j(f.left,f.right)):P=h-2*(I!==0||_!==0?I+_:j(f.top,f.bottom))}await l({...t,availableWidth:S,availableHeight:P});const L=await i.getDimensions(a.floating);return w!==L.width||h!==L.height?{reset:{rects:!0}}:{}}}};function Oe(){return typeof window<"u"}function le(e){return Kt(e)?(e.nodeName||"").toLowerCase():"#document"}function W(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function X(e){var t;return(t=(Kt(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Kt(e){return Oe()?e instanceof Node||e instanceof W(e).Node:!1}function H(e){return Oe()?e instanceof Element||e instanceof W(e).Element:!1}function Y(e){return Oe()?e instanceof HTMLElement||e instanceof W(e).HTMLElement:!1}function gt(e){return!Oe()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof W(e).ShadowRoot}function me(e){const{overflow:t,overflowX:o,overflowY:n,display:r}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+o)&&!["inline","contents"].includes(r)}function Oo(e){return["table","td","th"].includes(le(e))}function Ne(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Je(e){const t=Ge(),o=H(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(n=>o[n]?o[n]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!t&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!t&&(o.filter?o.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(n=>(o.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(o.contain||"").includes(n))}function No(e){let t=ee(e);for(;Y(t)&&!ce(t);){if(Je(t))return t;if(Ne(t))return null;t=ee(t)}return null}function Ge(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ce(e){return["html","body","#document"].includes(le(e))}function z(e){return W(e).getComputedStyle(e)}function De(e){return H(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if(le(e)==="html")return e;const t=e.assignedSlot||e.parentNode||gt(e)&&e.host||X(e);return gt(t)?t.host:t}function Yt(e){const t=ee(e);return ce(t)?e.ownerDocument?e.ownerDocument.body:e.body:Y(t)&&me(t)?t:Yt(t)}function pe(e,t,o){var n;t===void 0&&(t=[]),o===void 0&&(o=!0);const r=Yt(e),s=r===((n=e.ownerDocument)==null?void 0:n.body),i=W(r);if(s){const a=Ve(i);return t.concat(i,i.visualViewport||[],me(r)?r:[],a&&o?pe(a):[])}return t.concat(r,pe(r,[],o))}function Ve(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Xt(e){const t=z(e);let o=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const r=Y(e),s=r?e.offsetWidth:o,i=r?e.offsetHeight:n,a=Ee(o)!==s||Ee(n)!==i;return a&&(o=s,n=i),{width:o,height:n,$:a}}function Qe(e){return H(e)?e:e.contextElement}function ie(e){const t=Qe(e);if(!Y(t))return U(1);const o=t.getBoundingClientRect(),{width:n,height:r,$:s}=Xt(t);let i=(s?Ee(o.width):o.width)/n,a=(s?Ee(o.height):o.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Do=U(0);function qt(e){const t=W(e);return!Ge()||!t.visualViewport?Do:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Lo(e,t,o){return t===void 0&&(t=!1),!o||t&&o!==W(e)?!1:t}function oe(e,t,o,n){t===void 0&&(t=!1),o===void 0&&(o=!1);const r=e.getBoundingClientRect(),s=Qe(e);let i=U(1);t&&(n?H(n)&&(i=ie(n)):i=ie(e));const a=Lo(s,o,n)?qt(s):U(0);let l=(r.left+a.x)/i.x,c=(r.top+a.y)/i.y,f=r.width/i.x,d=r.height/i.y;if(s){const g=W(s),m=n&&H(n)?W(n):n;let w=g,h=Ve(w);for(;h&&n&&m!==w;){const p=ie(h),y=h.getBoundingClientRect(),v=z(h),x=y.left+(h.clientLeft+parseFloat(v.paddingLeft))*p.x,E=y.top+(h.clientTop+parseFloat(v.paddingTop))*p.y;l*=p.x,c*=p.y,f*=p.x,d*=p.y,l+=x,c+=E,w=W(h),h=Ve(w)}}return Ce({width:f,height:d,x:l,y:c})}function et(e,t){const o=De(e).scrollLeft;return t?t.left+o:oe(X(e)).left+o}function Zt(e,t,o){o===void 0&&(o=!1);const n=e.getBoundingClientRect(),r=n.left+t.scrollLeft-(o?0:et(e,n)),s=n.top+t.scrollTop;return{x:r,y:s}}function _o(e){let{elements:t,rect:o,offsetParent:n,strategy:r}=e;const s=r==="fixed",i=X(n),a=t?Ne(t.floating):!1;if(n===i||a&&s)return o;let l={scrollLeft:0,scrollTop:0},c=U(1);const f=U(0),d=Y(n);if((d||!d&&!s)&&((le(n)!=="body"||me(i))&&(l=De(n)),Y(n))){const m=oe(n);c=ie(n),f.x=m.x+n.clientLeft,f.y=m.y+n.clientTop}const g=i&&!d&&!s?Zt(i,l,!0):U(0);return{width:o.width*c.x,height:o.height*c.y,x:o.x*c.x-l.scrollLeft*c.x+f.x+g.x,y:o.y*c.y-l.scrollTop*c.y+f.y+g.y}}function Mo(e){return Array.from(e.getClientRects())}function Io(e){const t=X(e),o=De(e),n=e.ownerDocument.body,r=j(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),s=j(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let i=-o.scrollLeft+et(e);const a=-o.scrollTop;return z(n).direction==="rtl"&&(i+=j(t.clientWidth,n.clientWidth)-r),{width:r,height:s,x:i,y:a}}function Fo(e,t){const o=W(e),n=X(e),r=o.visualViewport;let s=n.clientWidth,i=n.clientHeight,a=0,l=0;if(r){s=r.width,i=r.height;const c=Ge();(!c||c&&t==="fixed")&&(a=r.offsetLeft,l=r.offsetTop)}return{width:s,height:i,x:a,y:l}}function ko(e,t){const o=oe(e,!0,t==="fixed"),n=o.top+e.clientTop,r=o.left+e.clientLeft,s=Y(e)?ie(e):U(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=r*s.x,c=n*s.y;return{width:i,height:a,x:l,y:c}}function wt(e,t,o){let n;if(t==="viewport")n=Fo(e,o);else if(t==="document")n=Io(X(e));else if(H(t))n=ko(t,o);else{const r=qt(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return Ce(n)}function Jt(e,t){const o=ee(e);return o===t||!H(o)||ce(o)?!1:z(o).position==="fixed"||Jt(o,t)}function jo(e,t){const o=t.get(e);if(o)return o;let n=pe(e,[],!1).filter(a=>H(a)&&le(a)!=="body"),r=null;const s=z(e).position==="fixed";let i=s?ee(e):e;for(;H(i)&&!ce(i);){const a=z(i),l=Je(i);!l&&a.position==="fixed"&&(r=null),(s?!l&&!r:!l&&a.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||me(i)&&!l&&Jt(e,i))?n=n.filter(f=>f!==i):r=a,i=ee(i)}return t.set(e,n),n}function $o(e){let{element:t,boundary:o,rootBoundary:n,strategy:r}=e;const i=[...o==="clippingAncestors"?Ne(t)?[]:jo(t,this._c):[].concat(o),n],a=i[0],l=i.reduce((c,f)=>{const d=wt(t,f,r);return c.top=j(d.top,c.top),c.right=G(d.right,c.right),c.bottom=G(d.bottom,c.bottom),c.left=j(d.left,c.left),c},wt(t,a,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Wo(e){const{width:t,height:o}=Xt(e);return{width:t,height:o}}function Bo(e,t,o){const n=Y(t),r=X(t),s=o==="fixed",i=oe(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=U(0);if(n||!n&&!s)if((le(t)!=="body"||me(r))&&(a=De(t)),n){const g=oe(t,!0,s,t);l.x=g.x+t.clientLeft,l.y=g.y+t.clientTop}else r&&(l.x=et(r));const c=r&&!n&&!s?Zt(r,a):U(0),f=i.left+a.scrollLeft-l.x-c.x,d=i.top+a.scrollTop-l.y-c.y;return{x:f,y:d,width:i.width,height:i.height}}function _e(e){return z(e).position==="static"}function yt(e,t){if(!Y(e)||z(e).position==="fixed")return null;if(t)return t(e);let o=e.offsetParent;return X(e)===o&&(o=o.ownerDocument.body),o}function Gt(e,t){const o=W(e);if(Ne(e))return o;if(!Y(e)){let r=ee(e);for(;r&&!ce(r);){if(H(r)&&!_e(r))return r;r=ee(r)}return o}let n=yt(e,t);for(;n&&Oo(n)&&_e(n);)n=yt(n,t);return n&&ce(n)&&_e(n)&&!Je(n)?o:n||No(e)||o}const Vo=async function(e){const t=this.getOffsetParent||Gt,o=this.getDimensions,n=await o(e.floating);return{reference:Bo(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function Ho(e){return z(e).direction==="rtl"}const zo={convertOffsetParentRelativeRectToViewportRelativeRect:_o,getDocumentElement:X,getClippingRect:$o,getOffsetParent:Gt,getElementRects:Vo,getClientRects:Mo,getDimensions:Wo,getScale:ie,isElement:H,isRTL:Ho};function Qt(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Uo(e,t){let o=null,n;const r=X(e);function s(){var a;clearTimeout(n),(a=o)==null||a.disconnect(),o=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const c=e.getBoundingClientRect(),{left:f,top:d,width:g,height:m}=c;if(a||t(),!g||!m)return;const w=ve(d),h=ve(r.clientWidth-(f+g)),p=ve(r.clientHeight-(d+m)),y=ve(f),x={rootMargin:-w+"px "+-h+"px "+-p+"px "+-y+"px",threshold:j(0,G(1,l))||1};let E=!0;function b(R){const P=R[0].intersectionRatio;if(P!==l){if(!E)return i();P?i(!1,P):n=setTimeout(()=>{i(!1,1e-7)},1e3)}P===1&&!Qt(c,e.getBoundingClientRect())&&i(),E=!1}try{o=new IntersectionObserver(b,{...x,root:r.ownerDocument})}catch{o=new IntersectionObserver(b,x)}o.observe(e)}return i(!0),s}function Ko(e,t,o,n){n===void 0&&(n={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=n,c=Qe(e),f=r||s?[...c?pe(c):[],...pe(t)]:[];f.forEach(y=>{r&&y.addEventListener("scroll",o,{passive:!0}),s&&y.addEventListener("resize",o)});const d=c&&a?Uo(c,o):null;let g=-1,m=null;i&&(m=new ResizeObserver(y=>{let[v]=y;v&&v.target===c&&m&&(m.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var x;(x=m)==null||x.observe(t)})),o()}),c&&!l&&m.observe(c),m.observe(t));let w,h=l?oe(e):null;l&&p();function p(){const y=oe(e);h&&!Qt(h,y)&&o(),h=y,w=requestAnimationFrame(p)}return o(),()=>{var y;f.forEach(v=>{r&&v.removeEventListener("scroll",o),s&&v.removeEventListener("resize",o)}),d==null||d(),(y=m)==null||y.disconnect(),m=null,l&&cancelAnimationFrame(w)}}const Yo=Ro,Xo=To,qo=bo,Zo=So,Jo=Co,vt=Eo,Go=Ao,Qo=(e,t,o)=>{const n=new Map,r={platform:zo,...o},s={...r.platform,_c:n};return xo(e,t,{...r,platform:s})};var xe=typeof document<"u"?u.useLayoutEffect:u.useEffect;function Pe(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let o,n,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(o=e.length,o!==t.length)return!1;for(n=o;n--!==0;)if(!Pe(e[n],t[n]))return!1;return!0}if(r=Object.keys(e),o=r.length,o!==Object.keys(t).length)return!1;for(n=o;n--!==0;)if(!{}.hasOwnProperty.call(t,r[n]))return!1;for(n=o;n--!==0;){const s=r[n];if(!(s==="_owner"&&e.$$typeof)&&!Pe(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function en(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function xt(e,t){const o=en(e);return Math.round(t*o)/o}function Me(e){const t=u.useRef(e);return xe(()=>{t.current=e}),t}function er(e){e===void 0&&(e={});const{placement:t="bottom",strategy:o="absolute",middleware:n=[],platform:r,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:c}=e,[f,d]=u.useState({x:0,y:0,strategy:o,placement:t,middlewareData:{},isPositioned:!1}),[g,m]=u.useState(n);Pe(g,n)||m(n);const[w,h]=u.useState(null),[p,y]=u.useState(null),v=u.useCallback(A=>{A!==R.current&&(R.current=A,h(A))},[]),x=u.useCallback(A=>{A!==P.current&&(P.current=A,y(A))},[]),E=s||w,b=i||p,R=u.useRef(null),P=u.useRef(null),S=u.useRef(f),L=l!=null,N=Me(l),F=Me(r),I=Me(c),_=u.useCallback(()=>{if(!R.current||!P.current)return;const A={placement:t,strategy:o,middleware:g};F.current&&(A.platform=F.current),Qo(R.current,P.current,A).then(k=>{const V={...k,isPositioned:I.current!==!1};C.current&&!Pe(S.current,V)&&(S.current=V,He.flushSync(()=>{d(V)}))})},[g,t,o,F,I]);xe(()=>{c===!1&&S.current.isPositioned&&(S.current.isPositioned=!1,d(A=>({...A,isPositioned:!1})))},[c]);const C=u.useRef(!1);xe(()=>(C.current=!0,()=>{C.current=!1}),[]),xe(()=>{if(E&&(R.current=E),b&&(P.current=b),E&&b){if(N.current)return N.current(E,b,_);_()}},[E,b,_,N,L]);const D=u.useMemo(()=>({reference:R,floating:P,setReference:v,setFloating:x}),[v,x]),O=u.useMemo(()=>({reference:E,floating:b}),[E,b]),M=u.useMemo(()=>{const A={position:o,left:0,top:0};if(!O.floating)return A;const k=xt(O.floating,f.x),V=xt(O.floating,f.y);return a?{...A,transform:"translate("+k+"px, "+V+"px)",...en(O.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:k,top:V}},[o,a,O.floating,f.x,f.y]);return u.useMemo(()=>({...f,update:_,refs:D,elements:O,floatingStyles:M}),[f,_,D,O,M])}const tr=e=>{function t(o){return{}.hasOwnProperty.call(o,"current")}return{name:"arrow",options:e,fn(o){const{element:n,padding:r}=typeof e=="function"?e(o):e;return n&&t(n)?n.current!=null?vt({element:n.current,padding:r}).fn(o):{}:n?vt({element:n,padding:r}).fn(o):{}}}},nr=(e,t)=>({...Yo(e),options:[e,t]}),or=(e,t)=>({...Xo(e),options:[e,t]}),rr=(e,t)=>({...Go(e),options:[e,t]}),sr=(e,t)=>({...qo(e),options:[e,t]}),ir=(e,t)=>({...Zo(e),options:[e,t]}),cr=(e,t)=>({...Jo(e),options:[e,t]}),ar=(e,t)=>({...tr(e),options:[e,t]});var lr="Arrow",tn=u.forwardRef((e,t)=>{const{children:o,width:n=10,height:r=5,...s}=e;return T.jsx(B.svg,{...s,ref:t,width:n,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?o:T.jsx("polygon",{points:"0,0 30,0 15,10"})})});tn.displayName=lr;var ur=tn;function fr(e){const[t,o]=u.useState(void 0);return ne(()=>{if(e){o({width:e.offsetWidth,height:e.offsetHeight});const n=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const s=r[0];let i,a;if("borderBoxSize"in s){const l=s.borderBoxSize,c=Array.isArray(l)?l[0]:l;i=c.inlineSize,a=c.blockSize}else i=e.offsetWidth,a=e.offsetHeight;o({width:i,height:a})});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}else o(void 0)},[e]),t}var nn="Popper",[on,Sr]=ze(nn),[Or,rn]=on(nn),sn="PopperAnchor",cn=u.forwardRef((e,t)=>{const{__scopePopper:o,virtualRef:n,...r}=e,s=rn(sn,o),i=u.useRef(null),a=q(t,i);return u.useEffect(()=>{s.onAnchorChange((n==null?void 0:n.current)||i.current)}),n?null:T.jsx(B.div,{...r,ref:a})});cn.displayName=sn;var tt="PopperContent",[dr,pr]=on(tt),an=u.forwardRef((e,t)=>{var nt,ot,rt,st,it,ct;const{__scopePopper:o,side:n="bottom",sideOffset:r=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:c=[],collisionPadding:f=0,sticky:d="partial",hideWhenDetached:g=!1,updatePositionStrategy:m="optimized",onPlaced:w,...h}=e,p=rn(tt,o),[y,v]=u.useState(null),x=q(t,fe=>v(fe)),[E,b]=u.useState(null),R=fr(E),P=(R==null?void 0:R.width)??0,S=(R==null?void 0:R.height)??0,L=n+(s!=="center"?"-"+s:""),N=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},F=Array.isArray(c)?c:[c],I=F.length>0,_={padding:N,boundary:F.filter(hr),altBoundary:I},{refs:C,floatingStyles:D,placement:O,isPositioned:M,middlewareData:A}=er({strategy:"fixed",placement:L,whileElementsMounted:(...fe)=>Ko(...fe,{animationFrame:m==="always"}),elements:{reference:p.anchor},middleware:[nr({mainAxis:r+S,alignmentAxis:i}),l&&or({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?rr():void 0,..._}),l&&sr({..._}),ir({..._,apply:({elements:fe,rects:at,availableWidth:mn,availableHeight:hn})=>{const{width:gn,height:wn}=at.reference,ge=fe.floating.style;ge.setProperty("--radix-popper-available-width",`${mn}px`),ge.setProperty("--radix-popper-available-height",`${hn}px`),ge.setProperty("--radix-popper-anchor-width",`${gn}px`),ge.setProperty("--radix-popper-anchor-height",`${wn}px`)}}),E&&ar({element:E,padding:a}),gr({arrowWidth:P,arrowHeight:S}),g&&cr({strategy:"referenceHidden",..._})]}),[k,V]=fn(O),re=K(w);ne(()=>{M&&(re==null||re())},[M,re]);const ue=(nt=A.arrow)==null?void 0:nt.x,se=(ot=A.arrow)==null?void 0:ot.y,he=((rt=A.arrow)==null?void 0:rt.centerOffset)!==0,[dn,pn]=u.useState();return ne(()=>{y&&pn(window.getComputedStyle(y).zIndex)},[y]),T.jsx("div",{ref:C.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:M?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:dn,"--radix-popper-transform-origin":[(st=A.transformOrigin)==null?void 0:st.x,(it=A.transformOrigin)==null?void 0:it.y].join(" "),...((ct=A.hide)==null?void 0:ct.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:T.jsx(dr,{scope:o,placedSide:k,onArrowChange:b,arrowX:ue,arrowY:se,shouldHideArrow:he,children:T.jsx(B.div,{"data-side":k,"data-align":V,...h,ref:x,style:{...h.style,animation:M?void 0:"none"}})})})});an.displayName=tt;var ln="PopperArrow",mr={top:"bottom",right:"left",bottom:"top",left:"right"},un=u.forwardRef(function(t,o){const{__scopePopper:n,...r}=t,s=pr(ln,n),i=mr[s.placedSide];return T.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:T.jsx(ur,{...r,ref:o,style:{...r.style,display:"block"}})})});un.displayName=ln;function hr(e){return e!==null}var gr=e=>({name:"transformOrigin",options:e,fn(t){var p,y,v;const{placement:o,rects:n,middlewareData:r}=t,i=((p=r.arrow)==null?void 0:p.centerOffset)!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,f]=fn(o),d={start:"0%",center:"50%",end:"100%"}[f],g=(((y=r.arrow)==null?void 0:y.x)??0)+a/2,m=(((v=r.arrow)==null?void 0:v.y)??0)+l/2;let w="",h="";return c==="bottom"?(w=i?d:`${g}px`,h=`${-l}px`):c==="top"?(w=i?d:`${g}px`,h=`${n.floating.height+l}px`):c==="right"?(w=`${-l}px`,h=i?d:`${m}px`):c==="left"&&(w=`${n.floating.width+l}px`,h=i?d:`${m}px`),{data:{x:w,y:h}}}});function fn(e){const[t,o="center"]=e.split("-");return[t,o]}var Nr=cn,Dr=an,Lr=un;export{Tr as A,Ar as C,Rr as D,Er as P,Cr as R,Pr as T,br as V,Sr as a,Nr as b,ze as c,B as d,$ as e,Dt as f,At as g,Dr as h,vr as i,T as j,Lr as k,yr as l,q as u};
