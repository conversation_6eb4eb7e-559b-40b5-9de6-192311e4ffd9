// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";

// shared/schema.ts
import { pgTable, text, serial, integer, boolean, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
var users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull()
});
var menuItems = pgTable("menu_items", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  price: integer("price").notNull(),
  // in cents
  category: text("category").notNull(),
  image: text("image").notNull(),
  available: boolean("available").notNull().default(true)
});
var orders = pgTable("orders", {
  id: serial("id").primary<PERSON>ey(),
  orderNumber: text("order_number").notNull().unique(),
  customerName: text("customer_name").notNull(),
  customerEmail: text("customer_email").notNull(),
  customerPhone: text("customer_phone").notNull(),
  status: text("status").notNull().default("confirmed"),
  // confirmed, preparing, out_for_delivery, delivered
  total: integer("total").notNull(),
  // in cents
  items: text("items").notNull(),
  // JSON string of order items
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow()
});
var contacts = pgTable("contacts", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  subject: text("subject").notNull(),
  message: text("message").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow()
});
var insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true
});
var insertMenuItemSchema = createInsertSchema(menuItems).omit({
  id: true
});
var insertOrderSchema = createInsertSchema(orders).omit({
  id: true,
  orderNumber: true,
  createdAt: true,
  updatedAt: true
});
var insertContactSchema = createInsertSchema(contacts).omit({
  id: true,
  createdAt: true
});

// server/storage.ts
import { drizzle } from "drizzle-orm/neon-http";
import { neon } from "@neondatabase/serverless";
import { eq } from "drizzle-orm";
var MemStorage = class {
  users;
  menuItems;
  orders;
  contacts;
  currentUserId;
  currentMenuItemId;
  currentContactId;
  constructor() {
    this.users = /* @__PURE__ */ new Map();
    this.menuItems = /* @__PURE__ */ new Map();
    this.orders = /* @__PURE__ */ new Map();
    this.contacts = /* @__PURE__ */ new Map();
    this.currentUserId = 1;
    this.currentMenuItemId = 1;
    this.currentContactId = 1;
    this.seedMenuItems();
  }
  seedMenuItems() {
    const items = [
      {
        id: 1,
        name: "Bull Signature Burger",
        description: "Double beef patty, special sauce, fresh vegetables, premium bun",
        price: 89900,
        // Rs. 899
        category: "Burgers",
        image: "https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        available: true
      },
      {
        id: 2,
        name: "Crispy Chicken Deluxe",
        description: "Tender chicken breast, crispy coating, mayo, lettuce",
        price: 79900,
        // Rs. 799
        category: "Burgers",
        image: "https://images.unsplash.com/photo-1606755962773-d324e2dea5f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        available: true
      },
      {
        id: 3,
        name: "Loaded Bull Fries",
        description: "Crispy fries, melted cheese, jalapenos, special sauce",
        price: 49900,
        // Rs. 499
        category: "Sides",
        image: "https://images.unsplash.com/photo-1518013431117-eb1465fa5752?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        available: true
      },
      {
        id: 4,
        name: "Chocolate Thunder Shake",
        description: "Rich chocolate, vanilla ice cream, whipped cream, cherry",
        price: 39900,
        // Rs. 399
        category: "Drinks",
        image: "https://images.unsplash.com/photo-1541518763669-27fef04b14ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        available: true
      },
      {
        id: 5,
        name: "Grilled Chicken Wrap",
        description: "Grilled chicken, fresh veggies, special sauce, soft tortilla",
        price: 64900,
        // Rs. 649
        category: "Wraps",
        image: "https://images.unsplash.com/photo-1565299507177-b0ac66763828?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        available: true
      },
      {
        id: 6,
        name: "Buffalo Wings",
        description: "Spicy buffalo wings, blue cheese dip, celery sticks",
        price: 74900,
        // Rs. 749
        category: "Sides",
        image: "https://images.unsplash.com/photo-1608039755401-742074f0548d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
        available: true
      }
    ];
    items.forEach((item) => {
      this.menuItems.set(item.id, item);
      this.currentMenuItemId = Math.max(this.currentMenuItemId, item.id + 1);
    });
  }
  async getUser(id) {
    return this.users.get(id);
  }
  async getUserByUsername(username) {
    return Array.from(this.users.values()).find(
      (user) => user.username === username
    );
  }
  async createUser(insertUser) {
    const id = this.currentUserId++;
    const user = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }
  async getMenuItems() {
    return Array.from(this.menuItems.values());
  }
  async getMenuItemsByCategory(category) {
    return Array.from(this.menuItems.values()).filter(
      (item) => item.category === category
    );
  }
  async createMenuItem(insertItem) {
    const id = this.currentMenuItemId++;
    const item = { ...insertItem, id, available: insertItem.available ?? true };
    this.menuItems.set(id, item);
    return item;
  }
  async getOrder(orderNumber) {
    return this.orders.get(orderNumber);
  }
  async createOrder(insertOrder) {
    const orderNumber = `BB${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
    const now = /* @__PURE__ */ new Date();
    const order = {
      ...insertOrder,
      id: Date.now(),
      orderNumber,
      status: insertOrder.status ?? "confirmed",
      createdAt: now,
      updatedAt: now
    };
    this.orders.set(orderNumber, order);
    return order;
  }
  async updateOrderStatus(orderNumber, status) {
    const order = this.orders.get(orderNumber);
    if (order) {
      order.status = status;
      order.updatedAt = /* @__PURE__ */ new Date();
      this.orders.set(orderNumber, order);
    }
    return order;
  }
  async createContact(insertContact) {
    const id = this.currentContactId++;
    const contact = {
      ...insertContact,
      id,
      createdAt: /* @__PURE__ */ new Date()
    };
    this.contacts.set(id, contact);
    return contact;
  }
};
var DatabaseStorage = class {
  db;
  constructor() {
    if (!process.env.DATABASE_URL) {
      throw new Error("DATABASE_URL environment variable is required");
    }
    const sql = neon(process.env.DATABASE_URL);
    this.db = drizzle(sql);
  }
  async getUser(id) {
    const result = await this.db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }
  async getUserByUsername(username) {
    const result = await this.db.select().from(users).where(eq(users.username, username)).limit(1);
    return result[0];
  }
  async createUser(insertUser) {
    const result = await this.db.insert(users).values(insertUser).returning();
    return result[0];
  }
  async getMenuItems() {
    return await this.db.select().from(menuItems);
  }
  async getMenuItemsByCategory(category) {
    return await this.db.select().from(menuItems).where(eq(menuItems.category, category));
  }
  async createMenuItem(insertItem) {
    const result = await this.db.insert(menuItems).values(insertItem).returning();
    return result[0];
  }
  async getOrder(orderNumber) {
    const result = await this.db.select().from(orders).where(eq(orders.orderNumber, orderNumber)).limit(1);
    return result[0];
  }
  async createOrder(insertOrder) {
    const orderNumber = `BB${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
    const orderData = {
      ...insertOrder,
      orderNumber,
      status: insertOrder.status ?? "confirmed"
    };
    const result = await this.db.insert(orders).values(orderData).returning();
    return result[0];
  }
  async updateOrderStatus(orderNumber, status) {
    const result = await this.db.update(orders).set({ status, updatedAt: /* @__PURE__ */ new Date() }).where(eq(orders.orderNumber, orderNumber)).returning();
    return result[0];
  }
  async createContact(insertContact) {
    const result = await this.db.insert(contacts).values(insertContact).returning();
    return result[0];
  }
};
var storage = process.env.NODE_ENV === "production" ? new DatabaseStorage() : new MemStorage();

// server/routes.ts
async function registerRoutes(app2) {
  app2.get("/api/menu", async (req, res) => {
    try {
      const items = await storage.getMenuItems();
      res.json(items);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch menu items" });
    }
  });
  app2.get("/api/menu/:category", async (req, res) => {
    try {
      const { category } = req.params;
      const items = await storage.getMenuItemsByCategory(category);
      res.json(items);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch menu items by category" });
    }
  });
  app2.post("/api/orders", async (req, res) => {
    try {
      const validation = insertOrderSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({ message: "Invalid order data", errors: validation.error.errors });
      }
      const order = await storage.createOrder(validation.data);
      res.status(201).json(order);
    } catch (error) {
      res.status(500).json({ message: "Failed to create order" });
    }
  });
  app2.get("/api/orders/:orderNumber", async (req, res) => {
    try {
      const { orderNumber } = req.params;
      const order = await storage.getOrder(orderNumber);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }
      res.json(order);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch order" });
    }
  });
  app2.patch("/api/orders/:orderNumber/status", async (req, res) => {
    try {
      const { orderNumber } = req.params;
      const { status } = req.body;
      if (!status || typeof status !== "string") {
        return res.status(400).json({ message: "Valid status is required" });
      }
      const order = await storage.updateOrderStatus(orderNumber, status);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }
      res.json(order);
    } catch (error) {
      res.status(500).json({ message: "Failed to update order status" });
    }
  });
  app2.post("/api/contact", async (req, res) => {
    try {
      const validation = insertContactSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({ message: "Invalid contact data", errors: validation.error.errors });
      }
      const contact = await storage.createContact(validation.data);
      res.status(201).json(contact);
    } catch (error) {
      res.status(500).json({ message: "Failed to submit contact form" });
    }
  });
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
var vite_config_default = defineConfig({
  plugins: [
    react()
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true,
    sourcemap: false,
    minify: "esbuild",
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["react", "react-dom"],
          ui: ["@radix-ui/react-dialog", "@radix-ui/react-dropdown-menu", "@radix-ui/react-toast"]
        }
      }
    }
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"]
    }
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/index.ts
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path3 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path3.startsWith("/api")) {
      let logLine = `${req.method} ${path3} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = process.env.PORT || 5e3;
  server.listen(port, () => {
    log(`serving on port ${port}`);
  });
})();
